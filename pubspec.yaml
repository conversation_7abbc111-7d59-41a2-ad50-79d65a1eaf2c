name: niimbot_template
description: '精臣模板解析库'
version: 0.4.8+4
homepage: https://git.jc-ai.cn/print/foundation/niimbot_template

environment:
  sdk: ">=3.5.0 <4.0.0"
  flutter: ">=3.24.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  decimal: ^3.0.0
  flutter_canvas_plugins_interface: ^1.0.13
  netal_plugin: ^1.4.5+2
  niimbot_excel: ^1.1.2
  uuid: ^4.0.0
  excel: ^4.0.3
  archive: ^3.4.9
  crypto: ^3.0.3
  niimbot_intl: ^1.2.3
  collection: ^1.17.0
  path_provider: ^2.1.1
  path: ^1.8.2
  intl: any # 跟随FlutterSDK
  equatable: ^2.0.5
  # 图片处理、翻转、解码
  image: ^4.2.0
  isolate: ^2.1.1
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0

#dependency_overrides:
#  netal_plugin:
#    path: ../netal_plugin
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: any
  cider: ^0.2.8
  build_runner: ^2.4.13
  freezed: ^2.5.7
  json_serializable: ^6.8.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.


# To add assets to your package, add an assets section, like this:
# assets:
#   - images/a_dot_burr.jpeg
#   - images/a_dot_ham.jpeg
#
# For details regarding assets in packages, see
# https://flutter.dev/assets-and-images/#from-packages
#
# An image asset can refer to one or more resolution-specific "variants", see
# https://flutter.dev/assets-and-images/#resolution-aware

# To add custom fonts to your package, add a fonts section here,
# in this "flutter" section. Each entry in this list should have a
# "family" key with the font family name, and a "fonts" key with a
# list giving the asset and other descriptors for the font. For
# example:
# fonts:
#   - family: Schyler
#     fonts:
#       - asset: fonts/Schyler-Regular.ttf
#       - asset: fonts/Schyler-Italic.ttf
#         style: italic
#   - family: Trajan Pro
#     fonts:
#       - asset: fonts/TrajanPro.ttf
#       - asset: fonts/TrajanPro_Bold.ttf
#         weight: 700
#
# For details regarding fonts in packages, see
# https://flutter.dev/custom-fonts/#from-packages