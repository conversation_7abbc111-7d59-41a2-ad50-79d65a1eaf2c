import 'dart:ui';

extension RectExtension on Rect {
  Rect copyWith({
    double? left,
    double? top,
    double? right,
    double? bottom,
    double? width,
    double? height,
  }) {
    if (width != null || height != null) {
      return Rect.fromLTWH(
        left ?? this.left,
        top ?? this.top,
        width ?? this.width,
        height ?? this.height,
      );
    }
    return Rect.fromLTRB(
      left ?? this.left,
      top ?? this.top,
      right ?? this.right,
      bottom ?? this.bottom,
    );
  }
}
