import 'dart:ui';

import 'package:collection/collection.dart';
import 'package:netal_plugin/models/netal_barcode_element.dart';
import 'package:netal_plugin/models/netal_element_base.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/models/netal_qrcode_element.dart';
import 'package:netal_plugin/models/netal_text_element.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/graph_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/line_element.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/serial_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/template/template_enum.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/models/values/element_value.dart';
import 'package:niimbot_template/models/values/extensions/element_value.dart';
import 'package:niimbot_template/utils/element_utils.dart';
import 'package:niimbot_template/utils/string_utils.dart';
import 'package:niimbot_template/utils/template_data_source_utils.dart';

extension TemplateDataExtension on TemplateData {
  /// 模板大小
  Size get size => Size(width.toDouble(), height.toDouble());

  /// 模板中心
  Offset get center => size.center(Offset.zero);

  bool get needVip {
    return hasVipElement || vip;
  }

  bool get hasVipElement {
    bool hasVip = false;
    if (elements.isNotEmpty) {
      hasVip = elements.any((element) => element.hasVipSource);
    }
    return hasVip;
  }

  String get labelNameIntl {
    String info = "";
    String labelName = "";
    String labelEnName = "";
    String labelCNName = '';
    for (final labelNameInfo in labelNames) {
      String currentLocale = NiimbotIntl.getCurrentLocale().toString();
      if (currentLocale == 'zh') currentLocale = 'zh-cn';
      if (currentLocale == 'zh_Hant') currentLocale = 'zh-cn-t';
      if (currentLocale == 'pl') {
        currentLocale = 'Polish';
      } else if (currentLocale == 'id') {
        currentLocale = 'ind';
      } else if (currentLocale == 'nl') {
        currentLocale = 'af';
      } else if (currentLocale == 'cs') {
        currentLocale = 'Czech';
      }
      if (currentLocale == labelNameInfo.languageCode) {
        labelName = labelNameInfo.name;
      }
      if (labelNameInfo.languageCode == "en") {
        labelEnName = labelNameInfo.name;
      }
      if (labelNameInfo.languageCode == "zh-cn") {
        labelCNName = labelNameInfo.name;
      }
    }
    // 当前语言> EN > CN
    labelName = labelName.isEmpty
        ? (labelEnName.isEmpty ? labelCNName : labelEnName)
        : labelName;
    if (profile.extra.labelId?.isNotEmpty == true) {
      info = labelName;
    }
    return info;
  }

  String get localBackImageUrl {
    return localBackground.length > multipleBackIndex && multipleBackIndex >= 0
        ? localBackground[multipleBackIndex]
        : "";
  }

  /// 生成元素值 默认方式
  String _generateElementValue(
    BaseElement element,
    String id,
    String? value,
    String defaultValue, [
    int? page,
  ]) {
    ///判断是否为数据源数据
    if ((element is BindElement) &&
        (element.dataBind?.isNotEmpty ?? false) &&
        element.value != null) {
      final value = TemplateDataSourceUtils.getElementBindValue(
        element.id,
        element.value!,
        element.dataBind,
        dataSources,
        dataSourceModifies,
        page ?? dataSourceBindInfo?.page ?? 1,
      );
      return value;
    }
    final elementValue = values?.firstWhereOrNull((e) => e.elementId == id);
    if (elementValue != null) {
      return elementValue.buildValue(
        dataSources: dataSources,
        modify: dataSourceModifies,
        row: page ?? dataSourceBindInfo?.page ?? 1,
      );
    }
    if (value?.isNotEmpty ?? false) {
      return value!;
    }
    return defaultValue;
  }

  String _generateDateValue(DateElement element, [int? page]) {
    final elementValue = values?.firstWhereOrNull(
      (e) => e.elementId == element.id,
    );
    if (elementValue != null) {
      if (elementValue is DateValue) {
        return elementValue
            .copyWith(
              time: (element.isAssociate ? false : elementValue.time != null)
                  ? null
                  : element.isAssociate
                      ? ElementUtils.buildDateElementAssociateTime(
                          elementValue.time != null,
                          elementValue.time!,
                          element.validityPeriodUnit,
                          element.validityPeriod,
                        )
                      : elementValue.time,
            )
            .buildValue(
              dataSources: dataSources,
              modify: dataSourceModifies,
              row: page ?? dataSourceBindInfo?.page ?? 1,
            );
      }
      return elementValue.buildValue(
        dataSources: dataSources,
        modify: dataSourceModifies,
        row: page ?? dataSourceBindInfo?.page ?? 1,
      );
    }
    return ElementUtils.buildDateValue(
      time: (element.isAssociate ? false : element.dateIsRefresh)
          ? null
          : element.isAssociate
              ? ElementUtils.buildDateElementAssociateTime(
                  element.dateIsRefresh,
                  element.time,
                  element.validityPeriodUnit,
                  element.validityPeriod,
                )
              : element.time,
      dateFormat: element.dateFormat?.value ?? element.dateFormatCompat,
      timeFormat: element.timeFormat?.value,
      timeOffset: element.timeOffset,
      militaryTime: element.timeUnit == null,
      prefix: element.contentTitle,
    );
  }

  String _generateSerialValue(SerialElement element, [int? page]) {
    final elementValue = values?.firstWhereOrNull(
      (e) => e.elementId == element.id,
    );
    if (elementValue != null) {
      return elementValue.buildValue(
        dataSources: dataSources,
        modify: dataSourceModifies,
        row: page ?? dataSourceBindInfo?.page ?? 1,
      );
    }
    return ElementUtils.buildSerialValue(
      prefix: element.prefix,
      suffix: element.suffix,
      startNumber: element.startNumber,
      increment: element.incrementValue,
      index: page ?? dataSourceBindInfo?.page ?? 1,
      fixLength: element.fixLength,
      fixValue: element.fixValue,
    );
  }

  T coverElementToNetal<T extends NetalElementBase>(
    BaseElement element, [
    int? page,
  ]) {
    if (element is BarCodeElement) {
      return NetalBarcodeElement(
        x: element.x,
        y: element.y,
        rotate: element.rotate,
        colorChannel: element.colorChannel,
        elementColor: element.elementColor,
        width: element.width,
        height: element.height,
        value: StringUtils.covertNewLineChar(
          ElementUtils.fixBarCodeValueLength(
            _generateElementValue(
              element,
              element.id,
              element.value,
              BarCodeElement.defaultValue,
              page,
            ),
            element.codeType,
          ),
        ),
        fontSize: element.fontSize,
        textHeight: element.textHeight,
        textPosition: element.textPosition,
        codeType: element.codeType,
      ) as T;
    }
    if (element is DateElement) {
      return NetalTextElement(
        x: element.x,
        y: element.y,
        rotate: element.rotate,
        elementColor: element.elementColor,
        colorChannel: element.colorChannel,
        width: element.width,
        height: element.height,
        value: _generateDateValue(element, page),
        colorReverse: element.colorReverse,
        // 上层使用fontCode标记字体 ZT001表示默认字体即为Harmony
        fontFamily: element.fontCode == 'ZT001' ? 'Harmony' : element.fontCode,
        fontSize: element.fontSize,
        fontStyle: element.fontStyle,
        letterSpacing: element.letterSpacing,
        lineBreakMode: element.lineBreakMode,
        lineSpacing: element.lineSpacing,
        textAlignVertical: element.textAlignVertical,
        textAlignHorizontal: element.textAlignHorizontal,
        typesettingMode: element.typesettingMode,
        typesettingParam: element.typesettingParam,
        wordSpacing: element.wordSpacing,
        write_mode: element.write_mode,
        boxStyle: element.boxStyle,
        textStyle: element.textStyle,
      ) as T;
    }
    if (element is GraphElement) {
      return element.toNetal() as T;
    }
    if (element is MaterialElement) {
      return element.toNetal() as T;
    }
    if (element is ImageElement) {
      return element.toNetal() as T;
    }
    if (element is LineElement) {
      return element.toNetal() as T;
    }
    if (element is QRCodeElement) {
      return NetalQRCodeElement(
        x: element.x,
        y: element.y,
        rotate: element.rotate,
        elementColor: element.elementColor,
        colorChannel: element.colorChannel,
        width: element.codeType == NetalQRCodeType.PDF417 && element.width < 12
            ? 12
            : element.width,
        height: element.codeType == NetalQRCodeType.PDF417 ? 1 : element.height,
        correctLevel: element.codeType == NetalQRCodeType.PDF417
            ? null
            : element.correctLevel,
        codeType: element.codeType,
        value: StringUtils.covertNewLineChar(
          _generateElementValue(
            element,
            element.id,
            element.value,
            QRCodeElement.defaultValue,
            page,
          ),
        ),
      ) as T;
    }
    if (element is SerialElement) {
      return NetalTextElement(
        x: element.x,
        y: element.y,
        rotate: element.rotate,
        elementColor: element.elementColor,
        colorChannel: element.colorChannel,
        width: element.width,
        height: element.height,
        value: _generateSerialValue(element, page),
        colorReverse: element.colorReverse,
        fontFamily: element.fontCode == 'ZT001' ? 'Harmony' : element.fontCode,
        fontSize: element.fontSize,
        fontStyle: element.fontStyle,
        letterSpacing: element.letterSpacing,
        lineBreakMode: element.lineBreakMode,
        lineSpacing: element.lineSpacing,
        textAlignHorizontal: element.textAlignHorizontal,
        textAlignVertical: element.textAlignVertical,
        typesettingMode: element.typesettingMode,
        typesettingParam: element.typesettingParam,
        wordSpacing: element.wordSpacing,
        write_mode: element.write_mode,
        boxStyle: element.boxStyle,
        textStyle: element.textStyle,
      ) as T;
    }
    if (element is TableElement) {
      return element.toNetal() as T;
    }
    if (element is TextElement) {
      return NetalTextElement(
        x: element.x,
        y: element.y,
        rotate: element.rotate,
        elementColor: element.elementColor,
        colorChannel: element.colorChannel,
        width: element.width,
        height: element.height,
        value: StringUtils.covertNewLineChar(
          _generateElementValue(
            element,
            element.id,
            element.value,
            TextElement.defaultValue,
            page,
          ),
        ),
        colorReverse: element.colorReverse,
        // 上层使用fontCode标记字体 ZT001表示默认字体即为Harmony
        fontFamily: element.fontCode == 'ZT001' ? 'Harmony' : element.fontCode,
        fontSize: element.fontSize,
        fontStyle: element.fontStyle,
        letterSpacing: element.letterSpacing,
        lineBreakMode: element.lineBreakMode,
        lineSpacing: element.lineSpacing,
        textAlignVertical: element.textAlignVertical,
        textAlignHorizontal: element.textAlignHorizontal,
        typesettingMode: element.typesettingMode,
        typesettingParam: element.typesettingParam,
        wordSpacing: element.wordSpacing,
        write_mode: element.write_mode,
        boxStyle: element.boxStyle,
        textStyle: element.textStyle,
        lineMode: element.lineMode,
      ) as T;
    }
    return element as T;
  }

  /// 将元素转换成图像库元素
  List<NetalElementBase> covertElementsToNetal([int? page]) {
    List<NetalElementBase> netalElements = [];
    for (var element in elements) {
      final e = coverElementToNetal(element, page);
      netalElements.add(e);
      if (element.isOpenMirror) {
        /* 镜像元素 */
        final mirrorElement = ElementUtils.buildMirrorElement(
          element: element,
          templateSize: size,
        );
        netalElements.add(coverElementToNetal(mirrorElement, page));
      }
    }
    return netalElements;
  }

  /// 是否为线号机文件
  bool get isTubeFile =>
      templateAttributes?.contains(TemplateAttributes.TUBE_FILE) ?? false;
}
