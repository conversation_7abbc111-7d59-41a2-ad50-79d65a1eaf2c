import 'dart:convert';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:niimbot_excel/niimbot_excel.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/graph_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/line_element.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/serial_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/parse/parse_image_resource.dart';
import 'package:niimbot_template/models/parse/parse_resource.dart';
import 'package:niimbot_template/models/template/constants.dart';
import 'package:niimbot_template/models/template/file_data_source.dart';
import 'package:niimbot_template/models/template/label_name_info.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_info.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template/template_data_source_range.dart';
import 'package:niimbot_template/models/template/template_enum.dart';
import 'package:niimbot_template/models/template/template_excel.dart';
import 'package:niimbot_template/models/template/template_profile.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/models/values/element_value.dart';
import 'package:niimbot_template/utils/element_utils.dart';
import 'package:niimbot_template/utils/num_utils.dart';
import 'package:niimbot_template/utils/platform_utils.dart';
import 'package:niimbot_template/utils/template_data_source_utils.dart';
import 'package:niimbot_template/utils/template_excel_utils.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

import 'models/parse/parse_exception.dart';
import 'models/template/tube_file_setting.dart';

/// 解析 缓存本地图片资源
/// [url] 图片资源地址
typedef ParseLocalImageResources = Future<String?> Function(String url);

/// 解析 图片资源
/// [url] 图片资源地址
typedef ParseImageResources = Future<ParseImageResource?> Function(
  String url, {
  num? materialId,
  bool? existsSync,
});

/// 解析 字体资源
/// [fontCode] 字体编码
/// [fontFamily] 字体名称
typedef ParseFontResources = Future<ParseFontResource?> Function(
  String fontCode,
  String fontFamily,
  bool existsSync,
);

/// 解析 数据源资源
/// [url] 数据源地址
/// [hash] 数据源唯一标识
typedef ParseDataSourceResources = Future<(List<List<String>>, List<String>)>
    Function(String url, String hash);

/// 解析 数据源资源/老数据结构
typedef ParseExcelDataToDataSources = Future<TemplateDataSource?> Function(
    num id);

class TemplateParse {
  /// 模板解析 通过JSON字符
  static Future<TemplateData> parse(
    String templateJson, {
    ParseFontResources? parseFontResources,
    ParseImageResources? parseImageResources,
    ParseDataSourceResources? parseDataSourceResources,
    ParseExcelDataToDataSources? parseExcelDataToDataSources,
    ParseLocalImageResources? parseLocalImageResources,
  }) async {
    final data = json.decode(templateJson);
    return parseFromMap(
      data,
      parseFontResources: parseFontResources,
      parseImageResources: parseImageResources,
      parseDataSourceResources: parseDataSourceResources,
      parseExcelDataToDataSources: parseExcelDataToDataSources,
      parseLocalImageResources: parseLocalImageResources,
    );
  }

  /// 模板解析 通过JSON解析后的Map
  static Future<TemplateData> parseFromMap(
    Map<String, dynamic> data, {
    ParseFontResources? parseFontResources,
    ParseImageResources? parseImageResources,
    ParseDataSourceResources? parseDataSourceResources,
    ParseExcelDataToDataSources? parseExcelDataToDataSources,
    ParseLocalImageResources? parseLocalImageResources,
  }) async {
    final templateVersion = parseTemplateVersion(data['templateVersion']);
    final id = TemplateParseUtils.parseStringFromJSON(data['id']);
    final labelId = TemplateParseUtils.parseStringFromJSON(data['labelId']);
    final isCable = TemplateParseUtils.parseBoolFromJSON(data['isCable']);
    final consumableType =
        TemplateParseUtils.parseNumberFromJSON(data['consumableType'])?.toInt();
    final paperType =
        TemplateParseUtils.parseNumberFromJSON(data['paperType'])?.toInt();
    final profile = data['profile'] != null
        ? TemplateProfile.fromJson(data['profile'])
        : null;
    final commodityTemplate = TemplateParseUtils.parseBoolFromJSON(
      data['commodityTemplate'],
    );
    final paperColor = TemplateParseUtils.parseListFromJSON<String>(
      data['paperColor'],
    );

    final TemplateDataSourceModifies? dataSourceModifies = parseTemplateModify(
      data['dataSourceModifies'],
      data['task'],
      data['elements'],
    );
    final dataSources = await parseTemplateDataSource(
      data['dataSources'],
      data['externalData'],
      data['elements'],
      parseDataSourceResources,
      parseExcelDataToDataSources,
      dataSourceModifies,
      data['task']?['modifyMap'],
      data['currentPage'],
    );

    final elements = await parseTemplateElements(
      data['elements'],
      parseImageResources: parseImageResources,
      parseFontResources: parseFontResources,
    );
    final hasVipRes = TemplateParseUtils.parseBoolFromJSON(
      data['hasVIPRes'] ?? data['hasVipRes'],
    );
    final vip = TemplateParseUtils.parseBoolFromJSON(data['vip']);
    final cableDirection =
        TemplateParseUtils.parseTemplateCableDirectionFromJSON(
      data['cableDirection'],
    );
    final margin = TemplateParseUtils.parseListFromJSON<num>(data['margin']);
    final platformCode = TemplateParseUtils.parseTemplatePlatformCodeFromJSON(
          data['platformCode'],
        ) ??
        (PlatformUtils.isDesktop
            ? TemplatePlatformCode.CP001PC
            : TemplatePlatformCode.CP001Mobile);
    Map<String, String> usedFonts = (data['usedFonts'] == null)
        ? {"ZT001": "ZT001.ttf"}
        : (Map<String, String>.from(data['usedFonts'])).isEmpty
            ? {"ZT001": "ZT001.ttf"}
            : Map<String, String>.from(data['usedFonts']);
    final thumbnail = TemplateParseUtils.parseStringFromJSON(data['thumbnail']);
    final backgroundImage = TemplateParseUtils.parseStringFromJSON(
      data['backgroundImage'],
    );
    final localBackground = await parseTemplateLocalImage(
      backgroundImage,
      parseLocalImageResources: parseLocalImageResources,
    );
    final multipleBackIndex = TemplateParseUtils.parseNumberFromJSON(
          data['multipleBackIndex'],
        )?.toInt() ??
        0;
    final isEdited = TemplateEditStatus.byValue(
          TemplateParseUtils.parseNumberFromJSON(data['isEdited'])?.toInt() ??
              0,
        ) ??
        TemplateEditStatus.none;
    final (values, sortElements) =
        (data['values'] == null || data['values']!.isEmpty)
            ? (List<ElementValue>.of([]), elements)
            : parseValues(data['values'], elements);

    final templateAttributes = TemplateParseUtils.parseListFromJSON(
          data['templateAttributes'],
        )?.map((e) => TemplateAttributes.byValue(e)).nonNulls.toList() ??
        [];
    TubeFileSetting? tubeFileSetting = data['tubeFileSetting'] != null
        ? TubeFileSetting.fromJson(data['tubeFileSetting'])
        : null;
    if (tubeFileSetting != null && tubeFileSetting.align == null) {
      tubeFileSetting = tubeFileSetting.copyWith(
          align: elements.any((final v) =>
                  v is TextElement &&
                  v.textAlignHorizontal == NetalTextAlign.center)
              ? 'center'
              : 'left');
    }
    List<FileDataSource>? fileDataSources = await parseFileDataSource(
      data['fileDataSources'],
      parseLocalImageResources: parseLocalImageResources,
    );
    return TemplateData(
      id: id,
      labelId: labelId,
      cloudTemplateId: TemplateParseUtils.parseStringFromJSON(
        data['cloudTemplateId'],
      ),
      originTemplateId: TemplateParseUtils.parseStringFromJSON(
        data['originTemplateId'],
      ),
      name: data['name'],
      labelNames: TemplateParseUtils.parseListFromJSON(
            data['labelNames'],
          )?.map((e) => LabelNameInfo.fromJson(e)).toList() ??
          [],
      description: data['description'],
      thumbnail: thumbnail,
      backgroundImage: backgroundImage,
      multipleBackIndex: multipleBackIndex < 0 ? 0 : multipleBackIndex,
      width: data['width'] ?? 50,
      height: data['height'] ?? 30,
      rotate: data['rotate'],
      consumableType: consumableType,
      paperType: paperType,
      isCable: isCable,
      cableDirection: cableDirection,
      cableLength: data['cableLength'] ?? 0.0,
      margin: margin,
      profile: profile,
      platformCode: platformCode,
      accuracyName: data['paccuracyName'],
      commodityTemplate: commodityTemplate,
      canvasRotate: data['canvasRotate'],
      elements: sortElements,
      usedFonts: usedFonts,
      hasVipRes: hasVipRes,
      vip: vip,
      dataSources: dataSources,
      templateVersion: templateVersion,
      dataSourceModifies: dataSourceModifies,
      dataSourceBindInfo: parseTemplateDataSourceInfo(
        data['dataSourceBindInfo'],
        data['totalPage'],
        data['currentPage'],
      ),
      localBackground: localBackground,
      isEdited: isEdited,
      values: values,
      templateAttributes: templateAttributes,
      tubeFileSetting: tubeFileSetting,
      fileDataSources: fileDataSources,
    );
  }

  /// 解析本地背景图、预览图
  static Future<List<String>> parseTemplateLocalImage(
    String? urls, {
    ParseLocalImageResources? parseLocalImageResources,
  }) async {
    final List<String> res = [];
    if (urls != null) {
      if (urls.isNotEmpty) {
        final urlList = urls.split(',');
        for (String e in urlList) {
          final localPath = await parseLocalImageResources?.call(e);
          if (localPath != null) {
            res.add(localPath);
          }
        }
      }
    }
    return res;
  }

  /// 解析元素
  static Future<List<BaseElement>> parseTemplateElements(
    List? elements, {
    ParseFontResources? parseFontResources,
    ParseImageResources? parseImageResources,
  }) async {
    final List<BaseElement> res = [];
    if (elements != null) {
      BaseElement? $element;
      for (dynamic element in elements) {
        if (PlatformUtils.isDesktop) {
          /* 桌面端使用非镜像元素方案 所以不解析第二个镜像元素 */
          final mirrorElement = _isMirrorElement(element);
          if (mirrorElement) {
            /* 不解析镜像出来的元素 */
            continue;
          }
        }
        final type = element['type'];

        parseTemplateElementFixValue(element);
        if (type == 'barcode') {
          $element = (BarCodeElement.fromJson(element));
        }
        if (type == 'date') {
          await parseFontElementResource(element, parseFontResources);
          element['hasVipRes'] = (element['hasVipRes'] != true)
              ? TemplateParseUtils.parseBoolFromJSON(
                  element['dateIsRefresh'],
                )
              : element['hasVipRes'];
          if (element['value'] is int) element['value'] = '';
          if (element['dateChecked'] != null &&
              !TemplateParseUtils.parseBoolFromJSON(element['dateChecked'])) {
            element['dateFormat'] = null;
          }
          if (element['timeChecked'] != null &&
              !TemplateParseUtils.parseBoolFromJSON(element['timeChecked'])) {
            element['timeFormat'] = null;
          }
          final unix = TemplateParseUtils.parseNumberFromJSON(element['unix']);
          if (element['time'] == null && unix != null) {
            element['time'] = unix;
          }
          $element = (DateElement.fromJson(element));
        }
        if (type == 'graph') {
          $element = (GraphElement.fromJson(element));
        }
        if (type == 'image') {
          await parseImageElementResource(element, parseImageResources);
          final localImageUrl = element['localImageUrl'] as String?;
          final materialId =
              TemplateParseUtils.parseNumberFromJSON(element['materialId']) ??
                  0;
          if (localImageUrl?.isNotEmpty ?? false) {
            if (materialId != 0) {
              $element = (MaterialElement.fromJson(element));
            } else {
              $element = (ImageElement.fromJson(element));
            }
          }
        }
        if (type == 'line') {
          $element = (LineElement.fromJson(element));
        }
        if (type == 'qrcode') {
          $element = (QRCodeElement.fromJson(element));
        }
        if (type == 'serial') {
          await parseFontElementResource(element, parseFontResources);
          $element = (SerialElement.fromJson(element));
        }
        if (type == 'table') {
          if (element['cells'] is List) {
            for (dynamic cell in element['cells']) {
              await parseFontElementResource(cell, parseFontResources);
            }
          }
          if (element['combineCells'] is List) {
            for (dynamic cell in element['combineCells']) {
              await parseFontElementResource(cell, parseFontResources);
            }
          }

          $element = (TableElement.fromJson(element));
        }
        if (type == 'text') {
          await parseFontElementResource(element, parseFontResources);
          $element = (TextElement.fromJson(element));
        }
        if ($element != null) {
          final elementIds = res.map((e) => e.id).toList();
          if (elementIds.contains($element.id)) {
            /* 元素Id重复 重新生成Id */
            res.add($element.copyWith(id: ElementUtils.generateId()));
          } else {
            res.add($element);
          }
        }
      }
    }
    return res;
  }

  static bool _isMirrorElement(dynamic element) {
    final isOpenMirror = TemplateParseUtils.parseBoolFromJSON(
      element['isOpenMirror'],
    );
    final mirrorId = TemplateParseUtils.parseStringFromJSON(
      element['mirrorId'],
    );
    return (mirrorId?.isNotEmpty ?? false) && !isOpenMirror;
  }

  /// 解析图片资源
  static Future<void> parseImageElementResource(
    dynamic element,
    ParseImageResources? parseResources,
  ) async {
    final imageData = element['imageData'] as String?;
    final imageUrl = element['imageUrl'] as String?;
    final localImageUrl = element['localImageUrl'] as String?;

    /// 优先判断本地文件\不存在走imageData(base64)逻辑
    final materialId =
        TemplateParseUtils.parseNumberFromJSON(element['materialId']) ?? 0;
    final data = (imageData?.isNotEmpty ?? false)
        ? imageData!
        : ((imageUrl?.isNotEmpty ?? false) ? imageUrl! : '');
    bool existsSync = false;
    if ((localImageUrl?.isNotEmpty ?? false)) {
      File localImage = File(localImageUrl!);
      if (localImage.existsSync()) {
        existsSync = true;
      }
    }
    final parsed = await parseResources?.call(
      data,
      materialId: materialId != 0 ? materialId : null,
      existsSync: existsSync,
    );
    if (parsed != null) {
      final parsedLocalImageUrl = parsed.localImageUrl ?? localImageUrl ?? '';
      element['localImageUrl'] = parsedLocalImageUrl;
      element['hasVipRes'] =
          (element['hasVipRes'] ?? false) || (parsed.hasVipRes ?? false);
      if (parsedLocalImageUrl.isNotEmpty) element['imageData'] = '';
    }
  }

  /// 解析字体资源
  static Future<void> parseFontElementResource(
    dynamic element,
    ParseFontResources? parseResources,
  ) async {
    final fonts = Platform.environment.containsKey('FLUTTER_TEST')
        ? {}
        : NetalPlugin().getUsedFonts();
    if (element['fontCode']?.isEmpty ?? true) {
      /* 如果字体编码为空，则使用字体 */
      element['fontCode'] = element['fontFamily'];
    }
    bool existsSync = fonts.containsKey(element['fontCode']);
    final parsed = await parseResources?.call(
      element['fontCode'],
      element['fontFamily'],
      existsSync,
    );
    element['hasVipRes'] =
        (element['hasVipRes'] ?? false) || (parsed?.hasVipRes ?? false);
    if (fonts.containsKey(element['fontCode'])) {
      /* 本地已包含该字体 */
      return;
    }
    element['fontCode'] = parsed?.fontCode;
    element['fontFamily'] = parsed?.fontFamily;
  }

  /// 解析元素数值并修正
  static void parseTemplateElementFixValue(dynamic element) {
    final lineHeight = TemplateParseUtils.parseNumberFromJSON(
      element['lineHeight'],
    );
    final fontSize = TemplateParseUtils.parseNumberFromJSON(
      element['fontSize'],
    );
    final lineSpacing = TemplateParseUtils.parseNumberFromJSON(
      element['lineSpacing'],
    );
    final letterSpacing = TemplateParseUtils.parseNumberFromJSON(
      element['letterSpacing'],
    );
    final wordSpacing = TemplateParseUtils.parseNumberFromJSON(
      element['wordSpacing'],
    );
    final lineWidth = TemplateParseUtils.parseNumberFromJSON(
      element['lineWidth'],
    );
    final dateFormat = TemplateParseUtils.parseStringFromJSON(
      element['dateFormat'],
    );
    if (lineSpacing != null) {
      element['lineSpacing'] = NumberUtils.getCloseValue(
        TemplateConstants.FONT_LINE_SPACING,
        lineSpacing,
      );
    } else if (lineHeight != null && fontSize != null) {
      final spacing = (Decimal.parse(lineHeight.toString()) -
              Decimal.parse(fontSize.toString()))
          .toDouble();
      element['lineSpacing'] = NumberUtils.getCloseValue(
        TemplateConstants.FONT_LINE_SPACING,
        spacing,
      );
    }
    if (letterSpacing != null) {
      element['letterSpacing'] = NumberUtils.getCloseValue(
        TemplateConstants.FONT_WORD_SPACING,
        letterSpacing,
      );
    }
    if (wordSpacing != null) {
      element['wordSpacing'] = NumberUtils.getCloseValue(
        TemplateConstants.FONT_WORD_SPACING,
        wordSpacing,
      );
    }
    if (fontSize != null) {
      element['fontSize'] = NumberUtils.getCloseValue(
        TemplateConstants.FONT_SIZE_LIST.map((e) => e.mm).toList(),
        fontSize,
      );
    }
    if (lineWidth != null) {
      element['lineWidth'] = NumberUtils.getCloseValue(
        TemplateConstants.LINE_WIDTH,
        lineWidth,
      );
    }
    if (dateFormat != null) {
      if (dateFormat.contains(' ') &&
          dateFormat != 'MMMM yyyy' &&
          (dateFormat.contains('dd,') || dateFormat.contains(' yyyy'))) {
        element['dateFormat'] =
            dateFormat.replaceAll('dd', 'd').replaceAll(' yyyy', ',yyyy');
      }
    }
  }

  /// 解析元素绑定值
  static void parseTemplateElementBindValue(
    dynamic element,
    List<String>? dataBind,
    TemplateDataSourceModifies? dataSourceModifies,
    Map<String, dynamic>? modifyMap,
    int? currentPage,
  ) {
    final value = TemplateParseUtils.parseStringFromJSON(element['value']);
    if (value != null) {
      if (element['binding'] != null &&
          element['binding'] == true &&
          element['uid'] != null &&
          (dataSourceModifies ?? {}).keys.contains(element['uid'])) {
        if (modifyMap != null && modifyMap.isNotEmpty) {
          final columnIndex = modifyMap[element['uid']]?['1']?['column'];
          if (columnIndex != null) {
            final bindValue = NiimbotExcelUtils.indexToLetters(columnIndex + 1);
            element['value'] = '$bindValue${currentPage ?? 1}';
            element['dataBind'] = dataBind;
          }
        }
      } else {
        final bindValue = NiimbotExcelUtils.getElementBindIndex(value);
        if (bindValue != null && dataBind != null) {
          element['value'] = bindValue;
          element['dataBind'] = dataBind;
        }
      }
    }
  }

  /// 解析数据源
  static Future<List<TemplateDataSource>?> parseTemplateDataSource(
    List? dataSource,
    Map<String, dynamic>? externalData,
    List? elements,
    ParseDataSourceResources? parseDataSourceResources,
    ParseExcelDataToDataSources? parseExcelDataToDataSources,
    TemplateDataSourceModifies? dataSourceModifies,
    Map<String, dynamic>? modifyMap,
    int? currentPage,
  ) async {
    if (dataSource != null && dataSource.isNotEmpty) {
      final List<TemplateDataSource> list = [];
      for (final e in dataSource) {
        final result =
            await parseDataSourceResources?.call(e['uri'], e['hash']);
        list.add(
          TemplateDataSource(
            type: TemplateDataSourceType.excel,
            uri: e['uri'],
            hash: e['hash'],
            name: e['name'],
            headers: TemplateParseUtils.parseTemplateDataSourceHeaderFromJSON(
              e['headers'],
            ),
            rowData: result?.$1 ?? [],
            range: (e['range'] ?? [])
                .map<TemplateDataSourceRange>(
                  (e) => e is TemplateDataSourceRange
                      ? e
                      : TemplateDataSourceRange.fromJson(e),
                )
                .toList(),
            collections: (result?.$2 ?? []).isNotEmpty
                ? (result?.$2 ?? [])
                : (e['collections'] ?? [])
                        ?.map<String>((e) => e.toString())
                        .toList() ??
                    [],
          ),
        );
      }
      return list;
    }
    if (externalData != null && externalData.isNotEmpty) {
      final excelData = TemplateExcel.fromJson(externalData);
      TemplateDataSource? dataSource =
          await TemplateExcelUtils.covertExcelDataToDataSource(
        excelData,
        parseExcelDataToDataSources,
      );
      if (elements != null && dataSource != null) {
        String filePath =
            await TemplateDataSourceUtils.buildLocalDataSourcePath(
          dataSource.hash,
        );
        List<String> sheets = [];
        try {
          sheets = NiimbotExcelUtils.getSheets(filePath);
        } catch (e) {
          NiimbotExcel.freedExcel();
        }
        // List<String> sheets = NiimbotExcelUtils.getSheets(filePath);
        String sheetName;
        if (sheets.isEmpty) {
          sheetName = "Sheet1";
        } else {
          sheetName = sheets.first;
        }
        for (var element in elements) {
          parseTemplateElementBindValue(
            element,
            [dataSource.hash, sheetName],
            dataSourceModifies,
            modifyMap,
            currentPage,
          );
          final type = element['type'];
          if (type == 'table') {
            if (element['cells'] is List) {
              element['cells'].forEach((cell) {
                parseTemplateElementBindValue(
                  cell,
                  [dataSource.hash, sheetName],
                  dataSourceModifies,
                  modifyMap,
                  currentPage,
                );
              });
            }
            if (element['combineCells'] is List) {
              element['combineCells'].forEach((cell) {
                parseTemplateElementBindValue(
                  cell,
                  [dataSource.hash, sheetName],
                  dataSourceModifies,
                  modifyMap,
                  currentPage,
                );
              });
            }
          }
        }
        return [dataSource];
      }
    }
    return null;
  }

  /// 解析数据源修改标记
  static TemplateDataSourceModifies? parseTemplateModify(
    dynamic modify,
    dynamic task,
    List? elements,
  ) {
    if (modify != null) {
      return (modify as Map).map(
        (key, value) => MapEntry(
          key,
          (value as Map).map(
            (k, v) => MapEntry(k, TemplateDataSourceModify.fromJson(v)),
          ),
        ),
      );
    }
    TemplateDataSourceModifies templateModify = {};
    elements?.forEach((e) {
      String? contentTitle = e['contentTitle'];
      final eId = TemplateParseUtils.parseStringFromJSON(e['id']);
      if (contentTitle != null &&
              NiimbotExcelUtils.getElementBindIndex(e?['value'] ?? '') !=
                  null ||
          e['binding'] == true) {
        if (eId != null) {
          templateModify[eId] = {
            '0': TemplateDataSourceModify(
              useTitle: contentTitle?.isNotEmpty,
              title: contentTitle,
              delimiter: e['delimiter'],
            ),
          };
        }
      }
    });
    if (task != null) {
      final modifyData = task['modifyData'];
      if (modifyData is Map && modifyData.isNotEmpty) {
        modifyData.forEach((eId, valueMap) {
          valueMap.forEach((row, value) {
            final _modify = templateModify[eId]?['0'] ??
                TemplateDataSourceModify(value: value);
            if (templateModify[eId] != null) {
              templateModify[eId]![row] = _modify.copyWith(value: value);
            } else {
              templateModify[eId] = {row: _modify.copyWith(value: value)};
            }
          });
        });
      }
      final modifyMap = task['modifyMap'];
      if (modifyMap is Map && modifyMap.isNotEmpty) {
        modifyMap.forEach((eId, valueMap) {
          valueMap.forEach((row, value) {
            TemplateDataSourceModify _modify = TemplateDataSourceModify(
              // value: row == 'global' ? value['value'] : null,
              useTitle: value['useTitle'],
              delimiter: value['delimiter'],
              title: value['title'],
              prefix: value['prefix'],
              suffix: value['suffix'],
            );
            if (row == 'global' || row == '1') {
              /* 全局绑定 */
              if (templateModify[eId] != null) {
                templateModify[eId]!['0'] = _modify;
              } else {
                templateModify[eId] = {'0': _modify};
              }
            } else {
              /* 非全局绑定 */
              var rowKey = row;
              _modify = _modify.copyWith(value: value['value']);
              if (templateModify[eId] != null) {
                templateModify[eId]![rowKey] = _modify;
              } else {
                templateModify[eId] = {rowKey: _modify};
              }
            }
          });
        });
      }
    }
    if (templateModify.isNotEmpty) {
      return templateModify;
    }
    return null;
  }

  /// 解析模板数据源信息
  static TemplateDataSourceInfo? parseTemplateDataSourceInfo(
    dynamic dataSourceBindInfo,
    dynamic totalPage,
    dynamic currentPage,
  ) {
    if (dataSourceBindInfo != null) {
      return TemplateDataSourceInfo.fromJson(dataSourceBindInfo);
    }
    final total = TemplateParseUtils.parseNumberFromJSON(totalPage);
    final page = TemplateParseUtils.parseNumberFromJSON(currentPage);
    if (total != null && page != null) {
      return TemplateDataSourceInfo(
        total: total < 0 ? 0 : total.toInt(),
        page: page < 1 ? 1 : page.toInt(),
      );
    }
    return null;
  }

  /// 解析模板版本号
  static String parseTemplateVersion(String? version) {
    final versionData = ['1.7.0.3', '1.7.0.4'];
    if (version?.isNotEmpty ?? false) {
      if (versionData.contains(version)) {
        return version!;
      }
      final verList = version!.split('.');
      if (verList.length == 4) {
        final supperVer = TemplateConstants.SUPPORT_TEMPLATE_VERSION.split('.');
        for (var i = 0; i < supperVer.length; i++) {
          if (i == 2) continue;
          final ver = int.tryParse(verList[i]);
          final sVer = int.tryParse(supperVer[i]);
          if (ver != null && sVer != null) {
            if (ver > sVer) {
              throw TemplateParseException(
                TemplateParseExceptionCode.versionNotSupport,
              );
            } else if (ver < sVer) {
              break;
            }
          }
        }
        return version;
      }
    }
    return TemplateConstants.SAVE_TEMPLATE_VERSION;
  }

  /// 解析模板值
  static (List<ElementValue>, List<BaseElement>) parseValues(
    dynamic values,
    List<BaseElement> elements,
  ) {
    final List<ElementValue> result = [];
    final list = TemplateParseUtils.parseListFromJSON(
          values,
        )?.map((e) => ElementValue.fromJson(e)).toList() ??
        [];
    final List<BaseElement> sortElements = [];
    for (final value in list) {
      final element = elements.firstWhereOrNull((e) => e.id == value.elementId);

      /// 一级能匹配到元素
      if (element != null) {
        result.add(value);
        sortElements.add(element);
      } else {
        /// 复合值类型
        if (value is CompositeValue) {
          if (value.valueObjects.length > 1) {
            /// 多行组合值 不支持
            throw TemplateParseException(
              TemplateParseExceptionCode.valuesNotSupport,
            );
          }
          final paragraph = value.valueObjects.firstOrNull;
          if (paragraph != null && paragraph is CompositeValue) {
            final element = elements.firstWhereOrNull(
              (e) => e.id == paragraph.elementId,
            );
            if (element != null) {
              result.add(paragraph.copyWith(repeatCount: value.repeatCount));
              sortElements.add(element);
            }
          }
        }
      }
    }
    return (result, sortElements);
  }

  /// 解析文件数据源
  static Future<List<FileDataSource>?> parseFileDataSource(
    dynamic dataSource, {
    ParseLocalImageResources? parseLocalImageResources,
  }) async {
    if (dataSource == null) {
      return null;
    }
    final List<FileDataSource> result = [];
    for (var e in (TemplateParseUtils.parseListFromJSON(dataSource) ?? [])) {
      final data = FileDataSource.fromJson(e);
      if (data.imageDir?.isNotEmpty == true &&
          parseLocalImageResources != null) {
        final List<String> pageImages = [];
        for (var path in data.pageImages) {
          final res = await parseLocalImageResources('${data.imageDir!}$path');
          pageImages.add(res!);
        }
        result.add(data.copyWith(
            pageImages: pageImages, imageDir: const CopyWrapper.value(null)));
      } else {
        result.add(data);
      }
    }
    return result.isNotEmpty ? result : null;
  }
}
