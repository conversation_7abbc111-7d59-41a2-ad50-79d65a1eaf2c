import 'dart:io';

import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart';
import 'package:collection/collection.dart';
import 'package:excel/excel.dart';

import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template/template_data_source_range.dart';
import 'package:niimbot_template/models/template/template_enum.dart';

class TemplateDataSourceUtils {
  /// 构建本地数据源路径
  /// [hash] 数据源唯一标识
  static Future<String> buildLocalDataSourcePath(String hash) async {
    var directory = await getApplicationDocumentsDirectory();
    var suffix = join('dataSource', '$hash.tempFile');
    if (Platform.isWindows) {
      return join(directory.path, 'JCPrintClient', suffix);
    }
    return join(directory.path, suffix);
  }

  /// 重新生成单元格索引值
  /// [value] 元素绑定的索引值
  /// [row] 指定行 从1开始
  /// [headerInfo] 表头信息
  static CellIndex? _rebuildValueIndex(
    String value,
    int row,
    dynamic headerInfo,
    List<TemplateDataSourceRange>? range,
  ) {
    var val = CellIndex.indexByString(value);
    if (range != null && range.isNotEmpty) {
      /* 当存在指定范围 */
      var realIndex = getRangeIndex(rangeMerge(range), row - 1);
      if (realIndex != null) {
        return CellIndex.indexByColumnRow(
          columnIndex: val.columnIndex,
          rowIndex: realIndex,
        );
      }
    } else {
      /* 需要根据行 重新生成单元格索引  */
      if (headerInfo != null && headerInfo is int) {
        /* 存在表头信息 且表头指定的是行数 */
        if (row >= headerInfo) {
          /* 当前行大于等于表头行 则行数加1 */
          return CellIndex.indexByColumnRow(
            columnIndex: val.columnIndex,
            rowIndex: row + 1,
          );
        }
      }
      return CellIndex.indexByColumnRow(
        columnIndex: val.columnIndex,
        rowIndex: row,
      );
    }
    return null;
  }

  /// 获取数据绑定的内容
  /// [eId] 元素绑定值
  /// [value] 元素绑定索引
  /// [dataBind] 元素绑定信息
  /// [dataSources] 模板中的数据源
  /// [modify] 模板修改信息
  /// [row] 指定行 如果指定 则根据指定行 重新生成单元格索引值
  static String getElementBindValue(
    String eId,
    String value,
    List<String>? dataBind,
    List<TemplateDataSource>? dataSources,
    TemplateDataSourceModifies? modify,
    int row,
  ) {
    if (dataBind != null && dataBind.isNotEmpty) {
      var dataSource = dataSources?.firstWhereOrNull(
        (e) => e.hash == dataBind[0],
      );
      if (dataSource != null) {
        /* 能找到数据源 */
        var tableName = dataBind[1];
        var headers = dataSource.headers?[tableName];
        int? headerInfoInt = headers is int ? headers : null;
        List<String>? headerInfoList =
            headers is List ? headers.map((e) => e.toString()).toList() : null;
        var headerInfo = headerInfoInt ?? headerInfoList;
        var valueIndex = _rebuildValueIndex(
          value,
          row,
          headerInfo,
          dataSource.range,
        );
        if (valueIndex != null) {
          /* 在范围内 */
          final cellValue = _buildCellValue(
            eId: eId,
            dataSource: dataSource,
            headerInfo: headerInfo,
            tableName: tableName,
            valueIndex: valueIndex,
            modify: modify,
          );
          if (cellValue != null) {
            return cellValue;
          }
        }
      }
    }
    return value;
  }

  /// 生成单元格数据
  static String? _buildCellValue({
    required String eId,
    required TemplateDataSource dataSource,
    dynamic headerInfo,
    required String tableName,
    required CellIndex valueIndex,
    TemplateDataSourceModifies? modify,
  }) {
    /* 在范围内 */
    List<String> headers = [];
    String? cell;
    if (dataSource.type == TemplateDataSourceType.excel) {
      /* 解析Excel类型数据 */
      if (headerInfo != null) {
        if (headerInfo is int) {
          headers = dataSource.rowData[headerInfo - 1];
        } else {
          headers = headerInfo;
        }
      }
      cell =
          dataSource.rowData
              .elementAtOrNull(valueIndex.rowIndex)
              ?.elementAtOrNull(valueIndex.columnIndex) ??
          '';
    }
    if (cell != null) {
      if (modify != null) {
        TemplateDataSourceModify? globalModify = modify[eId]?['0'];
        TemplateDataSourceModify? rowModify =
            modify[eId]?[(valueIndex.rowIndex + 1).toString()];
        bool useTitle = rowModify?.useTitle ?? globalModify?.useTitle ?? false;
        String delimiter =
            rowModify?.delimiter ??
            globalModify?.delimiter ??
            NiimbotIntl.getIntlMessage('app100001507', ':');
        String title =
            rowModify?.title ??
            globalModify?.title ??
            headers.elementAtOrNull(valueIndex.columnIndex) ??
            "";
        String fullTitle =
            useTitle
                ? title.isEmpty
                    ? ""
                    : "$title$delimiter"
                : "";
        String prefix = rowModify?.prefix ?? globalModify?.prefix ?? "";
        String val = rowModify?.value ?? globalModify?.value ?? cell;
        String suffix = rowModify?.suffix ?? globalModify?.suffix ?? "";
        return "$fullTitle$prefix$val$suffix";
      }
      return cell;
    }
    return null;
  }
}
