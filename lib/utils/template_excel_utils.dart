import 'dart:io';

import 'package:collection/collection.dart';
import 'package:excel/excel.dart';
import 'package:archive/archive.dart';
import 'package:crypto/crypto.dart';
import 'package:niimbot_excel/niimbot_excel.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';

import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_enum.dart';
import 'package:niimbot_template/models/template/template_excel.dart';
import 'package:niimbot_template/models/template/template_excel_data.dart';
import 'package:niimbot_template/template_parse.dart';
import 'package:niimbot_template/utils/file_utils.dart';
import 'package:niimbot_template/utils/template_data_source_utils.dart';

class TemplateExcelUtils {
  /// 构建Excel数据
  static Future<List<int>?> _buildExcelFile(TemplateExcelData excelData) async {
    final excel = Excel.createExcel();
    excel.rename('Sheet1', excelData.name);
    excel.insertRowIterables(excelData.name,
        excelData.data.columnHeaders.map((e) => TextCellValue(e)).toList(), 0);
    int maxColumnLength = excelData.data.columns.firstOrNull?.length ?? 0;
    for (int i = 0; i < excelData.data.columns.length; i++) {
      int columnLength = excelData.data.columns[i].length;
      if (columnLength > maxColumnLength) {
        maxColumnLength = columnLength;
      }
    }
    for (int i = 0; i < maxColumnLength; i++) {
      final row = excelData.data.columns.map((e) {
        var length = e.length;
        return TextCellValue(i < length ? e[i] : "");
      }).toList();
      excel.insertRowIterables(excelData.name, row, i + 1);
    }
    return excel.save();
  }

  /// 生成数据源 通过Excel数据
  static Future<TemplateDataSource?> _buildDataSourceByExcelData(
      TemplateExcelData excelData,
      String fileName,
      String sheetName,
      List<int>? fileBytes,
      String hashStr) async {
    if (fileBytes != null) {
      return buildDataSourceByExcel(
        fileBytes,
        fileName,
        sheetName,
        {excelData.name: 1}, //默认首行为表头
        hashStr,
      );
    }
    return null;
  }

  /// 根据Excel文件生成数据源
  /// [fileBytes] 文件字节码
  /// [fileName] 文件名
  /// [headers] 表头信息
  /// [uploadFile] 上传excel文件
  static Future<TemplateDataSource?> buildDataSourceByExcel(
      List<int> fileBytes,
      String fileName,
      String sheetName,
      TemplateDataSourceHeaders? headers,
      String contentHash) async {
    var path =
        await TemplateDataSourceUtils.buildLocalDataSourcePath(contentHash);
    /* 生成本地数据源文件 */
    File(path)
      ..createSync(recursive: true)
      ..writeAsBytesSync(fileBytes);
    if (path.isNotEmpty) {
      List<List<String>> rowData =
          NiimbotExcelUtils.getContent(path, sheetName).toList(growable: true);
      return TemplateDataSource(
        type: TemplateDataSourceType.excel,
        uri: path,
        hash: contentHash,
        name: fileName,
        headers: headers,
        rowData: rowData,
        range: [],
        collections: [],
      );
    }
    return null;
  }

  /// 模板中excel数据转换
  /// [excelData] 源JSON模板中的excel数据
  /// [parseExcelDataToDataSources] 通过excel云文件id获取云文件数据
  static Future<TemplateDataSource?> covertExcelDataToDataSource(
    TemplateExcel excelData,
    ParseExcelDataToDataSources? parseExcelDataToDataSources,
  ) async {
    final excel = excelData.list.firstOrNull;
    if (excel == null) return null;
    List<int>? fileBytes = await _buildExcelFile(excel);
    String fileHash = "";
    if (fileBytes != null) {
      List<int> result = [];
      var archive = ZipDecoder().decodeBytes(fileBytes);
      for (var element in archive.files) {
        result.addAll(element.content as List<int>);
      }
      fileHash = md5.convert(result).toString();
    }
    if (excelData.id != null) {
      final dataSource = await parseExcelDataToDataSources?.call(excelData.id!);
      if (dataSource != null) {
        return dataSource;
      }
    }
    String sheetName = excel.name.isNotEmpty ? excel.name : 'Sheet1';
    return _buildDataSourceByExcelData(
        excelData.list[0], excelData.fileName, sheetName, fileBytes, fileHash);
  }

  /// 获取不带后缀名的文件名称
  static String getFileNameWithoutExtension(path) {
    Uri fileUri = Uri.parse(path);
    String fileNameWithExtension = fileUri.pathSegments.last;
    int dotIndex = fileNameWithExtension.lastIndexOf('.');
    String fileNameWithoutExtension = dotIndex == -1
        ? fileNameWithExtension // 如果没有找到'.'，则文件名就是完整的文件名
        : fileNameWithExtension.substring(0, dotIndex); // 否则去掉扩展名
    return fileNameWithoutExtension;
  }

  static buildDataSourceByPath(String path,
      {String? uri, String? fileName, required String hash}) async {
    if (path.isNotEmpty) {
      var sheetName = 'Sheet1';
      var sheets = <String>[];
      try {
        sheets = NiimbotExcelUtils.getSheets(path);
        sheetName = sheets.firstOrNull ?? "Sheet1";
      } catch (e) {
        NiimbotExcel.freedExcel();
      }
      // var sheets = NiimbotExcelUtils.getSheets(path);
      // String sheetName = sheets.firstOrNull ?? "Sheet1";
      String excelName =
          fileName ?? FileUtils.getFileNameWithoutExtension(path);
      List<List<String>> rowData =
          NiimbotExcelUtils.getContent(path, sheetName).toList(growable: true);
      final dataSource = TemplateDataSource(
        type: TemplateDataSourceType.excel,
        uri: uri ?? path,
        hash: hash,
        name: excelName,
        collections: sheets,
        headers: {sheetName: 1},
        rowData: rowData,
        range: [],
      );

      return dataSource;
    }
  }
}
