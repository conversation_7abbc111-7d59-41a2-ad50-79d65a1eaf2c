import 'package:niimbot_template/utils/string_utils.dart';

class FileUtils{
  /// 获取不带后缀名的文件名称
  static String getFileNameWithoutExtension(path) {
    Uri fileUri = Uri.parse(path);
    String fileNameWithExtension = fileUri.pathSegments.last;
    int dotIndex = fileNameWithExtension.lastIndexOf('.');
    String fileNameWithoutExtension = dotIndex == -1
        ? fileNameWithExtension // 如果没有找到'.'，则文件名就是完整的文件名
        : fileNameWithExtension.substring(0, dotIndex); // 否则去掉扩展名
    return fileNameWithoutExtension;
  }

  /// 获取文件目录和文件名列表 仅处理网络地址
  static (String, List<String>)? extractUrls(List<String> urls) {
    if (urls.isEmpty) return null;

    // 从第一个URL中提取目录路径
    final firstUrl = urls.first;
    if (!StringUtils.isNetUrl(firstUrl)) {
      return null;
    }
    final lastSlashIndex = firstUrl.lastIndexOf('/');
    final dir = firstUrl.substring(0, lastSlashIndex + 1);

    // 提取所有文件名
    final newUrls = urls.map((url) {
      final lastSlash = url.lastIndexOf('/');
      return url.substring(lastSlash + 1);
    }).toList();

    return (dir, newUrls);
  }
}