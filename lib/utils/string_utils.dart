import 'package:flutter/material.dart';

class StringUtils {
  static bool isEmpty(String s) => s.trim().isEmpty;

  static bool isNotEmpty(String s) => !isEmpty(s);

  static bool isEmail(String value) {
    if (isEmpty(value)) return false;
    return RegExp(
            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(value);
  }

  // Measures text using an off-screen canvas. It's not fast, but not overly slow either. Use with (mild) caution :)
  static Size measure(
    String text,
    TextStyle style, {
    int maxLines = 1,
    TextDirection direction = TextDirection.ltr,
    double? maxWidth,
  }) {
    final TextPainter textPainter = TextPainter(
        text: TextSpan(text: text, style: style),
        maxLines: maxLines,
        textDirection: direction)
      ..layout(minWidth: 0, maxWidth: maxWidth ?? double.infinity);
    return textPainter.size;
  }

  // Measures longest text item in a list of Strings. Useful for things like Dropdown Menu, where you just want to take up as much space as the content requires.
  static double measureLongest(List<String> items, TextStyle style,
      [int? maxItems]) {
    double l = 0;
    if (maxItems != null && maxItems < items.length) {
      items.length = maxItems;
    }
    for (var item in items) {
      double m = measure(item, style).width;
      if (m > l) l = m;
    }
    return l;
  }

  /// Gracefully handles null values, and skips the suffix when null
  static String safeGet(String value, [String? suffix]) {
    return (value ?? "") + (!isEmpty(value) ? suffix ?? "" : "");
  }

  static String pluralize(String s, int length) {
    if (length == 1) return s;
    return "${s}s";
  }

  static String titleCaseSingle(String s) =>
      '${s[0].toUpperCase()}${s.substring(1)}';

  static String titleCase(String s) =>
      s.split(" ").map(titleCaseSingle).join(" ");

  static String defaultOnEmpty(String value, String fallback) =>
      isEmpty(value) ? fallback : value;

  static int perLineWords(String text, double width, double fontSize) {
    String cpText = text.substring(0, 1);
    final TextPainter textPainter = TextPainter(
        text: TextSpan(text: cpText, style: TextStyle(fontSize: fontSize)),
        textDirection: TextDirection.ltr)
      ..layout();
    Size textPSize = textPainter.size;
    int wordSizePerLine = (width / (textPSize.width)).truncate();
    return wordSizePerLine;
  }

  static double wordsHeight(String text, double fontSize) {
    String cpText = text.substring(0, 1);
    final TextPainter textPainter = TextPainter(
        text: TextSpan(text: cpText, style: TextStyle(fontSize: fontSize)),
        textDirection: TextDirection.ltr)
      ..layout();
    Size textPSize = textPainter.size;
    return textPSize.height;
  }

  static String addCharAtPosition(String s, String char, int position,
      {bool repeat = false}) {
    if (!repeat) {
      if (s.length < position) {
        return s;
      }
      var before = s.substring(0, position);
      var after = s.substring(position, s.length);
      return before + char + after;
    } else {
      if (position == 0) {
        return s;
      }
      var buffer = StringBuffer();
      for (var i = 0; i < s.length; i++) {
        if (i != 0 && i % position == 0) {
          buffer.write(char);
        }
        buffer.write(String.fromCharCode(s.runes.elementAt(i)));
      }
      return buffer.toString();
    }
  }

  /// 处理换行符
  static String covertNewLineChar(String str) {
    return str.replaceAll('\r\n', '\n').replaceAll('\r', '\n');
  }

  static String padStart(String str, int maxLength, String padding) {
    final diff = maxLength - str.length;
    if (diff > 0) {
      final res = '${padding * diff}$str';
      return res.substring(res.length - maxLength, res.length);
    }
    return str;
  }

  /// 是否为网络路径 http(s)协议
  static bool isNetUrl(String url) {
    final uri = Uri.tryParse(url);
    return uri?.scheme.startsWith('http') ?? false;
  }
}
