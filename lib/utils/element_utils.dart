import 'dart:math';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/extensions/rect.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/utils/string_utils.dart';
import 'package:uuid/uuid.dart';

class ElementUtils {
  /// 生成 id
  static String generateId() {
    return const Uuid().v4().replaceAll("-", "");
  }

  /// 解析颜色
  static Color parseColorFromStr(String color) {
    List<int> elementColor = color.split(".").map((e) => int.parse(e)).toList();
    if (elementColor.length == 4) {
      return Color.fromARGB(
        elementColor[0],
        elementColor[1],
        elementColor[2],
        elementColor[3],
      );
    } else if (elementColor.length == 3) {
      return Color.fromARGB(
        255,
        elementColor[0],
        elementColor[1],
        elementColor[2],
      );
    }
    return Colors.black;
  }

  ///处理默认字体
  static void completionFontDefault(Map<String, dynamic> data) {
    final fontCode = data['fontCode'] as String?;
    // if (fontCode != null && fontCode.isEmpty) {
    //   final languageType =
    //       CanvasPluginManager().hostMethodImpl?.getCurrentLanguageType();
    //   if (languageType == "ar") {
    //     data['fontCode'] = FontManager.DEFAULT_FONT_CODE_ARIAL;
    //     data['fontFamily'] = FontManager.DEFAULT_FONT_CODE_ARIAL;
    //   } else {
    //     data['fontCode'] = FontManager.DEFAULT_FONT_CODE;
    //     data['fontFamily'] = FontManager.DEFAULT_FONT_CODE;
    //   }
    // }
  }

  /// 生成镜像元素
  static T buildMirrorElement<T extends BaseElement>({
    required T element,
    required Size templateSize,
  }) {
    /// 模板中心点
    Offset templateCenter = templateSize.center(Offset.zero);
    Offset mirrorPosition = templateCenter * 2 -
        Offset(
          element.x.toDouble() + element.width,
          element.y.toDouble() + element.height,
        );

    final rotate = (element.rotate + 180) % 360;
    if (element.mirrorType == ElementMirrorType.canvasCenter) {
      /// 画板中心点镜像
      return element.copyWith(
        x: mirrorPosition.dx,
        y: mirrorPosition.dy,
        rotate: rotate,
      ) as T;
    } else {
      if (element.mirrorType == ElementMirrorType.canvasCenterX) {
        /// 画板中心 x 轴镜像
        return element.copyWith(x: mirrorPosition.dx, rotate: rotate) as T;
      } else {
        /// 画板中心 y 轴镜像
        return element.copyWith(y: mirrorPosition.dy, rotate: rotate) as T;
      }
    }
  }

  /// 构建镜像矩形
  static Rect buildMirrorRect({
    required Rect rect,
    required ElementMirrorType mirrorType,
    required Size templateSize,
  }) {
    /// 镜像点
    final mirrorPosition =
        templateSize.bottomRight(Offset.zero) - rect.bottomRight;
    if (mirrorType == ElementMirrorType.canvasCenter) {
      /// 画板中心点镜像
      return rect.copyWith(left: mirrorPosition.dx, top: mirrorPosition.dy);
    } else {
      if (mirrorType == ElementMirrorType.canvasCenterX) {
        /// 画板中心 x 轴镜像
        return rect.copyWith(left: mirrorPosition.dx);
      } else {
        /// 画板中心 y 轴镜像
        return rect.copyWith(top: mirrorPosition.dy);
      }
    }
  }

  ///是否支持竖排文字
  static bool checkSupportVerticalText() {
    // 竖排文字仅支持应用内 简中/繁中/韩文/日文/英文
    // String currentLanguage = NiimbotIntl.getCurrentLocale().languageCode;
    // return currentLanguage == LanguageName.LANGUAGE_ZH_CN ||
    //     currentLanguage == LanguageName.LANGUAGE_ZH_TW ||
    //     currentLanguage == "zh" ||
    //     currentLanguage == LanguageName.LANGUAGE_KO ||
    //     currentLanguage == LanguageName.LANGUAGE_JA ||
    //     currentLanguage == LanguageName.LANGUAGE_EN;
    return false;
  }

  /// 修复条码值长度
  static String fixBarCodeValueLength(String value, NetalBarcodeType codeType) {
    if (codeType == NetalBarcodeType.CODEBAR && value.length > 57) {
      return value.substring(0, 57);
    }
    return value;
  }

  /// 生成日期值
  /// [time] 时间戳 不穿则为当前时间
  /// [timeOffset] 时间偏移量 单位：分钟
  static String buildDateValue({
    int? time,
    int? timeOffset,
    String? dateFormat,
    String? timeFormat,
    bool? militaryTime,
    String? prefix,
  }) {
    final timestamp = (time ?? DateTime.now().millisecondsSinceEpoch) +
        (timeOffset ?? 0) * 60 * 1000;
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final splitString = NiimbotIntl.getIntlMessage('app100001507', '：');
    final lang = NiimbotIntl.getCurrentLocale().toString();
    final bool isZh = dateFormat == 'yyyy年MM月dd日 EEEE';

    if (!(militaryTime ?? false) && timeFormat != null) {
      final List<String> amPmList = [
        NiimbotIntl.getIntlMessage('app100001461', '上午', param: ['']),
        NiimbotIntl.getIntlMessage('app100001460', '下午', param: ['']),
      ];
      final bool beforeAmPm = ['zh', 'zh_Hant', 'ko', 'ja'].contains(lang);
      String dateFormatVal = dateFormat != null
          ? DateFormat(dateFormat, isZh ? 'zh_CN' : null).format(dateTime)
          : '';
      String timeFormatVal = DateFormat(('$timeFormat a').replaceAll('HH', 'h'))
          .format(dateTime)
          .replaceAll('AM', amPmList.first)
          .replaceAll('PM', amPmList.last);

      // 为CJK语言添加时间单位
      if (beforeAmPm) {
        timeFormatVal = timeFormatVal.split(' ').reversed.join('');
        timeFormatVal =
            addHourUnitToTimeFormat(timeFormatVal, timeFormat, lang);
      }

      return '${prefix ?? ''}${prefix?.isNotEmpty ?? false ? splitString : ''}$dateFormatVal${dateFormatVal.isNotEmpty ? ' ' : ''}$timeFormatVal';
    } else {
      String dateFormatString = DateFormat(
        '${dateFormat ?? ''}${dateFormat != null && timeFormat != null ? ' ' : ''}${timeFormat ?? ''}',
        isZh ? 'zh_CN' : null,
      ).format(dateTime);

      // 为24小时制的CJK语言添加时间单位
      if (timeFormat != null && ['zh', 'zh_Hant', 'ko', 'ja'].contains(lang)) {
        // 提取时间部分并添加单位
        final timePartMatch = RegExp(r'(\d{1,2}:\d{2}(?::\d{2})?|\d{1,2})$')
            .firstMatch(dateFormatString);
        if (timePartMatch != null) {
          final timePart = timePartMatch.group(1)!;
          final timeWithUnit =
              addHourUnitToTimeFormat(timePart, timeFormat, lang);
          dateFormatString = dateFormatString.replaceRange(
              timePartMatch.start, timePartMatch.end, timeWithUnit);
        }
      }

      return '${prefix ?? ''}${prefix?.isNotEmpty ?? false ? splitString : ''}$dateFormatString';
    }
  }

  /// 获取语言对应的时间单位
  static String getHourUnit(String language) {
    switch (language) {
      case 'zh': // 简体中文
        return '时';
      case 'zh_Hant': // 繁体中文
        return '時';
      case 'ko': // 韩语
        return '시';
      case 'ja': // 日语
        return '時';
      default:
        return '';
    }
  }

  /// 为时间格式添加单位
  static String addHourUnitToTimeFormat(
      String timeFormatVal, String timeFormat, String language) {
    final hourUnit = getHourUnit(language);
    if (hourUnit.isEmpty) return timeFormatVal;

    // 如果时间格式包含小时（HH 或 H），则添加时间单位
    if (timeFormat.contains('HH') || timeFormat.contains('H')) {
      // 处理不同的时间格式
      if (timeFormat == 'HH' || timeFormat == 'H') {
        // 只有小时的格式，直接添加单位
        return timeFormatVal + hourUnit;
      } else {
        // 包含小时和其他部分的格式，在小时后添加单位
        // 例如：10:30 -> 10时30分，10:30:45 -> 10时30分45秒
        return timeFormatVal
            .replaceAllMapped(RegExp(r'(\d{1,2})(:)'),
                (match) => '${match.group(1)}$hourUnit${match.group(2)}')
            .replaceAll(':', '分')
            .replaceAllMapped(
                RegExp(r'分(\d{2})$'), (match) => '分${match.group(1)}秒');
      }
    }
    return timeFormatVal;
  }

  /// 生成序列值
  static String buildSerialValue({
    String? prefix,
    String? suffix,
    required String startNumber,
    num? increment,
    required int index,
    int? fixLength,
    String? fixValue,
    SerialElementFormat? format,
    int? endNumber,
  }) {
    final start = int.tryParse(startNumber) ?? 1;
    final num = (start + (increment ?? 1) * (index - 1));
    if (endNumber != null && num > endNumber) {
      return '${prefix ?? ''}${suffix ?? ''}';
    }
    final _fixLength = max(fixLength ?? 2, startNumber.length);
    var numStr = num.toString();
    final isLetter = format == SerialElementFormat.capitalLetter ||
        format == SerialElementFormat.smallLetter;
    if (isLetter) {
      numStr = String.fromCharCode(num.toInt());
    }
    final fillVal = !isLetter
        ? StringUtils.padStart(numStr, _fixLength, fixValue ?? '0')
        : numStr;
    return '${prefix ?? ''}$fillVal${suffix ?? ''}';
  }

  static int daysInMonth(int year, int month, int day) {
    // 构造指定年份和月份的DateTime对象
    DateTime lastDayOfMonth = DateTime(year, month + 1, 0);
    // 计算天数
    int days = lastDayOfMonth.day;
    if (day <= days) {
      return day;
    }
    return days;
  }

  /// 生成日期元素关联时间
  static int buildDateElementAssociateTime(
    bool dateIsRefresh,
    int time,
    ElementDateAssociatedUnit validityPeriodUnit,
    int validityPeriodNew,
  ) {
    final datetime = dateIsRefresh
        ? DateTime.now()
        : DateTime.fromMillisecondsSinceEpoch(time);
    switch (validityPeriodUnit) {
      case ElementDateAssociatedUnit.hour:
        return DateTime(
          datetime.year,
          datetime.month,
          datetime.day,
          datetime.hour + validityPeriodNew,
          datetime.minute - 1,
          datetime.second,
          datetime.millisecond,
          datetime.microsecond,
        ).millisecondsSinceEpoch;
      case ElementDateAssociatedUnit.day:
        return DateTime(
          datetime.year,
          datetime.month,
          datetime.day + validityPeriodNew - 1,
          datetime.hour,
          datetime.minute,
          datetime.second,
          datetime.millisecond,
          datetime.microsecond,
        ).millisecondsSinceEpoch;
      case ElementDateAssociatedUnit.month:
        final day = daysInMonth(
          datetime.year,
          datetime.month + validityPeriodNew,
          datetime.day,
        );
        return DateTime(
          datetime.year,
          datetime.month + validityPeriodNew,
          day - 1,
          datetime.hour,
          datetime.minute,
          datetime.second,
          datetime.millisecond,
          datetime.microsecond,
        ).millisecondsSinceEpoch;
      case ElementDateAssociatedUnit.year:
        final day = daysInMonth(
          datetime.year + validityPeriodNew,
          datetime.month,
          datetime.day,
        );
        return DateTime(
          datetime.year + validityPeriodNew,
          datetime.month,
          day - 1,
          datetime.hour,
          datetime.minute,
          datetime.second,
          datetime.millisecond,
          datetime.microsecond,
        ).millisecondsSinceEpoch;
      default:
        return time;
    }
  }
}
