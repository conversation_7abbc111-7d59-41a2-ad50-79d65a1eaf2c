
import 'dart:math';

import 'package:niimbot_template/models/font_size_config.dart';
import 'package:niimbot_template/models/template/constants.dart';

/// templateWidth, templateHeight 为毫米
/// 根据模版宽高，获取最大字体
List<FontSizeConfig> calcMaxFontSize(
    double templateWidth, double templateHeight) {
  double maxMM = 0;
  double maxEdge = max(templateWidth, templateHeight);
  double minEdge = min(templateWidth, templateHeight);
  if (maxEdge < 2 * minEdge) {
    maxMM = maxEdge / 2;
  }
  if (maxEdge >= 2 * minEdge) {
    maxMM = minEdge;
  }
  int left = 0;
  int right = TemplateConstants.FONT_SIZE_LIST.length - 1;

  int mid = left;
  while (left <= right) {
    mid = (left + right) ~/ 2;
    if (TemplateConstants.FONT_SIZE_LIST[mid].mm == maxMM) {
      left = mid;
      break;
    } else if (TemplateConstants.FONT_SIZE_LIST[mid].mm > maxMM) {
      right = mid - 1;
    } else {
      left = mid + 1;
    }
  }

  if (left >= TemplateConstants.FONT_SIZE_LIST.length) {
    left = TemplateConstants.FONT_SIZE_LIST.length - 1;
  } else if (left < 8) {
    left = 8;
  } else if ((left > 0 &&
      (maxMM - TemplateConstants.FONT_SIZE_LIST[left - 1].mm).abs() <
          (maxMM - TemplateConstants.FONT_SIZE_LIST[left].mm).abs())) {
    left--;
  }

  return TemplateConstants.FONT_SIZE_LIST.sublist(0, left + 1);
  // return allFontSizeConfigList;
}