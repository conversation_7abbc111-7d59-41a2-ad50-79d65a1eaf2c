import 'dart:ui';

import 'package:collection/collection.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_enum.dart';

class TemplateParseUtils {
  /// 从JSON中解析bool类型字段
  /// 由于数据传输不规范 导致 bool类型在JSON中可能存在以下几种类型
  /// 1.正确的bool类型
  /// 2.字符串 如 '1'表示true '0'表示false 'true'表示true 'false'表示false
  /// 3.数字类型 如 1表true 0 表false
  static bool parseBoolFromJSON(dynamic field) {
    if (field is bool) {
      return field;
    }
    if (field is num) {
      return field == 1;
    }
    if (field is String) {
      return field == 'true' || field == '1';
    }
    return false;
  }

  /// 从JSON中解析num类型字段
  /// 由于数据传输不规范 导致 num类型在JSON中可能存在以下几种类型
  /// 1.正确的num类型
  /// 2.字符串类型 如'1'
  static num? parseNumberFromJSON(dynamic field) {
    if (field is num) {
      return field;
    }
    if (field is String) {
      return num.tryParse(field);
    }
    return null;
  }

  /// 从JSON中解析String类型字段
  /// 由于数据传输不规范 导致 String类型在JSON中可能存在以下几种类型
  /// 1.正确的String类型
  /// 2.非String类型
  static String? parseStringFromJSON(dynamic field) {
    if (field == null) {
      return null;
    }
    if (field is String) {
      return field;
    }
    return field.toString();
  }

  /// 从JSON中解析Color类型字段
  /// 由于数据传输不规范 导致Color类型在JSON中可能存在以下几种类型
  /// 1.数组类型 如[255,0,0,0]
  /// 2.字符串类型 如'0.0.0'
  static Color? parseColorFromJSON(dynamic field) {
    if (field is List) {
      if (field.length == 4) {
        return Color.fromARGB(field[0], field[1], field[2], field[3]);
      }
      if (field.length == 3) {
        return Color.fromARGB(255, field[0], field[1], field[2]);
      }
    }
    if (field is String) {
      try {
        List<int> color = field.split(".").map((e) => int.parse(e)).toList();
        if (color.length == 4) {
          return Color.fromARGB(color[0], color[1], color[2], color[3]);
        }
        if (color.length == 3) {
          return Color.fromARGB(255, color[0], color[1], color[2]);
        }
      } catch (e) {}
    }
    return null;
  }

  /// 从JSON中解析List类型字段
  static List<T>? parseListFromJSON<T>(dynamic field) {
    if (field is List) {
      return List<T>.from(field);
    }
    return null;
  }

  /// 从JSON中解析文本对齐方式
  static NetalTextAlign? parseNetalTextAlignFromJSON(dynamic field) {
    final val = parseNumberFromJSON(field)?.toInt();
    if (val != null) return NetalTextAlign.byValue(val);
    return null;
  }

  /// 从JSON中解析文本换行模式
  @Deprecated('换行模式已弃用')
  static NetalTextLineMode? parseNetalTextLineModeFromJSON(dynamic field) {
    final val = parseNumberFromJSON(field)?.toInt();
    if (val != null) return NetalTextLineMode.byValue(val);
    return null;
  }

  /// 从JSON中解析文本样式
  static List<NetalTextFontStyle>? parseNetalTextFontStyleFromJSON(
      dynamic field) {
    if (field is List) {
      // return field.map((e) => NetalTextStyle.values.byName(e)).toList();
      final List<NetalTextFontStyle> list = [];
      for (var e in field) {
        try {
          list.add(NetalTextFontStyle.values.byName(e));
        } catch (e) {
          list.add(NetalTextFontStyle.normal);
        }
      }
      return list;
    }
    return null;
  }

  /// 从JSON中解析文本换行断词模式
  static NetalTextLineBreakMode? parseNetalTextLineBreakModeFromJSON(
      dynamic field) {
    final val = parseNumberFromJSON(field)?.toInt();
    if (val != null) return NetalTextLineBreakMode.byValue(val);
    return null;
  }

  /// 从JSON中解析文本排版模式
  static NetalTypesettingMode? parseNetalTypesettingModeFromJSON(
      dynamic field) {
    final val = parseNumberFromJSON(field)?.toInt();
    if (val != null) return NetalTypesettingMode.byValue(val);
    return null;
  }

  /// 从JSON中解析文本书写方向
  static NetalTextWriteMode? parseNetalTextWriteModeFromJSON(dynamic field) {
    final val = parseNumberFromJSON(field)?.toInt();
    if (val != null) return NetalTextWriteMode.byValue(val);
    return null;
  }

  /// 从JSON中解析线条类型
  static NetalLineType? parseNetalLineTypeFromJSON(dynamic field) {
    final val = parseNumberFromJSON(field)?.toInt();
    if (val != null) return NetalLineType.byValue(val);
    return null;
  }

  /// 从JSON中解析模板尾巴方向
  static NetalCableDirection? parseTemplateCableDirectionFromJSON(
      dynamic field) {
    final val = parseNumberFromJSON(field)?.toInt();
    if (val != null) return NetalCableDirection.byValue(val);
    return null;
  }

  /// 从JSON中解析模板平台类型
  static TemplatePlatformCode? parseTemplatePlatformCodeFromJSON(
      dynamic field) {
    return TemplatePlatformCode.values.firstWhereOrNull((e) => e.name == field);
  }

  /// 从JSON中解析文本框样式
  static NetalTextBoxStyle? parseTemplateNetalTextBoxStyleFromJSON(
      dynamic field) {
    return NetalTextBoxStyle.values.firstWhereOrNull((e) => e.value == field);
  }

  ///从JSON中解析文本内容样式
  static List<NetalTextStyle>? parseTemplateNetalTextStyleFromJSON(
      dynamic field) {
    if (field is List) {
      final List<NetalTextStyle> list = [];
      for (var e in field) {
        try {
          list.add(NetalTextStyle.values.byName(e));
        } catch (e) {
          list.add(NetalTextStyle.norm);
        }
      }
      return list;
    }
    return null;
  }

  ///数据源表头数据处理
  /// [key] 为表名
  /// [value] 为配置信息
  /// [value]为 [int] 类型时 表示为该表的指定行数为表头
  /// [value]为 [List<String>] 类型时 表示为自定义的表头
  static TemplateDataSourceHeaders? parseTemplateDataSourceHeaderFromJSON(
      Map<String, dynamic>? field) {
    if (field != null) {
      return field.map((key, value) {
        if (value is int) {
          return MapEntry(
            key,
            value,
          );
        } else {
          return MapEntry(
            key,
            value.map((v) => v.toString()).toList(),
          );
        }
      });
    }
    return null;
  }
}
