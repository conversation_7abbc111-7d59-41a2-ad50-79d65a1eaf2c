// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'template_data_source_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TemplateDataSourceInfo {
  int get page;
  int get total;
  dynamic get extend;

  /// Create a copy of TemplateDataSourceInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TemplateDataSourceInfoCopyWith<TemplateDataSourceInfo> get copyWith =>
      _$TemplateDataSourceInfoCopyWithImpl<TemplateDataSourceInfo>(
          this as TemplateDataSourceInfo, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TemplateDataSourceInfo &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other.extend, extend));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, page, total, const DeepCollectionEquality().hash(extend));

  @override
  String toString() {
    return 'TemplateDataSourceInfo(page: $page, total: $total, extend: $extend)';
  }
}

/// @nodoc
abstract mixin class $TemplateDataSourceInfoCopyWith<$Res> {
  factory $TemplateDataSourceInfoCopyWith(TemplateDataSourceInfo value,
          $Res Function(TemplateDataSourceInfo) _then) =
      _$TemplateDataSourceInfoCopyWithImpl;
  @useResult
  $Res call({int page, int total, dynamic extend});
}

/// @nodoc
class _$TemplateDataSourceInfoCopyWithImpl<$Res>
    implements $TemplateDataSourceInfoCopyWith<$Res> {
  _$TemplateDataSourceInfoCopyWithImpl(this._self, this._then);

  final TemplateDataSourceInfo _self;
  final $Res Function(TemplateDataSourceInfo) _then;

  /// Create a copy of TemplateDataSourceInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? total = null,
    Object? extend = freezed,
  }) {
    return _then(TemplateDataSourceInfo(
      page: null == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      total: null == total
          ? _self.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      extend: freezed == extend ? _self.extend! : extend,
    ));
  }
}

// dart format on
