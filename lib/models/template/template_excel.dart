import 'package:niimbot_template/models/template/template_excel_data.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

/// 模板里关联的excel数据
class TemplateExcel {
  /// excel Id
  num? id;

  /// 文件名
  String fileName;

  /// 标题分隔符
  String delimiter = '：';

  /// 是否使用标题
  bool useTitle = true;

  /// excel数据
  List<TemplateExcelData> list;

  TemplateExcel({
    this.id,
    required this.fileName,
    required this.list,
    this.delimiter = "：",
    this.useTitle = true,
  });

  TemplateExcel.fromJson(Map<String, dynamic> json)
      : id = TemplateParseUtils.parseNumberFromJSON(json["id"]),
        fileName = json["fileName"] ?? "",
        delimiter = json["delimiter"] ?? "：",
        useTitle = TemplateParseUtils.parseBoolFromJSON(json["useTitle"]),
        list = TemplateParseUtils.parseListFromJSON(json["list"])
                ?.map((i) => TemplateExcelData.fromJson(i))
                .toList() ??
            [];

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "fileName": fileName,
      "delimiter": delimiter,
      "useTitle": useTitle,
      "list": list,
    };
  }
}
