// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'template_data_source.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TemplateDataSource {
  TemplateDataSourceType get type;
  String get uri;
  String get hash;
  Object? get params;
  String? get name;
  TemplateDataSourceHeaders? get headers;
  List<TemplateDataSourceRange>? get range;
  List<List<String>> get rowData;
  List<String> get collections;

  /// Create a copy of TemplateDataSource
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TemplateDataSourceCopyWith<TemplateDataSource> get copyWith =>
      _$TemplateDataSourceCopyWithImpl<TemplateDataSource>(
          this as TemplateDataSource, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TemplateDataSource &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.uri, uri) || other.uri == uri) &&
            (identical(other.hash, hash) || other.hash == hash) &&
            const DeepCollectionEquality().equals(other.params, params) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other.headers, headers) &&
            const DeepCollectionEquality().equals(other.range, range) &&
            const DeepCollectionEquality().equals(other.rowData, rowData) &&
            const DeepCollectionEquality()
                .equals(other.collections, collections));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      uri,
      hash,
      const DeepCollectionEquality().hash(params),
      name,
      const DeepCollectionEquality().hash(headers),
      const DeepCollectionEquality().hash(range),
      const DeepCollectionEquality().hash(rowData),
      const DeepCollectionEquality().hash(collections));

  @override
  String toString() {
    return 'TemplateDataSource(type: $type, uri: $uri, hash: $hash, params: $params, name: $name, headers: $headers, range: $range, rowData: $rowData, collections: $collections)';
  }
}

/// @nodoc
abstract mixin class $TemplateDataSourceCopyWith<$Res> {
  factory $TemplateDataSourceCopyWith(
          TemplateDataSource value, $Res Function(TemplateDataSource) _then) =
      _$TemplateDataSourceCopyWithImpl;
  @useResult
  $Res call(
      {TemplateDataSourceType type,
      String uri,
      String hash,
      Object? params,
      String? name,
      Map<String, dynamic>? headers,
      List<TemplateDataSourceRange>? range,
      List<List<String>> rowData,
      List<String> collections});
}

/// @nodoc
class _$TemplateDataSourceCopyWithImpl<$Res>
    implements $TemplateDataSourceCopyWith<$Res> {
  _$TemplateDataSourceCopyWithImpl(this._self, this._then);

  final TemplateDataSource _self;
  final $Res Function(TemplateDataSource) _then;

  /// Create a copy of TemplateDataSource
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? uri = null,
    Object? hash = null,
    Object? params = freezed,
    Object? name = freezed,
    Object? headers = freezed,
    Object? range = freezed,
    Object? rowData = null,
    Object? collections = null,
  }) {
    return _then(TemplateDataSource(
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as TemplateDataSourceType,
      uri: null == uri
          ? _self.uri
          : uri // ignore: cast_nullable_to_non_nullable
              as String,
      hash: null == hash
          ? _self.hash
          : hash // ignore: cast_nullable_to_non_nullable
              as String,
      params: freezed == params ? _self.params : params,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      headers: freezed == headers
          ? _self.headers!
          : headers // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      range: freezed == range
          ? _self.range
          : range // ignore: cast_nullable_to_non_nullable
              as List<TemplateDataSourceRange>?,
      rowData: null == rowData
          ? _self.rowData
          : rowData // ignore: cast_nullable_to_non_nullable
              as List<List<String>>,
      collections: null == collections
          ? _self.collections
          : collections // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

// dart format on
