import 'package:niimbot_template/utils/template_parse_utils.dart';

/// key 修改的元素Id
/// key 行数 从1开始 0表示所有行
typedef TemplateDataSourceModifies
    = Map<String, Map<String, TemplateDataSourceModify>>;

class TemplateDataSourceModify {
  /// 绑定修改后的值 如果该值存在 则使用该值
  String? value;

  /// 是否使用标题 与标题分隔符同时使用
  bool? useTitle;

  /// 标题分隔符 与是否使用标题同时使用
  String? delimiter;

  /// 自定义标题
  String? title;

  /// 自定义前缀
  String? prefix;

  /// 自定义后缀
  String? suffix;

  TemplateDataSourceModify({
    this.value,
    this.useTitle,
    this.delimiter,
    this.title,
    this.prefix,
    this.suffix,
  });

  factory TemplateDataSourceModify.fromJson(Map<String, dynamic> json) {
    return TemplateDataSourceModify(
      value: TemplateParseUtils.parseStringFromJSON(json["value"]),
      useTitle: TemplateParseUtils.parseBoolFromJSON(json["useTitle"]),
      delimiter: TemplateParseUtils.parseStringFromJSON(json["delimiter"]),
      title: TemplateParseUtils.parseStringFromJSON(json["title"]),
      prefix: TemplateParseUtils.parseStringFromJSON(json["prefix"]),
      suffix: TemplateParseUtils.parseStringFromJSON(json["suffix"]),
    );
  }

  Map<String, dynamic> toJson() {
    return ({
      "value": value,
      "useTitle": useTitle,
      "delimiter": delimiter,
      "title": title,
      "prefix": prefix,
      "suffix": suffix,
    }..removeWhere((k, v) => v == null));
  }

  TemplateDataSourceModify copyWith({
    String? value,
    bool? useTitle,
    String? delimiter,
    String? title,
    String? prefix,
    String? suffix,
  }) {
    return TemplateDataSourceModify(
      value: value ?? this.value,
      useTitle: useTitle ?? this.useTitle,
      delimiter: delimiter ?? this.delimiter,
      title: title ?? this.title,
      prefix: prefix ?? this.prefix,
      suffix: suffix ?? this.suffix,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TemplateDataSourceModify &&
          runtimeType == other.runtimeType &&
          value == other.value &&
          useTitle == other.useTitle &&
          delimiter == other.delimiter &&
          title == other.title &&
          prefix == other.prefix &&
          suffix == other.suffix;

  @override
  int get hashCode =>
      value.hashCode ^
      useTitle.hashCode ^
      delimiter.hashCode ^
      title.hashCode ^
      prefix.hashCode ^
      suffix.hashCode;
}
