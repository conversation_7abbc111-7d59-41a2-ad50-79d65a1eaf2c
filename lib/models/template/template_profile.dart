import 'package:niimbot_template/models/template/template_profile_extra.dart';

/// 模板元信息
class TemplateProfile {
  ///模板条码，可用于查询
  final String? barcode;

  ///关键词，可用于全文检索
  final String? keyword;

  ///硬件系列id
  final String? hardwareSeriesId;

  ///硬件系列名称(SDK分类描述，如D11、U11都属于D11系列)
  final String? machineId;

  ///适配机型（B11、B3S、B50）
  final String? machineName;

  ///自定义扩展属性
  final TemplateProfileExtra extra;

  const TemplateProfile({
    this.barcode,
    this.keyword,
    this.hardwareSeriesId,
    this.machineId,
    this.machineName,
    TemplateProfileExtra? extra,
  }) : extra = extra ?? const TemplateProfileExtra();

  TemplateProfile.fromJson(Map<String, dynamic> json)
      : barcode = json['barcode'],
        keyword = json['keyword'],
        hardwareSeriesId = json['hardwareSeriesId'],
        machineId = json['machineId'],
        machineName = json['machineName'],
        extra = json['extrain'] == null
            ? TemplateProfileExtra()
            : TemplateProfileExtra.fromJson(
                Map<String, dynamic>.from(json['extrain']));

  Map<String, dynamic> toJson() => ({
        'barcode': barcode,
        'keyword': keyword,
        'hardwareSeriesId': hardwareSeriesId,
        'machineId': machineId,
        'machineName': machineName,
        'extrain': extra.toJson(),
      }..removeWhere((k, v) => v == null));

  TemplateProfile copyWith({
    String? barcode,
    String? keyword,
    String? hardwareSeriesId,
    String? machineId,
    String? machineName,
    TemplateProfileExtra? extra,
  }) {
    return TemplateProfile(
      barcode: barcode ?? this.barcode,
      keyword: keyword ?? this.keyword,
      hardwareSeriesId: hardwareSeriesId ?? this.hardwareSeriesId,
      machineId: machineId ?? this.machineId,
      machineName: machineName ?? this.machineName,
      extra: extra ?? this.extra,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TemplateProfile &&
          runtimeType == other.runtimeType &&
          barcode == other.barcode &&
          keyword == other.keyword &&
          hardwareSeriesId == other.hardwareSeriesId &&
          machineId == other.machineId &&
          machineName == other.machineName &&
          extra == other.extra;

  @override
  int get hashCode =>
      barcode.hashCode ^
      keyword.hashCode ^
      hardwareSeriesId.hashCode ^
      machineId.hashCode ^
      machineName.hashCode ^
      extra.hashCode;
}
