// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tube_file_setting.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TubeFileSetting {
  String? get align;
  bool? get autoWidth;
  num? get width;
  int? get line;
  String? get specId;
  String? get specName;
  num? get paragraphCount;

  /// Create a copy of TubeFileSetting
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TubeFileSettingCopyWith<TubeFileSetting> get copyWith =>
      _$TubeFileSettingCopyWithImpl<TubeFileSetting>(
          this as TubeFileSetting, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TubeFileSetting &&
            (identical(other.align, align) || other.align == align) &&
            (identical(other.autoWidth, autoWidth) ||
                other.autoWidth == autoWidth) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.line, line) || other.line == line) &&
            (identical(other.specId, specId) || other.specId == specId) &&
            (identical(other.specName, specName) ||
                other.specName == specName) &&
            (identical(other.paragraphCount, paragraphCount) ||
                other.paragraphCount == paragraphCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, align, autoWidth, width, line,
      specId, specName, paragraphCount);

  @override
  String toString() {
    return 'TubeFileSetting(align: $align, autoWidth: $autoWidth, width: $width, line: $line, specId: $specId, specName: $specName, paragraphCount: $paragraphCount)';
  }
}

/// @nodoc
abstract mixin class $TubeFileSettingCopyWith<$Res> {
  factory $TubeFileSettingCopyWith(
          TubeFileSetting value, $Res Function(TubeFileSetting) _then) =
      _$TubeFileSettingCopyWithImpl;
  @useResult
  $Res call(
      {String? align,
      bool? autoWidth,
      num? width,
      int? line,
      String? specId,
      String? specName,
      num? paragraphCount});
}

/// @nodoc
class _$TubeFileSettingCopyWithImpl<$Res>
    implements $TubeFileSettingCopyWith<$Res> {
  _$TubeFileSettingCopyWithImpl(this._self, this._then);

  final TubeFileSetting _self;
  final $Res Function(TubeFileSetting) _then;

  /// Create a copy of TubeFileSetting
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? align = freezed,
    Object? autoWidth = freezed,
    Object? width = freezed,
    Object? line = freezed,
    Object? specId = freezed,
    Object? specName = freezed,
    Object? paragraphCount = freezed,
  }) {
    return _then(TubeFileSetting(
      align: freezed == align
          ? _self.align
          : align // ignore: cast_nullable_to_non_nullable
              as String?,
      autoWidth: freezed == autoWidth
          ? _self.autoWidth
          : autoWidth // ignore: cast_nullable_to_non_nullable
              as bool?,
      width: freezed == width
          ? _self.width
          : width // ignore: cast_nullable_to_non_nullable
              as num?,
      line: freezed == line
          ? _self.line
          : line // ignore: cast_nullable_to_non_nullable
              as int?,
      specId: freezed == specId
          ? _self.specId
          : specId // ignore: cast_nullable_to_non_nullable
              as String?,
      specName: freezed == specName
          ? _self.specName
          : specName // ignore: cast_nullable_to_non_nullable
              as String?,
      paragraphCount: freezed == paragraphCount
          ? _self.paragraphCount
          : paragraphCount // ignore: cast_nullable_to_non_nullable
              as num?,
    ));
  }
}

// dart format on
