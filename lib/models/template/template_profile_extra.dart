import 'package:niimbot_template/models/template/label_name_info.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

class TemplateProfileExtra {
  // 是否订制标签模板(固定资产)
  final bool? isCustom;

  ///所属文件夹ID
  final String? folderId;

  ///适配平台Code
  final String? adaptPlatformCode;

  ///适配平台名
  final String? adaptPlatformName;

  ///创建的用户id
  final String? userId;

  ///行业分类id
  final String? industryId;

  ///商品分类id
  final String? commodityCategoryId;

  ///下载次数
  final dynamic downloadCount;

  ///是否删除，0_未删除,1_已删除
  final bool? isDelete;

  ///添加时间
  final String? createTime;

  ///最后更新时间
  final String? updateTime;

  ///是否是热门模板，0_不是,1_是
  final bool? isHot;

  ///个人模板来源
  final String? sourceId;

  ///标签纸id
  final String? labelId;

  ///服务端返回用于排序序号
  final String? sortDependency;

  ///模板点击量、访问量,
  final dynamic clickNum;

  ///模板类型，0_普通模板, 1_云模板,2_用户模板样例,4_云模板样例
  final num? templateType;

  ///云模板类型：0：标签，1：模板
  final num? templateClass;

  ///模板展示，0_公开, 1_私有
  final bool? isPrivate;

  ///亚马逊条码（北京）
  final String? amazonCodeBeijing;

  ///亚马逊条码（武汉）
  final String? amazonCodeWuhan;

  ///备用条码,
  final String? sparedCode;

  ///虚拟条码
  final String? virtualBarCode;

  ///是否是新版路径，0_否,1_是
  final bool? isNewPath;

  ///是否移动端模板：1_是:0_否（用于兼容旧模板数据适配平台的字段）
  final bool? isMobileTemplete;

  ///通过扫码取模方式关联的用户手机号码
  final String? phone;

  ///通过扫描打印方式创建模板的商品条码
  final String? goodsCode;

  ///备用条码
  final Map<String, String>? barcodeCategoryMap;

  /// 模板支持的最低版本，值类型为“5.3.0”，为空表示支持所有版本，有值表示低于此版本时此模板不可打开
  /// 主要用于后端判断，当前版本是否可以获取模板详情
  final String? resourceVersion;

  ///标签多语言名称集合
  final List<LabelNameInfo> labelNames;

  ///标签纸sn码
  final String? materialModelSn;

  const TemplateProfileExtra({
    this.isCustom,
    this.folderId,
    this.adaptPlatformCode,
    this.adaptPlatformName,
    this.userId,
    this.industryId,
    this.commodityCategoryId,
    this.downloadCount,
    this.isDelete,
    this.createTime,
    this.updateTime,
    this.isHot,
    this.sourceId,
    this.labelId,
    this.sortDependency,
    this.clickNum,
    this.templateType,
    this.templateClass,
    this.isPrivate,
    this.amazonCodeBeijing,
    this.amazonCodeWuhan,
    this.sparedCode,
    this.virtualBarCode,
    this.isNewPath,
    this.isMobileTemplete,
    this.phone,
    this.goodsCode,
    this.barcodeCategoryMap,
    this.resourceVersion,
    this.labelNames = const [],
    this.materialModelSn,
  });

  TemplateProfileExtra.fromJson(Map<String, dynamic> json)
      : isCustom = (json['isCustom'] is bool)
            ? json['isCustom']
            : json['isCustom'] == 1,
        folderId =
            TemplateParseUtils.parseStringFromJSON(json['folderId']) ?? "0",
        adaptPlatformCode = json['adaptPlatformCode'] == null
            ? ""
            : json['adaptPlatformCode'] as String,
        adaptPlatformName = json['adaptPlatformName'] == null
            ? ""
            : json['adaptPlatformName'] as String,
        userId = TemplateParseUtils.parseStringFromJSON(json['userId']),
        industryId = TemplateParseUtils.parseStringFromJSON(json['industryId']),
        commodityCategoryId =
            TemplateParseUtils.parseStringFromJSON(json['commodityCategoryId']),
        downloadCount = json['downloadCount'] == null
            ? 0
            : json['downloadCount'] as dynamic,
        isDelete = (json['isDelete'] is bool)
            ? json['isDelete']
            : json['isDelete'] == 1,
        createTime = json['createTime'],
        updateTime = json['updateTime'],
        isHot = (json['isHot'] is bool) ? json['isHot'] : json['isHot'] == 1,
        sourceId = TemplateParseUtils.parseStringFromJSON(json['sourceId']),
        labelId = TemplateParseUtils.parseStringFromJSON(json['labelId']),
        sortDependency = json['sortDependency'] == null
            ? ''
            : json['sortDependency'] as String,
        clickNum = json['clickNum'] == null ? 0 : json['clickNum'] as dynamic,
        templateType = (json['templateType'] is num)
            ? json['templateType']
            : num.parse(json['templateType']),
        templateClass =
            TemplateParseUtils.parseNumberFromJSON(json['templateClass']),
        isPrivate = (json['isPrivate'] is bool)
            ? json['isPrivate']
            : json['isPrivate'] == 1,
        amazonCodeBeijing = json['amazonCodeBeijing'] == null
            ? null
            : json['amazonCodeBeijing'] as String,
        amazonCodeWuhan = json['amazonCodeWuhan'] == null
            ? null
            : json['amazonCodeWuhan'] as String,
        sparedCode =
            json['sparedCode'] == null ? null : json['sparedCode'] as String,
        virtualBarCode = json['virtualBarCode'] == null
            ? null
            : json['virtualBarCode'] as String,
        isNewPath = (json['isNewPath'] is bool)
            ? json['isNewPath']
            : json['isNewPath'] == 1,
        isMobileTemplete = (json['isMobileTemplete'] is bool)
            ? json['isMobileTemplete']
            : json['isMobileTemplete'] == 1,
        phone = json['phone'] == null ? null : json['phone'] as String,
        goodsCode =
            json['goodsCode'] == null ? null : json['goodsCode'] as String,
        barcodeCategoryMap =
            Map<String, String>.from(json['barcodeCategoryMap'] as Map),
        resourceVersion = json['resourceVersion'] == null
            ? null
            : json['resourceVersion'] as String,
        labelNames = TemplateParseUtils.parseListFromJSON(json['labelNames'])
                ?.map((e) => LabelNameInfo.fromJson(e))
                .toList() ??
            [],
        materialModelSn = json['materialModelSn'] == null
            ? null
            : json['materialModelSn'] as String;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'isCustom': isCustom ?? false,
        'folderId': folderId ?? "0",
        'adaptPlatformCode': adaptPlatformCode ?? "",
        'adaptPlatformName': adaptPlatformName ?? "",
        'userId': userId ?? "",
        'industryId': industryId ?? "",
        'commodityCategoryId': commodityCategoryId ?? "",
        'downloadCount': downloadCount ?? 0,
        'isDelete': isDelete ?? false,
        'createTime': createTime ?? "",
        'updateTime': updateTime ?? "",
        'isHot': isHot ?? false,
        'sourceId': sourceId ?? "",
        'labelId': labelId ?? "",
        'sortDependency': sortDependency ?? "",
        'clickNum': clickNum ?? 0,
        'templateType': templateType ?? -1,
        'templateClass': templateClass ?? 1,
        'isPrivate': isPrivate ?? false,
        'amazonCodeBeijing': amazonCodeBeijing ?? "",
        'amazonCodeWuhan': amazonCodeWuhan ?? "",
        'sparedCode': sparedCode ?? "",
        'virtualBarCode': virtualBarCode ?? "",
        'isNewPath': isNewPath ?? false,
        'isMobileTemplete': isMobileTemplete ?? true,
        'phone': phone ?? "",
        'goodsCode': goodsCode ?? "",
        'barcodeCategoryMap': barcodeCategoryMap ?? {},
        'resourceVersion': resourceVersion ?? "",
        'labelNames': labelNames.map((e) => e.toJson()).toList(),
        'materialModelSn': materialModelSn ?? "",
      };

  TemplateProfileExtra copyWith({
    bool? isCustom,
    String? folderId,
    String? adaptPlatformCode,
    String? adaptPlatformName,
    String? userId,
    String? industryId,
    String? commodityCategoryId,
    dynamic downloadCount,
    bool? isDelete,
    String? createTime,
    String? updateTime,
    bool? isHot,
    String? sourceId,
    String? labelId,
    String? sortDependency,
    dynamic clickNum,
    num? templateType,
    num? templateClass,
    bool? isPrivate,
    String? amazonCodeBeijing,
    String? amazonCodeWuhan,
    String? sparedCode,
    String? virtualBarCode,
    bool? isNewPath,
    bool? isMobileTemplete,
    String? phone,
    String? goodsCode,
    Map<String, String>? barcodeCategoryMap,
    String? resourceVersion,
    List<LabelNameInfo>? labelNames,
    String? materialModelSn,
  }) {
    return TemplateProfileExtra(
      isCustom: isCustom ?? this.isCustom,
      folderId: folderId ?? this.folderId,
      adaptPlatformCode: adaptPlatformCode ?? this.adaptPlatformCode,
      adaptPlatformName: adaptPlatformName ?? this.adaptPlatformName,
      userId: userId ?? this.userId,
      industryId: industryId ?? this.industryId,
      commodityCategoryId: commodityCategoryId ?? this.commodityCategoryId,
      downloadCount: downloadCount ?? this.downloadCount,
      isDelete: isDelete ?? this.isDelete,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      isHot: isHot ?? this.isHot,
      sourceId: sourceId ?? this.sourceId,
      labelId: labelId ?? this.labelId,
      sortDependency: sortDependency ?? this.sortDependency,
      clickNum: clickNum ?? this.clickNum,
      templateType: templateType ?? this.templateType,
      templateClass: templateClass ?? this.templateClass,
      isPrivate: isPrivate ?? this.isPrivate,
      amazonCodeBeijing: amazonCodeBeijing ?? this.amazonCodeBeijing,
      amazonCodeWuhan: amazonCodeWuhan ?? this.amazonCodeWuhan,
      sparedCode: sparedCode ?? this.sparedCode,
      virtualBarCode: virtualBarCode ?? this.virtualBarCode,
      isNewPath: isNewPath ?? this.isNewPath,
      isMobileTemplete: isMobileTemplete ?? this.isMobileTemplete,
      phone: phone ?? this.phone,
      goodsCode: goodsCode ?? this.goodsCode,
      barcodeCategoryMap: barcodeCategoryMap ?? this.barcodeCategoryMap,
      resourceVersion: resourceVersion ?? this.resourceVersion,
      labelNames: labelNames ?? this.labelNames,
      materialModelSn: materialModelSn ?? this.materialModelSn,
    );
  }
}
