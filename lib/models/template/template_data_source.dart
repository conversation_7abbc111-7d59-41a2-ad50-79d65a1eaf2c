import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:niimbot_template/models/template/template_data_source_range.dart';
import 'package:niimbot_template/models/template/template_enum.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

part 'template_data_source.freezed.dart';

part 'template_data_source.g.dart';

typedef TemplateDataSourceHeaders = Map<String, dynamic>;

@freezed
@JsonSerializable()
class TemplateDataSource with _$TemplateDataSource {
  final TemplateDataSourceType type;
  final String uri;
  final String hash;
  final Object? params;
  final String? name;

  /// 为数据配置的表头
  /// [key] 为表名
  /// [value] 为配置信息
  /// [value]为 [int] 类型时 表示为该表的指定行数为表头
  /// [value]为 [List<String>] 类型时 表示为自定义的表头
  @JsonKey(fromJson: TemplateParseUtils.parseTemplateDataSourceHeaderFromJSON)
  final TemplateDataSourceHeaders? headers;

  /// 选中范围
  final List<TemplateDataSourceRange>? range;

  /// 数据源原始数据
  final List<List<String>> rowData;

  /// 数据集合名称
  final List<String> collections;

  const TemplateDataSource({
    required this.type,
    required this.uri,
    required this.hash,
    this.params,
    this.name,
    this.headers,
    this.range,
    this.rowData = const [],
    this.collections = const [],
  });

  factory TemplateDataSource.fromJson(Map<String, dynamic> json) =>
      _$TemplateDataSourceFromJson(json);

  Map<String, dynamic> toJson() => _$TemplateDataSourceToJson(this);
}
