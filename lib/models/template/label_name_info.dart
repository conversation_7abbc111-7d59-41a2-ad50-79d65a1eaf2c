class LabelNameInfo {
  String languageCode;
  String languageName;
  String name;

  LabelNameInfo(this.languageCode, this.languageName, this.name);

  LabelNameInfo.fromJson(Map<String, dynamic> json)
      : languageCode = json['languageCode'] as String,
        languageName = json['languageName'] as String,
        name = json['name'] as String;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'languageCode': languageCode,
        'languageName': languageName,
        'name': name,
      };

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LabelNameInfo &&
          runtimeType == other.runtimeType &&
          languageCode == other.languageCode &&
          languageName == other.languageName &&
          name == other.name;

  @override
  int get hashCode =>
      languageCode.hashCode ^ languageName.hashCode ^ name.hashCode;
}
