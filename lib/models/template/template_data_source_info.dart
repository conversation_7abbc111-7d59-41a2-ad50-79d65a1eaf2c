import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

part 'template_data_source_info.freezed.dart';

part 'template_data_source_info.g.dart';

@freezed
@JsonSerializable()
class TemplateDataSourceInfo with _$TemplateDataSourceInfo {
  /// 当前显示导入数据页码 从1开始
  @JsonKey(fromJson: _parsePage)
  final int page;

  /// 导入数据总条数
  final int total;

  final dynamic extend;

  const TemplateDataSourceInfo({
    this.page = 1,
    required this.total,
    this.extend,
  });

  factory TemplateDataSourceInfo.fromJson(Map<String, dynamic> json) =>
      _$TemplateDataSourceInfoFromJson(json);

  Map<String, dynamic> toJson() => _$TemplateDataSourceInfoToJson(this);
}

/// 解析页码
int _parsePage(dynamic field) {
  final val = TemplateParseUtils.parseNumberFromJSON(field)?.toInt() ?? 1;
  if (val < 1) return 1;
  return val;
}
