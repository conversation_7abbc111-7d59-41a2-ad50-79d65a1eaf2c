import 'package:collection/collection.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

class TemplateDataSourceRange implements Comparable<TemplateDataSourceRange> {
  /// 开始行 从0开始
  int s;

  /// 结束行 从0开始
  int e;

  TemplateDataSourceRange({required this.s, required this.e})
      : assert(e >= s, "end value must greater or equal to start value");

  TemplateDataSourceRange.fromJson(Map<String, dynamic> json)
      : s = TemplateParseUtils.parseNumberFromJSON(json['s'])?.toInt() ?? 0,
        e = TemplateParseUtils.parseNumberFromJSON(json['e'])?.toInt() ?? 0;

  Map<String, dynamic> toJson() {
    return {
      "s": s,
      "e": e,
    };
  }

  @override
  int compareTo(TemplateDataSourceRange other) {
    if (s == other.s) {
      if (e < other.e) {
        return -1;
      }
      if (e > other.e) {
        return 1;
      }
      return 0;
    }
    if (s < other.s) {
      return -1;
    }
    return 1;
  }

  // 将Range对象转换为数字列表的方法
  List<int> toList() {
    return List.generate(e - s + 1, (i) => s + i);
  }

  // 重写toString方法，以便更好地显示Range对象
  @override
  String toString() {
    return '{s: $s, e: $e}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TemplateDataSourceRange &&
          runtimeType == other.runtimeType &&
          s == other.s &&
          e == other.e;

  @override
  int get hashCode => s.hashCode ^ e.hashCode;
}

List<TemplateDataSourceRange> rangeMerge(List<TemplateDataSourceRange> ranges) {
  final sorted = ranges.sorted();
  for (var i = 1; i < sorted.length;) {
    if (sorted[i - 1].e > sorted[i].s) {
      /* 当前range结束值大于下一个的起始值 */
      sorted[i - 1].e = sorted[i].e;
      sorted.removeAt(i);
    } else {
      i++;
    }
  }
  return sorted;
}

/// 获取在range中的索引
/// [index] 从0开始
int? getRangeIndex(List<TemplateDataSourceRange> ranges, int index) {
  final list = rangeToArr(ranges);
  return list.elementAtOrNull(index);
}

// 定义DataSourceRange类型别名
typedef DataSourceRange = List<TemplateDataSourceRange>;

/// 选择的数组生成Range的函数
/// [rows] 索引从1开始
DataSourceRange arrToRange(List<int> rows) {
  // 去重
  List<int> uniqueRows = List.from(rows.toSet());
  // 排序
  uniqueRows.sort();

  List<TemplateDataSourceRange> result = [];
  int? start = uniqueRows.first;

  for (int i = 1; i < uniqueRows.length; i++) {
    if (uniqueRows[i] == uniqueRows[i - 1] + 1) {
      continue;
    } else {
      result.add(
          TemplateDataSourceRange(s: start! - 1, e: uniqueRows[i - 1] - 1));
      start = uniqueRows[i];
    }
  }

  // 添加最后一个范围
  result.add(TemplateDataSourceRange(s: start! - 1, e: uniqueRows.last - 1));

  return result;
}

// range生成数组的函数
List<int> rangeToArr(DataSourceRange range) {
  List<int> result = [];

  for (var obj in range) {
    result.addAll(obj.toList());
  }

  return result;
}
