import 'package:niimbot_template/utils/template_parse_utils.dart';

/// Excel的Sheet数据
class TemplateExcelDataSheet {
  /// 列头信息数组
  List<String> columnHeaders;

  /// 每列数据
  List<List<String>> columns;

  TemplateExcelDataSheet({required this.columnHeaders, required this.columns});

  TemplateExcelDataSheet.fromJson(Map<String, dynamic> json)
      : columnHeaders = TemplateParseUtils.parseListFromJSON<String>(
                json["columnHeaders"]) ??
            [],
        columns = _parseColumns(json["columns"]);

  Map<String, dynamic> toJson() {
    return {
      "columnHeaders": columnHeaders,
      "columns": columns,
    };
  }
}

List<List<String>> _parseColumns(dynamic columns) {
  final data = TemplateParseUtils.parseListFromJSON(columns);
  if (data != null) {
    final List<List<String>> list = [];
    for (final e in data) {
      final column = TemplateParseUtils.parseListFromJSON(e);
      if (column != null) {
        final List<String> li = [];
        for (final i in column) {
          final item = TemplateParseUtils.parseStringFromJSON(i);
          if (item != null) li.add(item);
        }
        list.add(li);
      }
    }
    return list;
  }
  return [];
}
