// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tube_file_setting.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TubeFileSetting _$TubeFileSettingFromJson(Map<String, dynamic> json) =>
    TubeFileSetting(
      align: json['align'] as String?,
      autoWidth: json['autoWidth'] as bool?,
      width: json['width'] as num?,
      line: (json['line'] as num?)?.toInt(),
      specId: json['specId'] as String?,
      specName: json['specName'] as String?,
      paragraphCount: json['paragraphCount'] as num?,
    );

Map<String, dynamic> _$TubeFileSettingToJson(TubeFileSetting instance) =>
    <String, dynamic>{
      'align': instance.align,
      'autoWidth': instance.autoWidth,
      'width': instance.width,
      'line': instance.line,
      'specId': instance.specId,
      'specName': instance.specName,
      'paragraphCount': instance.paragraphCount,
    };
