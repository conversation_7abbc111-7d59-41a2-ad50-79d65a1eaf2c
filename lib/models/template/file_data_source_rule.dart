import 'package:niimbot_template/utils/template_parse_utils.dart';

class FileDataSourceRule {
  final String elementId;
  final List<int> pages;

  FileDataSourceRule({
    required this.elementId,
    required this.pages,
  });

  factory FileDataSourceRule.fromJson(Map<String, dynamic> json) {
    return FileDataSourceRule(
      elementId: json['elementId'] as String,
      pages: TemplateParseUtils.parseListFromJSON<int>(json['pages']) ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'elementId': elementId,
      'pages': pages,
    };
  }
}
