// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'template_data_source.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TemplateDataSource _$TemplateDataSourceFromJson(Map<String, dynamic> json) =>
    TemplateDataSource(
      type: $enumDecode(_$TemplateDataSourceTypeEnumMap, json['type']),
      uri: json['uri'] as String,
      hash: json['hash'] as String,
      params: json['params'],
      name: json['name'] as String?,
      headers: TemplateParseUtils.parseTemplateDataSourceHeaderFromJSON(
          json['headers'] as Map<String, dynamic>?),
      range: (json['range'] as List<dynamic>?)
          ?.map((e) =>
              TemplateDataSourceRange.fromJson(e as Map<String, dynamic>))
          .toList(),
      rowData: (json['rowData'] as List<dynamic>?)
              ?.map(
                  (e) => (e as List<dynamic>).map((e) => e as String).toList())
              .toList() ??
          const [],
      collections: (json['collections'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$TemplateDataSourceToJson(TemplateDataSource instance) =>
    <String, dynamic>{
      'type': _$TemplateDataSourceTypeEnumMap[instance.type]!,
      'uri': instance.uri,
      'hash': instance.hash,
      'params': instance.params,
      'name': instance.name,
      'headers': instance.headers,
      'range': instance.range,
      'rowData': instance.rowData,
      'collections': instance.collections,
    };

const _$TemplateDataSourceTypeEnumMap = {
  TemplateDataSourceType.excel: 'excel',
  TemplateDataSourceType.commodity: 'commodity',
};
