import 'package:collection/collection.dart';

enum TemplatePlatformCode { CP001Mobile, CP001PC }

enum TemplateDataSourceType { excel, commodity }

enum ElementModifyType {
  /// 修改是否显示列名
  modifyUseTitle,

  /// 修改列名
  modifyTitle,

  /// 修改列值
  modifyValue,
}

enum TemplateEditStatus {
  /// 未编辑
  none(0),

  /// 待编辑
  toBeEdited(1),

  /// 已编辑
  edited(2);

  final int value;

  const TemplateEditStatus(this.value);

  static TemplateEditStatus? byValue(final int value) {
    return TemplateEditStatus.values.firstWhereOrNull(
      (final e) => e.value == value,
    );
  }
}

enum TemplateAttributes {
  COMMODITY_TEMPLATE(4),
  TUBE_FILE(8);

  final int value;

  const TemplateAttributes(this.value);

  static TemplateAttributes? byValue(final int value) {
    return TemplateAttributes.values.firstWhereOrNull(
      (final e) => e.value == value,
    );
  }
}
