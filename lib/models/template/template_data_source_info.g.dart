// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'template_data_source_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TemplateDataSourceInfo _$TemplateDataSourceInfoFromJson(
        Map<String, dynamic> json) =>
    TemplateDataSourceInfo(
      page: json['page'] == null ? 1 : _parsePage(json['page']),
      total:
          TemplateParseUtils.parseNumberFromJSON(json['total'])?.toInt() ?? 0,
      extend: json['extend'],
    );

Map<String, dynamic> _$TemplateDataSourceInfoToJson(
        TemplateDataSourceInfo instance) =>
    <String, dynamic>{
      'page': instance.page,
      'total': instance.total,
      'extend': instance.extend,
    };
