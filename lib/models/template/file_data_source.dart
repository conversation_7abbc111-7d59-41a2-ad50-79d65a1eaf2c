import 'dart:ui';

import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:niimbot_template/models/template/file_data_source_rule.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

class FileDataSource {
  final String? id;
  final String? templateId;
  final String? sourceFileId;
  final String? sourceFilePath;
  final int pageCount;
  final List<String> pageImages;
  final String? imageDir;
  final Rect? crop;
  final List<FileDataSourceRule> rule;

  ///type: image pdf
  final String type;
  final String? originImage;
  final String? croppedImage;

  FileDataSource({
    this.id,
    this.templateId,
    this.sourceFileId,
    this.sourceFilePath,
    required this.pageCount,
    required this.pageImages,
    this.imageDir,
    this.crop,
    required this.rule,
    required this.type,
    this.originImage,
    this.croppedImage,
  });

  factory FileDataSource.fromJson(Map<String, dynamic> json) {
    return FileDataSource(
      id: json['id'],
      templateId: json['templateId'],
      sourceFileId: json['sourceFileId'],
      sourceFilePath: json['sourceFilePath'],
      pageCount: json['pageCount'] as int,
      pageImages:
          TemplateParseUtils.parseListFromJSON<String>(json['pageImages']) ??
              [],
      imageDir: json['imageDir'],
      crop: json['crop'] == null
          ? null
          : Rect.fromLTWH(
              double.parse(json['crop']['x'].toString()),
              double.parse(json['crop']['y'].toString()),
              double.parse(json['crop']['width'].toString()),
              double.parse(json['crop']['height'].toString()),
            ),
      rule: (json['rule'] as List<dynamic>)
          .map((e) => FileDataSourceRule.fromJson(e as Map<String, dynamic>))
          .toList(),
      type: json['type'] ?? (json['sourceFilePath'] != null ? 'pdf' : 'image'),
      originImage: json['originImage'],
      croppedImage: json['croppedImage'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'templateId': templateId,
      'sourceFileId': sourceFileId,
      'sourceFilePath': sourceFilePath,
      'pageCount': pageCount,
      'pageImages': pageImages,
      'imageDir': imageDir,
      'crop': crop == null
          ? null
          : {
              'x': crop?.left,
              'y': crop?.top,
              'width': crop?.width,
              'height': crop?.height,
            },
      'rule': rule.map((e) => e.toJson()).toList(),
      'type': type,
      'originImage': originImage,
      'croppedImage': croppedImage,
    }..removeWhere((k, v) => v == null);
  }

  FileDataSource copyWith({
    CopyWrapper<String?>? id,
    CopyWrapper<String?>? templateId,
    CopyWrapper<String?>? sourceFileId,
    CopyWrapper<String?>? sourceFilePath,
    int? pageCount,
    List<String>? pageImages,
    CopyWrapper<String?>? imageDir,
    Rect? crop,
    List<FileDataSourceRule>? rule,
    String? type,
    String? originImage,
    String? croppedImage,
  }) {
    return FileDataSource(
      id: id != null ? id.value : this.id,
      templateId: templateId != null ? templateId.value : this.templateId,
      sourceFileId: sourceFileId != null ? sourceFileId.value : this.sourceFileId,
      sourceFilePath: sourceFilePath != null ? sourceFilePath.value : this.sourceFilePath,
      pageCount: pageCount ?? this.pageCount,
      pageImages: pageImages ?? this.pageImages,
      imageDir: imageDir != null ? imageDir.value : this.imageDir,
      crop: crop ?? this.crop,
      rule: rule ?? this.rule,
      type: type ?? this.type,
      originImage: originImage ?? this.originImage,
      croppedImage: croppedImage ?? this.croppedImage,
    );
  }
}
