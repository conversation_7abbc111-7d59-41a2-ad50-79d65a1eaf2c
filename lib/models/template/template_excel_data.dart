import 'template_excel_data_sheet.dart';

/// Excel数据
class TemplateExcelData {
  /// Sheet名称
  String name;

  /// Sheet 数据
  TemplateExcelDataSheet data;

  TemplateExcelData({required this.name, required this.data});

  TemplateExcelData.fromJson(Map<String, dynamic> json)
      : name = json["name"],
        data = TemplateExcelDataSheet.fromJson(json["data"]);

  Map<String, dynamic> toJson() {
    return {
      "name": name,
      "data": data,
    };
  }
}
