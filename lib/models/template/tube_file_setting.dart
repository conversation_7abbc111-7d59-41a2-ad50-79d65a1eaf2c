import 'package:freezed_annotation/freezed_annotation.dart';

part 'tube_file_setting.freezed.dart';

part 'tube_file_setting.g.dart';

@freezed
@JsonSerializable()
class TubeFileSetting with _$TubeFileSetting {
  final String? align;
  final bool? autoWidth;
  final num? width;
  final int? line;
  final String? specId;
  final String? specName;
  final num? paragraphCount;

  const TubeFileSetting({
    this.align,
    this.autoWidth,
    this.width,
    this.line,
    this.specId,
    this.specName,
    this.paragraphCount,
  });

  factory TubeFileSetting.fromJson(Map<String, dynamic> json) =>
      _$TubeFileSettingFromJson(json);

  Map<String, dynamic> toJson() => _$TubeFileSettingToJson(this);
}
