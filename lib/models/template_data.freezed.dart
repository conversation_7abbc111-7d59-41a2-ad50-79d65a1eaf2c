// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'template_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TemplateData {
  String? get id;

  String get similarity;

  String? get cloudTemplateId;

  String get name;

  List<LabelNameInfo>? get names;

  List<LabelNameInfo> get labelNames;

  String? get description;

  String get thumbnail;

  String get backgroundImage;

  int get multipleBackIndex;

  num get width;

  num get height;

  num get rotate;

  int get consumableType;

  int get paperType;

  bool get isCable;

  num get cableLength;

  NetalCableDirection? get cableDirection;

  List<num> get margin;

  Map<String, String> get usedFonts;

  List<BaseElement> get elements;

  TemplateProfile get profile;

  String? get labelId;

  int get totalPage;

  int get currentPageIndex;

  num get templatePrintMode;

  TemplatePlatformCode get platformCode;

  num get accuracyName;

  bool get hasVipRes;

  bool get vip;

  bool get commodityTemplate;

  String? get version;

  num get canvasRotate;

  String? get originTemplateId;

  TemplateEditStatus get isEdited;

  String get contentThumbnail;

  String get localContentThumb;

  List<String> get localBackground;

  Map<String, dynamic>? get business;

  String? get dataBindingMode;

  String? get goodsIds;

  List<GoodsModel>? get goodsData;

  Map<String, Map<String, TemplateDataSourceModify>>? get dataSourceModifies;

  List<TemplateDataSource>? get dataSources;

  TemplateDataSourceInfo? get dataSourceBindInfo;

  String? get templateVersion;

  List<ElementValue>? get values;

  List<TemplateAttributes>? get templateAttributes;

  TubeFileSetting? get tubeFileSetting;

  List<FileDataSource>? get fileDataSources;

  /// Create a copy of TemplateData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TemplateDataCopyWith<TemplateData> get copyWith =>
      _$TemplateDataCopyWithImpl<TemplateData>(
          this as TemplateData, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'TemplateData'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('similarity', similarity))
      ..add(DiagnosticsProperty('cloudTemplateId', cloudTemplateId))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('names', names))
      ..add(DiagnosticsProperty('labelNames', labelNames))
      ..add(DiagnosticsProperty('description', description))
      ..add(DiagnosticsProperty('thumbnail', thumbnail))
      ..add(DiagnosticsProperty('backgroundImage', backgroundImage))
      ..add(DiagnosticsProperty('multipleBackIndex', multipleBackIndex))
      ..add(DiagnosticsProperty('width', width))
      ..add(DiagnosticsProperty('height', height))
      ..add(DiagnosticsProperty('rotate', rotate))
      ..add(DiagnosticsProperty('consumableType', consumableType))
      ..add(DiagnosticsProperty('paperType', paperType))
      ..add(DiagnosticsProperty('isCable', isCable))
      ..add(DiagnosticsProperty('cableLength', cableLength))
      ..add(DiagnosticsProperty('cableDirection', cableDirection))
      ..add(DiagnosticsProperty('margin', margin))
      ..add(DiagnosticsProperty('usedFonts', usedFonts))
      ..add(DiagnosticsProperty('elements', elements))
      ..add(DiagnosticsProperty('profile', profile))
      ..add(DiagnosticsProperty('labelId', labelId))
      ..add(DiagnosticsProperty('totalPage', totalPage))
      ..add(DiagnosticsProperty('currentPageIndex', currentPageIndex))
      ..add(DiagnosticsProperty('templatePrintMode', templatePrintMode))
      ..add(DiagnosticsProperty('platformCode', platformCode))
      ..add(DiagnosticsProperty('accuracyName', accuracyName))
      ..add(DiagnosticsProperty('hasVipRes', hasVipRes))
      ..add(DiagnosticsProperty('vip', vip))
      ..add(DiagnosticsProperty('commodityTemplate', commodityTemplate))
      ..add(DiagnosticsProperty('version', version))
      ..add(DiagnosticsProperty('canvasRotate', canvasRotate))
      ..add(DiagnosticsProperty('originTemplateId', originTemplateId))
      ..add(DiagnosticsProperty('isEdited', isEdited))
      ..add(DiagnosticsProperty('contentThumbnail', contentThumbnail))
      ..add(DiagnosticsProperty('localContentThumb', localContentThumb))
      ..add(DiagnosticsProperty('localBackground', localBackground))
      ..add(DiagnosticsProperty('business', business))
      ..add(DiagnosticsProperty('dataBindingMode', dataBindingMode))
      ..add(DiagnosticsProperty('goodsIds', goodsIds))
      ..add(DiagnosticsProperty('goodsData', goodsData))
      ..add(DiagnosticsProperty('dataSourceModifies', dataSourceModifies))
      ..add(DiagnosticsProperty('dataSources', dataSources))
      ..add(DiagnosticsProperty('dataSourceBindInfo', dataSourceBindInfo))
      ..add(DiagnosticsProperty('templateVersion', templateVersion))
      ..add(DiagnosticsProperty('values', values))
      ..add(DiagnosticsProperty('templateAttributes', templateAttributes))
      ..add(DiagnosticsProperty('tubeFileSetting', tubeFileSetting))
      ..add(DiagnosticsProperty('fileDataSources', fileDataSources));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TemplateData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.similarity, similarity) ||
                other.similarity == similarity) &&
            (identical(other.cloudTemplateId, cloudTemplateId) ||
                other.cloudTemplateId == cloudTemplateId) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other.names, names) &&
            const DeepCollectionEquality()
                .equals(other.labelNames, labelNames) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.thumbnail, thumbnail) ||
                other.thumbnail == thumbnail) &&
            (identical(other.backgroundImage, backgroundImage) ||
                other.backgroundImage == backgroundImage) &&
            (identical(other.multipleBackIndex, multipleBackIndex) ||
                other.multipleBackIndex == multipleBackIndex) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.rotate, rotate) || other.rotate == rotate) &&
            (identical(other.consumableType, consumableType) ||
                other.consumableType == consumableType) &&
            (identical(other.paperType, paperType) ||
                other.paperType == paperType) &&
            (identical(other.isCable, isCable) || other.isCable == isCable) &&
            (identical(other.cableLength, cableLength) ||
                other.cableLength == cableLength) &&
            (identical(other.cableDirection, cableDirection) ||
                other.cableDirection == cableDirection) &&
            const DeepCollectionEquality().equals(other.margin, margin) &&
            const DeepCollectionEquality().equals(other.usedFonts, usedFonts) &&
            const DeepCollectionEquality().equals(other.elements, elements) &&
            (identical(other.profile, profile) || other.profile == profile) &&
            (identical(other.labelId, labelId) || other.labelId == labelId) &&
            (identical(other.totalPage, totalPage) ||
                other.totalPage == totalPage) &&
            (identical(other.currentPageIndex, currentPageIndex) ||
                other.currentPageIndex == currentPageIndex) &&
            (identical(other.templatePrintMode, templatePrintMode) ||
                other.templatePrintMode == templatePrintMode) &&
            (identical(other.platformCode, platformCode) ||
                other.platformCode == platformCode) &&
            (identical(other.accuracyName, accuracyName) ||
                other.accuracyName == accuracyName) &&
            (identical(other.hasVipRes, hasVipRes) ||
                other.hasVipRes == hasVipRes) &&
            (identical(other.vip, vip) || other.vip == vip) &&
            (identical(other.commodityTemplate, commodityTemplate) ||
                other.commodityTemplate == commodityTemplate) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.canvasRotate, canvasRotate) ||
                other.canvasRotate == canvasRotate) &&
            (identical(other.originTemplateId, originTemplateId) ||
                other.originTemplateId == originTemplateId) &&
            (identical(other.isEdited, isEdited) ||
                other.isEdited == isEdited) &&
            (identical(other.contentThumbnail, contentThumbnail) ||
                other.contentThumbnail == contentThumbnail) &&
            (identical(other.localContentThumb, localContentThumb) ||
                other.localContentThumb == localContentThumb) &&
            const DeepCollectionEquality()
                .equals(other.localBackground, localBackground) &&
            const DeepCollectionEquality().equals(other.business, business) &&
            (identical(other.dataBindingMode, dataBindingMode) ||
                other.dataBindingMode == dataBindingMode) &&
            (identical(other.goodsIds, goodsIds) ||
                other.goodsIds == goodsIds) &&
            const DeepCollectionEquality().equals(other.goodsData, goodsData) &&
            const DeepCollectionEquality()
                .equals(other.dataSourceModifies, dataSourceModifies) &&
            const DeepCollectionEquality()
                .equals(other.dataSources, dataSources) &&
            (identical(other.dataSourceBindInfo, dataSourceBindInfo) ||
                other.dataSourceBindInfo == dataSourceBindInfo) &&
            (identical(other.templateVersion, templateVersion) ||
                other.templateVersion == templateVersion) &&
            const DeepCollectionEquality().equals(other.values, values) &&
            const DeepCollectionEquality()
                .equals(other.templateAttributes, templateAttributes) &&
            (identical(other.tubeFileSetting, tubeFileSetting) ||
                other.tubeFileSetting == tubeFileSetting) &&
            const DeepCollectionEquality()
                .equals(other.fileDataSources, fileDataSources));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        similarity,
        cloudTemplateId,
        name,
        const DeepCollectionEquality().hash(names),
        const DeepCollectionEquality().hash(labelNames),
        description,
        thumbnail,
        backgroundImage,
        multipleBackIndex,
        width,
        height,
        rotate,
        consumableType,
        paperType,
        isCable,
        cableLength,
        cableDirection,
        const DeepCollectionEquality().hash(margin),
        const DeepCollectionEquality().hash(usedFonts),
        const DeepCollectionEquality().hash(elements),
        profile,
        labelId,
        totalPage,
        currentPageIndex,
        templatePrintMode,
        platformCode,
        accuracyName,
        hasVipRes,
        vip,
        commodityTemplate,
        version,
        canvasRotate,
        originTemplateId,
        isEdited,
        contentThumbnail,
        localContentThumb,
        const DeepCollectionEquality().hash(localBackground),
        const DeepCollectionEquality().hash(business),
        dataBindingMode,
        goodsIds,
        const DeepCollectionEquality().hash(goodsData),
        const DeepCollectionEquality().hash(dataSourceModifies),
        const DeepCollectionEquality().hash(dataSources),
        dataSourceBindInfo,
        templateVersion,
        const DeepCollectionEquality().hash(values),
        const DeepCollectionEquality().hash(templateAttributes),
        tubeFileSetting,
        const DeepCollectionEquality().hash(fileDataSources)
      ]);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TemplateData(id: $id, similarity: $similarity, cloudTemplateId: $cloudTemplateId, name: $name, names: $names, labelNames: $labelNames, description: $description, thumbnail: $thumbnail, backgroundImage: $backgroundImage, multipleBackIndex: $multipleBackIndex, width: $width, height: $height, rotate: $rotate, consumableType: $consumableType, paperType: $paperType, isCable: $isCable, cableLength: $cableLength, cableDirection: $cableDirection, margin: $margin, usedFonts: $usedFonts, elements: $elements, profile: $profile, labelId: $labelId, totalPage: $totalPage, currentPageIndex: $currentPageIndex, templatePrintMode: $templatePrintMode, platformCode: $platformCode, accuracyName: $accuracyName, hasVipRes: $hasVipRes, vip: $vip, commodityTemplate: $commodityTemplate, version: $version, canvasRotate: $canvasRotate, originTemplateId: $originTemplateId, isEdited: $isEdited, contentThumbnail: $contentThumbnail, localContentThumb: $localContentThumb, localBackground: $localBackground, business: $business, dataBindingMode: $dataBindingMode, goodsIds: $goodsIds, goodsData: $goodsData, dataSourceModifies: $dataSourceModifies, dataSources: $dataSources, dataSourceBindInfo: $dataSourceBindInfo, templateVersion: $templateVersion, values: $values, templateAttributes: $templateAttributes, tubeFileSetting: $tubeFileSetting, fileDataSources: $fileDataSources)';
  }
}

/// @nodoc
abstract mixin class $TemplateDataCopyWith<$Res> {
  factory $TemplateDataCopyWith(
          TemplateData value, $Res Function(TemplateData) _then) =
      _$TemplateDataCopyWithImpl;

  @useResult
  $Res call(
      {
      String? id,
      String? name,
      String? cloudTemplateId,
      List<LabelNameInfo>? names,
      List<LabelNameInfo> labelNames,
      String? description,
      String? thumbnail,
      String? backgroundImage,
      int? multipleBackIndex,
      num? width,
      num? height,
      num? rotate,
      num? canvasRotate,
      int? consumableType,
      int? paperType,
      bool? isCable,
      num? cableLength,
      NetalCableDirection? cableDirection,
      List<num>? margin,
      Map<String, String> usedFonts,
      List<BaseElement>? elements,
      String? labelId,
      int totalPage,
      List<String> localBackground,
      num templatePrintMode,
      TemplatePlatformCode? platformCode,
      num? accuracyName,
      bool? hasVipRes,
      bool? vip,
      bool? commodityTemplate,
      String? version,
      String? originTemplateId,
      TemplateEditStatus isEdited,
      String contentThumbnail,
      String localContentThumb,
      Map<String, dynamic>? business,
      int currentPageIndex,
      List<TemplateDataSource>? dataSources,
      TemplateDataSourceInfo? dataSourceBindInfo,
      Map<String, Map<String, TemplateDataSourceModify>>? dataSourceModifies,
      String? templateVersion,
      List<GoodsModel>? goodsData,
      TemplateProfile? profile,
      String? dataBindingMode,
      String similarity,
      String? goodsIds,
      List<ElementValue>? values,
      List<TemplateAttributes>? templateAttributes,
      TubeFileSetting? tubeFileSetting,
      List<FileDataSource>? fileDataSources});
}

/// @nodoc
class _$TemplateDataCopyWithImpl<$Res> implements $TemplateDataCopyWith<$Res> {
  _$TemplateDataCopyWithImpl(this._self, this._then);

  final TemplateData _self;
  final $Res Function(TemplateData) _then;

  /// Create a copy of TemplateData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? cloudTemplateId = freezed,
    Object? names = freezed,
    Object? labelNames = null,
    Object? description = freezed,
    Object? thumbnail = freezed,
    Object? backgroundImage = freezed,
    Object? multipleBackIndex = freezed,
    Object? width = freezed,
    Object? height = freezed,
    Object? rotate = freezed,
    Object? canvasRotate = freezed,
    Object? consumableType = freezed,
    Object? paperType = freezed,
    Object? isCable = freezed,
    Object? cableLength = freezed,
    Object? cableDirection = freezed,
    Object? margin = freezed,
    Object? usedFonts = null,
    Object? elements = freezed,
    Object? labelId = freezed,
    Object? totalPage = null,
    Object? localBackground = null,
    Object? templatePrintMode = null,
    Object? platformCode = freezed,
    Object? accuracyName = freezed,
    Object? hasVipRes = freezed,
    Object? vip = freezed,
    Object? commodityTemplate = freezed,
    Object? version = freezed,
    Object? originTemplateId = freezed,
    Object? isEdited = null,
    Object? contentThumbnail = null,
    Object? localContentThumb = null,
    Object? business = freezed,
    Object? currentPageIndex = null,
    Object? dataSources = freezed,
    Object? dataSourceBindInfo = freezed,
    Object? dataSourceModifies = freezed,
    Object? templateVersion = freezed,
    Object? goodsData = freezed,
    Object? profile = freezed,
    Object? dataBindingMode = freezed,
    Object? similarity = null,
    Object? goodsIds = freezed,
    Object? values = freezed,
    Object? templateAttributes = freezed,
    Object? tubeFileSetting = freezed,
    Object? fileDataSources = freezed,
  }) {
    return _then(TemplateData(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _self.name!
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      cloudTemplateId: freezed == cloudTemplateId
          ? _self.cloudTemplateId
          : cloudTemplateId // ignore: cast_nullable_to_non_nullable
              as String?,
      names: freezed == names
          ? _self.names
          : names // ignore: cast_nullable_to_non_nullable
              as List<LabelNameInfo>?,
      labelNames: null == labelNames
          ? _self.labelNames
          : labelNames // ignore: cast_nullable_to_non_nullable
              as List<LabelNameInfo>,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      thumbnail: freezed == thumbnail
          ? _self.thumbnail!
          : thumbnail // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundImage: freezed == backgroundImage
          ? _self.backgroundImage!
          : backgroundImage // ignore: cast_nullable_to_non_nullable
              as String?,
      multipleBackIndex: freezed == multipleBackIndex
          ? _self.multipleBackIndex!
          : multipleBackIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      width: freezed == width
          ? _self.width!
          : width // ignore: cast_nullable_to_non_nullable
              as num?,
      height: freezed == height
          ? _self.height!
          : height // ignore: cast_nullable_to_non_nullable
              as num?,
      rotate: freezed == rotate
          ? _self.rotate!
          : rotate // ignore: cast_nullable_to_non_nullable
              as num?,
      canvasRotate: freezed == canvasRotate
          ? _self.canvasRotate!
          : canvasRotate // ignore: cast_nullable_to_non_nullable
              as num?,
      consumableType: freezed == consumableType
          ? _self.consumableType!
          : consumableType // ignore: cast_nullable_to_non_nullable
              as int?,
      paperType: freezed == paperType
          ? _self.paperType!
          : paperType // ignore: cast_nullable_to_non_nullable
              as int?,
      isCable: freezed == isCable
          ? _self.isCable!
          : isCable // ignore: cast_nullable_to_non_nullable
              as bool?,
      cableLength: freezed == cableLength
          ? _self.cableLength!
          : cableLength // ignore: cast_nullable_to_non_nullable
              as num?,
      cableDirection: freezed == cableDirection
          ? _self.cableDirection
          : cableDirection // ignore: cast_nullable_to_non_nullable
              as NetalCableDirection?,
      margin: freezed == margin
          ? _self.margin!
          : margin // ignore: cast_nullable_to_non_nullable
              as List<num>?,
      usedFonts: null == usedFonts
          ? _self.usedFonts
          : usedFonts // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      elements: freezed == elements
          ? _self.elements!
          : elements // ignore: cast_nullable_to_non_nullable
              as List<BaseElement>?,
      labelId: freezed == labelId
          ? _self.labelId
          : labelId // ignore: cast_nullable_to_non_nullable
              as String?,
      totalPage: null == totalPage
          ? _self.totalPage
          : totalPage // ignore: cast_nullable_to_non_nullable
              as int,
      localBackground: null == localBackground
          ? _self.localBackground
          : localBackground // ignore: cast_nullable_to_non_nullable
              as List<String>,
      templatePrintMode: null == templatePrintMode
          ? _self.templatePrintMode
          : templatePrintMode // ignore: cast_nullable_to_non_nullable
              as num,
      platformCode: freezed == platformCode
          ? _self.platformCode!
          : platformCode // ignore: cast_nullable_to_non_nullable
              as TemplatePlatformCode?,
      accuracyName: freezed == accuracyName
          ? _self.accuracyName!
          : accuracyName // ignore: cast_nullable_to_non_nullable
              as num?,
      hasVipRes: freezed == hasVipRes
          ? _self.hasVipRes!
          : hasVipRes // ignore: cast_nullable_to_non_nullable
              as bool?,
      vip: freezed == vip
          ? _self.vip!
          : vip // ignore: cast_nullable_to_non_nullable
              as bool?,
      commodityTemplate: freezed == commodityTemplate
          ? _self.commodityTemplate!
          : commodityTemplate // ignore: cast_nullable_to_non_nullable
              as bool?,
      version: freezed == version
          ? _self.version
          : version // ignore: cast_nullable_to_non_nullable
              as String?,
      originTemplateId: freezed == originTemplateId
          ? _self.originTemplateId
          : originTemplateId // ignore: cast_nullable_to_non_nullable
              as String?,
      isEdited: null == isEdited
          ? _self.isEdited
          : isEdited // ignore: cast_nullable_to_non_nullable
              as TemplateEditStatus,
      contentThumbnail: null == contentThumbnail
          ? _self.contentThumbnail
          : contentThumbnail // ignore: cast_nullable_to_non_nullable
              as String,
      localContentThumb: null == localContentThumb
          ? _self.localContentThumb
          : localContentThumb // ignore: cast_nullable_to_non_nullable
              as String,
      business: freezed == business
          ? _self.business
          : business // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      currentPageIndex: null == currentPageIndex
          ? _self.currentPageIndex
          : currentPageIndex // ignore: cast_nullable_to_non_nullable
              as int,
      dataSources: freezed == dataSources
          ? _self.dataSources
          : dataSources // ignore: cast_nullable_to_non_nullable
              as List<TemplateDataSource>?,
      dataSourceBindInfo: freezed == dataSourceBindInfo
          ? _self.dataSourceBindInfo
          : dataSourceBindInfo // ignore: cast_nullable_to_non_nullable
              as TemplateDataSourceInfo?,
      dataSourceModifies: freezed == dataSourceModifies
          ? _self.dataSourceModifies
          : dataSourceModifies // ignore: cast_nullable_to_non_nullable
              as Map<String, Map<String, TemplateDataSourceModify>>?,
      templateVersion: freezed == templateVersion
          ? _self.templateVersion
          : templateVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      goodsData: freezed == goodsData
          ? _self.goodsData
          : goodsData // ignore: cast_nullable_to_non_nullable
              as List<GoodsModel>?,
      profile: freezed == profile
          ? _self.profile!
          : profile // ignore: cast_nullable_to_non_nullable
              as TemplateProfile?,
      dataBindingMode: freezed == dataBindingMode
          ? _self.dataBindingMode
          : dataBindingMode // ignore: cast_nullable_to_non_nullable
              as String?,
      similarity: null == similarity
          ? _self.similarity
          : similarity // ignore: cast_nullable_to_non_nullable
              as String,
      goodsIds: freezed == goodsIds
          ? _self.goodsIds
          : goodsIds // ignore: cast_nullable_to_non_nullable
              as String?,
      values: freezed == values
          ? _self.values
          : values // ignore: cast_nullable_to_non_nullable
              as List<ElementValue>?,
      templateAttributes: freezed == templateAttributes
          ? _self.templateAttributes
          : templateAttributes // ignore: cast_nullable_to_non_nullable
              as List<TemplateAttributes>?,
      tubeFileSetting: freezed == tubeFileSetting
          ? _self.tubeFileSetting
          : tubeFileSetting // ignore: cast_nullable_to_non_nullable
              as TubeFileSetting?,
      fileDataSources: freezed == fileDataSources
          ? _self.fileDataSources
          : fileDataSources // ignore: cast_nullable_to_non_nullable
              as List<FileDataSource>?,
    ));
  }
}

// dart format on
