class FontSizeConfig {
  /// 小一
  final String title;

  /// 字号
  final double fontSize;

  /// mm
  final double mm;

  const FontSizeConfig(this.title, this.fontSize, this.mm);
}

extension ClosestValueExtension on List<FontSizeConfig> {
  int getClosestValueIndex(double target) {
    int low = 0;
    int high = length - 1;
    int closestIndex = -1;

    while (low <= high) {
      int mid = (low + high) ~/ 2;
      double midValue = this[mid].mm;

      if (midValue == target) {
        closestIndex = mid;
        break;
      }

      if (midValue < target) {
        closestIndex = mid;
        low = mid + 1;
      } else {
        high = mid - 1;
      }
    }
    // 检查前一个 double 是否更接近
    if (closestIndex > 0 &&
        target - this[closestIndex - 1].mm < this[closestIndex].mm - target) {
      closestIndex -= 1;
    }

    return closestIndex;
  }
}
