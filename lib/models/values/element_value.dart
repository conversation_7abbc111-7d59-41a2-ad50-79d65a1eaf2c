import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/utils/element_utils.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

part 'element_value.freezed.dart';
part 'element_value.g.dart';

@Freezed(unionKey: 'type')
sealed class ElementValue with _$ElementValue {
  @override
  final String id;
  @override
  final String value;

  @override
  final String elementId;

  ElementValue._({String? id, final String? value, final String? elementId})
    : id = id ?? ElementUtils.generateId(),
      value = value ?? '',
      elementId = elementId ?? '';

  factory ElementValue.fromJson(Map<String, dynamic> json) =>
      _$ElementValueFromJson(json);

  /// 文本值
  factory ElementValue.text({
    ///唯一标识
    final String? id,

    /// 对应netalJson元素的唯一标识
    required final String elementId,

    /// 字面值
    required final String value,
  }) = TextValue;

  /// 条形码值
  factory ElementValue.barcode({
    ///唯一标识
    final String? id,

    /// 对应netalJson元素的唯一标识
    required final String elementId,

    /// 字面值
    required final String value,
  }) = BarcodeValue;

  /// 组合值
  factory ElementValue.composite({
    ///唯一标识
    final String? id,

    /// 对应netalJson元素的唯一标识
    required final String elementId,
    required final List<ElementValue> valueObjects,
    required final String delimiter,

    /// 重复次数
    @Default(1) final int repeatCount,

    /// 是否自动宽度
    final ParagraphOverflowEnum? overflowType,

    /// 应用全部段落
    @Default(false) final bool applyAllParagraph,
  }) = CompositeValue;

  /// 数据源值
  factory ElementValue.dText({
    ///唯一标识
    final String? id,

    /// 对应netalJson元素的唯一标识
    required final String elementId,

    /// 字面值
    required final String value,

    /// 数据源标识,sheet名/表名（对应数据绑定规范）
    required final List<String> dataBind,
  }) = DataSourceTextValue;

  /// 日期值
  factory ElementValue.date({
    ///唯一标识
    final String? id,

    /// 对应netalJson元素的唯一标识
    required final String elementId,

    /// 日期格式化公式
    final String? dateFormat,

    /// 时间格式化公式
    final String? timeFormat,

    /// value是否为当前时间
    // @Default(false) final bool dateIsRefresh,

    /// 是否使用24小时制
    @Default(false) final bool militaryTime,

    /// 前缀
    final String? prefix,

    /// 时间戳 不传时为当前时间
    final int? time,

    /// 时间偏移量 单位：分钟
    @Default(0) final int timeOffset,
  }) = DateValue;

  /// 图片值
  factory ElementValue.image({
    ///唯一标识
    final String? id,

    /// 对应netalJson元素的唯一标识
    required final String elementId,

    /// 字面值
    required final String value,
  }) = ImageValue;

  /// 二维码
  factory ElementValue.qrcode({
    ///唯一标识
    final String? id,

    /// 对应netalJson元素的唯一标识
    required final String elementId,

    /// 字面值
    required final String value,
  }) = QRCodeValue;

  /// 序列号
  factory ElementValue.serial({
    ///唯一标识
    final String? id,

    /// 对应netalJson元素的唯一标识
    required final String elementId,

    /// 前缀
    final String? prefix,

    /// 后缀
    final String? suffix,

    /// 起始值
    @JsonKey(fromJson: _parseStartNumber)
    final int? startNumber,

    /// 补零
    @JsonKey(fromJson: _parseFixValue)
    final int? fixValue,

    /// 补位
    final int? fixLength,

    /// 增量
    required final int incrementValue,

    /// 格式
    final SerialElementFormat? format,

    /// 结束值
    final int? endNumber,
  }) = SerialValue;
}

/// 解析起始值
int? _parseStartNumber(dynamic field) {
  final val = TemplateParseUtils.parseNumberFromJSON(field);
  if (val != null) return val.toInt();
  return null;
}

/// 解析补零
int? _parseFixValue(dynamic field) {
  final val = TemplateParseUtils.parseNumberFromJSON(field);
  if (val != null) return val.toInt();
  return null;
}
