import 'package:intl/intl.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/values/element_value.dart';
import 'package:niimbot_template/utils/element_utils.dart';
import 'package:niimbot_template/utils/template_data_source_utils.dart';

extension ElementValueExtensions on ElementValue {
  // 序号排列组合后总段数
  int seriesMaxLength() {
    int maxLength = 1;
    for (var element in (this as CompositeValue).valueObjects) {
      if (element is SerialValue) {
        final start = element.startNumber;
        final end = element.endNumber;
        final length =
            start != null && end != null
                ? ((end - start) / element.incrementValue).floor() + 1
                : 0;
        maxLength = maxLength * length;
      }
    }
    return maxLength;
  }

  // 多序号排列组合生成数据
  List<String> generateCombinations(
    CompositeValue compositeValue,
    List<TemplateDataSource>? dataSources,
    TemplateDataSourceModifies? modify,
  ) {
    final List<List<String>> data = compositeValue.valueObjects.fold([], (
      res,
      e,
    ) {
      int length = 1;
      if (e is SerialValue) {
        final start = e.startNumber;
        final end = e.endNumber;
        length =
            start != null && end != null
                ? ((end - start) / e.incrementValue).floor() + 1
                : 1;
      }
      final List<String> result = [];
      for (int i = 0; i < length; i++) {
        result.add(
          e.buildValue(dataSources: dataSources, modify: modify, row: i + 1),
        );
      }
      res.add(result);
      return res;
    });
    if (data.isEmpty) return [];
    List<String> result = [];

    void helper(List<String> current, int index) {
      if (index == data.length) {
        result.add(current.join((this as CompositeValue).delimiter));
        return;
      }

      for (String item in data[index]) {
        helper([...current, item], index + 1);
      }
    }

    helper([], 0);
    return result;
  }

  String buildValue({
    List<TemplateDataSource>? dataSources,
    TemplateDataSourceModifies? modify,
    int row = 1,
  }) {
    if (this is CompositeValue) {
      final List<String> data = generateCombinations(
        (this as CompositeValue),
        dataSources,
        modify,
      );
      return data.isNotEmpty ? data[row - 1] : '';
    }
    if (this is DataSourceTextValue) {
      return TemplateDataSourceUtils.getElementBindValue(
        id,
        value,
        (this as DataSourceTextValue).dataBind,
        dataSources,
        modify,
        row,
      );
    }
    if (this is DateValue) {
      return ElementUtils.buildDateValue(
        time: (this as DateValue).time,
        timeOffset: (this as DateValue).timeOffset,
        dateFormat: (this as DateValue).dateFormat,
        timeFormat: (this as DateValue).timeFormat,
        militaryTime: (this as DateValue).militaryTime,
        prefix: (this as DateValue).prefix,
      );
    }
    if (this is SerialValue) {
      NumberFormat();
      return ElementUtils.buildSerialValue(
        startNumber: (this as SerialValue).startNumber.toString(),
        fixValue: (this as SerialValue).fixValue.toString(),
        fixLength: (this as SerialValue).fixLength,
        increment: (this as SerialValue).incrementValue,
        format: (this as SerialValue).format,
        prefix: (this as SerialValue).prefix,
        suffix: (this as SerialValue).suffix,
        index: row,
        endNumber: (this as SerialValue).endNumber,
      );
    }
    return value;
  }
}
