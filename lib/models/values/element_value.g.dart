// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'element_value.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TextValue _$TextValueFromJson(Map<String, dynamic> json) => TextValue(
      id: json['id'] as String?,
      elementId: json['elementId'] as String,
      value: json['value'] as String,
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$TextValueToJson(TextValue instance) => <String, dynamic>{
      'id': instance.id,
      'value': instance.value,
      'elementId': instance.elementId,
      'type': instance.$type,
    };

BarcodeValue _$BarcodeValueFromJson(Map<String, dynamic> json) => BarcodeValue(
      id: json['id'] as String?,
      elementId: json['elementId'] as String,
      value: json['value'] as String,
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$BarcodeValueToJson(BarcodeValue instance) =>
    <String, dynamic>{
      'id': instance.id,
      'value': instance.value,
      'elementId': instance.elementId,
      'type': instance.$type,
    };

CompositeValue _$CompositeValueFromJson(Map<String, dynamic> json) =>
    CompositeValue(
      id: json['id'] as String?,
      elementId: json['elementId'] as String,
      valueObjects: (json['valueObjects'] as List<dynamic>)
          .map((e) => ElementValue.fromJson(e as Map<String, dynamic>))
          .toList(),
      delimiter: json['delimiter'] as String,
      repeatCount: (json['repeatCount'] as num?)?.toInt() ?? 1,
      overflowType: $enumDecodeNullable(
          _$ParagraphOverflowEnumEnumMap, json['overflowType']),
      applyAllParagraph: json['applyAllParagraph'] as bool? ?? false,
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$CompositeValueToJson(CompositeValue instance) =>
    <String, dynamic>{
      'id': instance.id,
      'elementId': instance.elementId,
      'valueObjects': instance.valueObjects.map((e) => e.toJson()).toList(),
      'delimiter': instance.delimiter,
      'repeatCount': instance.repeatCount,
      'overflowType': _$ParagraphOverflowEnumEnumMap[instance.overflowType],
      'applyAllParagraph': instance.applyAllParagraph,
      'type': instance.$type,
    };

const _$ParagraphOverflowEnumEnumMap = {
  ParagraphOverflowEnum.current: 'current',
  ParagraphOverflowEnum.all: 'all',
};

DataSourceTextValue _$DataSourceTextValueFromJson(Map<String, dynamic> json) =>
    DataSourceTextValue(
      id: json['id'] as String?,
      elementId: json['elementId'] as String,
      value: json['value'] as String,
      dataBind:
          (json['dataBind'] as List<dynamic>).map((e) => e as String).toList(),
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$DataSourceTextValueToJson(
        DataSourceTextValue instance) =>
    <String, dynamic>{
      'id': instance.id,
      'value': instance.value,
      'elementId': instance.elementId,
      'dataBind': instance.dataBind,
      'type': instance.$type,
    };

DateValue _$DateValueFromJson(Map<String, dynamic> json) => DateValue(
      id: json['id'] as String?,
      elementId: json['elementId'] as String,
      dateFormat: json['dateFormat'] as String?,
      timeFormat: json['timeFormat'] as String?,
      militaryTime: json['militaryTime'] as bool? ?? false,
      prefix: json['prefix'] as String?,
      time: (json['time'] as num?)?.toInt(),
      timeOffset: (json['timeOffset'] as num?)?.toInt() ?? 0,
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$DateValueToJson(DateValue instance) => <String, dynamic>{
      'id': instance.id,
      'elementId': instance.elementId,
      'dateFormat': instance.dateFormat,
      'timeFormat': instance.timeFormat,
      'militaryTime': instance.militaryTime,
      'prefix': instance.prefix,
      'time': instance.time,
      'timeOffset': instance.timeOffset,
      'type': instance.$type,
    };

ImageValue _$ImageValueFromJson(Map<String, dynamic> json) => ImageValue(
      id: json['id'] as String?,
      elementId: json['elementId'] as String,
      value: json['value'] as String,
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$ImageValueToJson(ImageValue instance) =>
    <String, dynamic>{
      'id': instance.id,
      'value': instance.value,
      'elementId': instance.elementId,
      'type': instance.$type,
    };

QRCodeValue _$QRCodeValueFromJson(Map<String, dynamic> json) => QRCodeValue(
      id: json['id'] as String?,
      elementId: json['elementId'] as String,
      value: json['value'] as String,
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$QRCodeValueToJson(QRCodeValue instance) =>
    <String, dynamic>{
      'id': instance.id,
      'value': instance.value,
      'elementId': instance.elementId,
      'type': instance.$type,
    };

SerialValue _$SerialValueFromJson(Map<String, dynamic> json) => SerialValue(
      id: json['id'] as String?,
      elementId: json['elementId'] as String,
      prefix: json['prefix'] as String?,
      suffix: json['suffix'] as String?,
      startNumber: _parseStartNumber(json['startNumber']),
      fixValue: _parseFixValue(json['fixValue']),
      fixLength: (json['fixLength'] as num?)?.toInt(),
      incrementValue: (json['incrementValue'] as num).toInt(),
      format: $enumDecodeNullable(_$SerialElementFormatEnumMap, json['format']),
      endNumber: (json['endNumber'] as num?)?.toInt(),
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$SerialValueToJson(SerialValue instance) =>
    <String, dynamic>{
      'id': instance.id,
      'elementId': instance.elementId,
      'prefix': instance.prefix,
      'suffix': instance.suffix,
      'startNumber': instance.startNumber,
      'fixValue': instance.fixValue,
      'fixLength': instance.fixLength,
      'incrementValue': instance.incrementValue,
      'format': _$SerialElementFormatEnumMap[instance.format],
      'endNumber': instance.endNumber,
      'type': instance.$type,
    };

const _$SerialElementFormatEnumMap = {
  SerialElementFormat.capitalLetter: 'capitalLetter',
  SerialElementFormat.smallLetter: 'smallLetter',
  SerialElementFormat.digitRepeat: 'digitRepeat',
  SerialElementFormat.capitalLetterRepeat: 'capitalLetterRepeat',
  SerialElementFormat.smallLetterRepeat: 'smallLetterRepeat',
};
