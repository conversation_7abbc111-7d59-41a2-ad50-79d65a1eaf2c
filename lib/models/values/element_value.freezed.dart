// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'element_value.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
ElementValue _$ElementValueFromJson(Map<String, dynamic> json) {
  switch (json['type']) {
    case 'text':
      return TextValue.fromJson(json);
    case 'barcode':
      return BarcodeValue.fromJson(json);
    case 'composite':
      return CompositeValue.fromJson(json);
    case 'dText':
      return DataSourceTextValue.fromJson(json);
    case 'date':
      return DateValue.fromJson(json);
    case 'image':
      return ImageValue.fromJson(json);
    case 'qrcode':
      return QRCodeValue.fromJson(json);
    case 'serial':
      return SerialValue.fromJson(json);

    default:
      throw CheckedFromJsonException(json, 'type', 'ElementValue',
          'Invalid union type "${json['type']}"!');
  }
}

/// @nodoc
mixin _$ElementValue {
  String get id;
  String get value;
  String get elementId;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ElementValueCopyWith<ElementValue> get copyWith =>
      _$ElementValueCopyWithImpl<ElementValue>(
          this as ElementValue, _$identity);

  /// Serializes this ElementValue to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ElementValue &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.elementId, elementId) ||
                other.elementId == elementId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, value, elementId);

  @override
  String toString() {
    return 'ElementValue(id: $id, value: $value, elementId: $elementId)';
  }
}

/// @nodoc
abstract mixin class $ElementValueCopyWith<$Res> {
  factory $ElementValueCopyWith(
          ElementValue value, $Res Function(ElementValue) _then) =
      _$ElementValueCopyWithImpl;
  @useResult
  $Res call({String? id, String elementId});
}

/// @nodoc
class _$ElementValueCopyWithImpl<$Res> implements $ElementValueCopyWith<$Res> {
  _$ElementValueCopyWithImpl(this._self, this._then);

  final ElementValue _self;
  final $Res Function(ElementValue) _then;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? elementId = null,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id!
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      elementId: null == elementId
          ? _self.elementId
          : elementId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class TextValue extends ElementValue {
  TextValue(
      {final String? id,
      required final String elementId,
      required final String value,
      final String? $type})
      : $type = $type ?? 'text',
        super._(id: id, value: value, elementId: elementId);
  factory TextValue.fromJson(Map<String, dynamic> json) =>
      _$TextValueFromJson(json);

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TextValueCopyWith<TextValue> get copyWith =>
      _$TextValueCopyWithImpl<TextValue>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TextValueToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TextValue &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.elementId, elementId) ||
                other.elementId == elementId) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, elementId, value);

  @override
  String toString() {
    return 'ElementValue.text(id: $id, elementId: $elementId, value: $value)';
  }
}

/// @nodoc
abstract mixin class $TextValueCopyWith<$Res>
    implements $ElementValueCopyWith<$Res> {
  factory $TextValueCopyWith(TextValue value, $Res Function(TextValue) _then) =
      _$TextValueCopyWithImpl;
  @override
  @useResult
  $Res call({String? id, String elementId, String value});
}

/// @nodoc
class _$TextValueCopyWithImpl<$Res> implements $TextValueCopyWith<$Res> {
  _$TextValueCopyWithImpl(this._self, this._then);

  final TextValue _self;
  final $Res Function(TextValue) _then;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? elementId = null,
    Object? value = null,
  }) {
    return _then(TextValue(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      elementId: null == elementId
          ? _self.elementId
          : elementId // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class BarcodeValue extends ElementValue {
  BarcodeValue(
      {final String? id,
      required final String elementId,
      required final String value,
      final String? $type})
      : $type = $type ?? 'barcode',
        super._(id: id, value: value, elementId: elementId);
  factory BarcodeValue.fromJson(Map<String, dynamic> json) =>
      _$BarcodeValueFromJson(json);

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $BarcodeValueCopyWith<BarcodeValue> get copyWith =>
      _$BarcodeValueCopyWithImpl<BarcodeValue>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$BarcodeValueToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is BarcodeValue &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.elementId, elementId) ||
                other.elementId == elementId) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, elementId, value);

  @override
  String toString() {
    return 'ElementValue.barcode(id: $id, elementId: $elementId, value: $value)';
  }
}

/// @nodoc
abstract mixin class $BarcodeValueCopyWith<$Res>
    implements $ElementValueCopyWith<$Res> {
  factory $BarcodeValueCopyWith(
          BarcodeValue value, $Res Function(BarcodeValue) _then) =
      _$BarcodeValueCopyWithImpl;
  @override
  @useResult
  $Res call({String? id, String elementId, String value});
}

/// @nodoc
class _$BarcodeValueCopyWithImpl<$Res> implements $BarcodeValueCopyWith<$Res> {
  _$BarcodeValueCopyWithImpl(this._self, this._then);

  final BarcodeValue _self;
  final $Res Function(BarcodeValue) _then;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? elementId = null,
    Object? value = null,
  }) {
    return _then(BarcodeValue(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      elementId: null == elementId
          ? _self.elementId
          : elementId // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class CompositeValue extends ElementValue {
  CompositeValue(
      {final String? id,
      required final String elementId,
      required final List<ElementValue> valueObjects,
      required this.delimiter,
      this.repeatCount = 1,
      this.overflowType,
      this.applyAllParagraph = false,
      final String? $type})
      : _valueObjects = valueObjects,
        $type = $type ?? 'composite',
        super._(id: id, elementId: elementId);
  factory CompositeValue.fromJson(Map<String, dynamic> json) =>
      _$CompositeValueFromJson(json);

  final List<ElementValue> _valueObjects;
  List<ElementValue> get valueObjects {
    if (_valueObjects is EqualUnmodifiableListView) return _valueObjects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_valueObjects);
  }

  final String delimiter;

  /// 重复次数
  @JsonKey()
  final int repeatCount;

  /// 是否自动宽度
  final ParagraphOverflowEnum? overflowType;

  /// 应用全部段落
  @JsonKey()
  final bool applyAllParagraph;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CompositeValueCopyWith<CompositeValue> get copyWith =>
      _$CompositeValueCopyWithImpl<CompositeValue>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CompositeValueToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CompositeValue &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.elementId, elementId) ||
                other.elementId == elementId) &&
            const DeepCollectionEquality()
                .equals(other._valueObjects, _valueObjects) &&
            (identical(other.delimiter, delimiter) ||
                other.delimiter == delimiter) &&
            (identical(other.repeatCount, repeatCount) ||
                other.repeatCount == repeatCount) &&
            (identical(other.overflowType, overflowType) ||
                other.overflowType == overflowType) &&
            (identical(other.applyAllParagraph, applyAllParagraph) ||
                other.applyAllParagraph == applyAllParagraph));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      elementId,
      const DeepCollectionEquality().hash(_valueObjects),
      delimiter,
      repeatCount,
      overflowType,
      applyAllParagraph);

  @override
  String toString() {
    return 'ElementValue.composite(id: $id, elementId: $elementId, valueObjects: $valueObjects, delimiter: $delimiter, repeatCount: $repeatCount, overflowType: $overflowType, applyAllParagraph: $applyAllParagraph)';
  }
}

/// @nodoc
abstract mixin class $CompositeValueCopyWith<$Res>
    implements $ElementValueCopyWith<$Res> {
  factory $CompositeValueCopyWith(
          CompositeValue value, $Res Function(CompositeValue) _then) =
      _$CompositeValueCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? id,
      String elementId,
      List<ElementValue> valueObjects,
      String delimiter,
      int repeatCount,
      ParagraphOverflowEnum? overflowType,
      bool applyAllParagraph});
}

/// @nodoc
class _$CompositeValueCopyWithImpl<$Res>
    implements $CompositeValueCopyWith<$Res> {
  _$CompositeValueCopyWithImpl(this._self, this._then);

  final CompositeValue _self;
  final $Res Function(CompositeValue) _then;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? elementId = null,
    Object? valueObjects = null,
    Object? delimiter = null,
    Object? repeatCount = null,
    Object? overflowType = freezed,
    Object? applyAllParagraph = null,
  }) {
    return _then(CompositeValue(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      elementId: null == elementId
          ? _self.elementId
          : elementId // ignore: cast_nullable_to_non_nullable
              as String,
      valueObjects: null == valueObjects
          ? _self._valueObjects
          : valueObjects // ignore: cast_nullable_to_non_nullable
              as List<ElementValue>,
      delimiter: null == delimiter
          ? _self.delimiter
          : delimiter // ignore: cast_nullable_to_non_nullable
              as String,
      repeatCount: null == repeatCount
          ? _self.repeatCount
          : repeatCount // ignore: cast_nullable_to_non_nullable
              as int,
      overflowType: freezed == overflowType
          ? _self.overflowType
          : overflowType // ignore: cast_nullable_to_non_nullable
              as ParagraphOverflowEnum?,
      applyAllParagraph: null == applyAllParagraph
          ? _self.applyAllParagraph
          : applyAllParagraph // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class DataSourceTextValue extends ElementValue {
  DataSourceTextValue(
      {final String? id,
      required final String elementId,
      required final String value,
      required final List<String> dataBind,
      final String? $type})
      : _dataBind = dataBind,
        $type = $type ?? 'dText',
        super._(id: id, value: value, elementId: elementId);
  factory DataSourceTextValue.fromJson(Map<String, dynamic> json) =>
      _$DataSourceTextValueFromJson(json);

  /// 数据源标识,sheet名/表名（对应数据绑定规范）
  final List<String> _dataBind;

  /// 数据源标识,sheet名/表名（对应数据绑定规范）
  List<String> get dataBind {
    if (_dataBind is EqualUnmodifiableListView) return _dataBind;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dataBind);
  }

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DataSourceTextValueCopyWith<DataSourceTextValue> get copyWith =>
      _$DataSourceTextValueCopyWithImpl<DataSourceTextValue>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DataSourceTextValueToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DataSourceTextValue &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.elementId, elementId) ||
                other.elementId == elementId) &&
            (identical(other.value, value) || other.value == value) &&
            const DeepCollectionEquality().equals(other._dataBind, _dataBind));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, elementId, value,
      const DeepCollectionEquality().hash(_dataBind));

  @override
  String toString() {
    return 'ElementValue.dText(id: $id, elementId: $elementId, value: $value, dataBind: $dataBind)';
  }
}

/// @nodoc
abstract mixin class $DataSourceTextValueCopyWith<$Res>
    implements $ElementValueCopyWith<$Res> {
  factory $DataSourceTextValueCopyWith(
          DataSourceTextValue value, $Res Function(DataSourceTextValue) _then) =
      _$DataSourceTextValueCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? id, String elementId, String value, List<String> dataBind});
}

/// @nodoc
class _$DataSourceTextValueCopyWithImpl<$Res>
    implements $DataSourceTextValueCopyWith<$Res> {
  _$DataSourceTextValueCopyWithImpl(this._self, this._then);

  final DataSourceTextValue _self;
  final $Res Function(DataSourceTextValue) _then;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? elementId = null,
    Object? value = null,
    Object? dataBind = null,
  }) {
    return _then(DataSourceTextValue(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      elementId: null == elementId
          ? _self.elementId
          : elementId // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      dataBind: null == dataBind
          ? _self._dataBind
          : dataBind // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class DateValue extends ElementValue {
  DateValue(
      {final String? id,
      required final String elementId,
      this.dateFormat,
      this.timeFormat,
      this.militaryTime = false,
      this.prefix,
      this.time,
      this.timeOffset = 0,
      final String? $type})
      : $type = $type ?? 'date',
        super._(id: id, elementId: elementId);
  factory DateValue.fromJson(Map<String, dynamic> json) =>
      _$DateValueFromJson(json);

  /// 日期格式化公式
  final String? dateFormat;

  /// 时间格式化公式
  final String? timeFormat;

  /// value是否为当前时间
// @Default(false) final bool dateIsRefresh,
  /// 是否使用24小时制
  @JsonKey()
  final bool militaryTime;

  /// 前缀
  final String? prefix;

  /// 时间戳 不传时为当前时间
  final int? time;

  /// 时间偏移量 单位：分钟
  @JsonKey()
  final int timeOffset;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DateValueCopyWith<DateValue> get copyWith =>
      _$DateValueCopyWithImpl<DateValue>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DateValueToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DateValue &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.elementId, elementId) ||
                other.elementId == elementId) &&
            (identical(other.dateFormat, dateFormat) ||
                other.dateFormat == dateFormat) &&
            (identical(other.timeFormat, timeFormat) ||
                other.timeFormat == timeFormat) &&
            (identical(other.militaryTime, militaryTime) ||
                other.militaryTime == militaryTime) &&
            (identical(other.prefix, prefix) || other.prefix == prefix) &&
            (identical(other.time, time) || other.time == time) &&
            (identical(other.timeOffset, timeOffset) ||
                other.timeOffset == timeOffset));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, elementId, dateFormat,
      timeFormat, militaryTime, prefix, time, timeOffset);

  @override
  String toString() {
    return 'ElementValue.date(id: $id, elementId: $elementId, dateFormat: $dateFormat, timeFormat: $timeFormat, militaryTime: $militaryTime, prefix: $prefix, time: $time, timeOffset: $timeOffset)';
  }
}

/// @nodoc
abstract mixin class $DateValueCopyWith<$Res>
    implements $ElementValueCopyWith<$Res> {
  factory $DateValueCopyWith(DateValue value, $Res Function(DateValue) _then) =
      _$DateValueCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? id,
      String elementId,
      String? dateFormat,
      String? timeFormat,
      bool militaryTime,
      String? prefix,
      int? time,
      int timeOffset});
}

/// @nodoc
class _$DateValueCopyWithImpl<$Res> implements $DateValueCopyWith<$Res> {
  _$DateValueCopyWithImpl(this._self, this._then);

  final DateValue _self;
  final $Res Function(DateValue) _then;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? elementId = null,
    Object? dateFormat = freezed,
    Object? timeFormat = freezed,
    Object? militaryTime = null,
    Object? prefix = freezed,
    Object? time = freezed,
    Object? timeOffset = null,
  }) {
    return _then(DateValue(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      elementId: null == elementId
          ? _self.elementId
          : elementId // ignore: cast_nullable_to_non_nullable
              as String,
      dateFormat: freezed == dateFormat
          ? _self.dateFormat
          : dateFormat // ignore: cast_nullable_to_non_nullable
              as String?,
      timeFormat: freezed == timeFormat
          ? _self.timeFormat
          : timeFormat // ignore: cast_nullable_to_non_nullable
              as String?,
      militaryTime: null == militaryTime
          ? _self.militaryTime
          : militaryTime // ignore: cast_nullable_to_non_nullable
              as bool,
      prefix: freezed == prefix
          ? _self.prefix
          : prefix // ignore: cast_nullable_to_non_nullable
              as String?,
      time: freezed == time
          ? _self.time
          : time // ignore: cast_nullable_to_non_nullable
              as int?,
      timeOffset: null == timeOffset
          ? _self.timeOffset
          : timeOffset // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class ImageValue extends ElementValue {
  ImageValue(
      {final String? id,
      required final String elementId,
      required final String value,
      final String? $type})
      : $type = $type ?? 'image',
        super._(id: id, value: value, elementId: elementId);
  factory ImageValue.fromJson(Map<String, dynamic> json) =>
      _$ImageValueFromJson(json);

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ImageValueCopyWith<ImageValue> get copyWith =>
      _$ImageValueCopyWithImpl<ImageValue>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ImageValueToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ImageValue &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.elementId, elementId) ||
                other.elementId == elementId) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, elementId, value);

  @override
  String toString() {
    return 'ElementValue.image(id: $id, elementId: $elementId, value: $value)';
  }
}

/// @nodoc
abstract mixin class $ImageValueCopyWith<$Res>
    implements $ElementValueCopyWith<$Res> {
  factory $ImageValueCopyWith(
          ImageValue value, $Res Function(ImageValue) _then) =
      _$ImageValueCopyWithImpl;
  @override
  @useResult
  $Res call({String? id, String elementId, String value});
}

/// @nodoc
class _$ImageValueCopyWithImpl<$Res> implements $ImageValueCopyWith<$Res> {
  _$ImageValueCopyWithImpl(this._self, this._then);

  final ImageValue _self;
  final $Res Function(ImageValue) _then;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? elementId = null,
    Object? value = null,
  }) {
    return _then(ImageValue(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      elementId: null == elementId
          ? _self.elementId
          : elementId // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class QRCodeValue extends ElementValue {
  QRCodeValue(
      {final String? id,
      required final String elementId,
      required final String value,
      final String? $type})
      : $type = $type ?? 'qrcode',
        super._(id: id, value: value, elementId: elementId);
  factory QRCodeValue.fromJson(Map<String, dynamic> json) =>
      _$QRCodeValueFromJson(json);

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QRCodeValueCopyWith<QRCodeValue> get copyWith =>
      _$QRCodeValueCopyWithImpl<QRCodeValue>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QRCodeValueToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QRCodeValue &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.elementId, elementId) ||
                other.elementId == elementId) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, elementId, value);

  @override
  String toString() {
    return 'ElementValue.qrcode(id: $id, elementId: $elementId, value: $value)';
  }
}

/// @nodoc
abstract mixin class $QRCodeValueCopyWith<$Res>
    implements $ElementValueCopyWith<$Res> {
  factory $QRCodeValueCopyWith(
          QRCodeValue value, $Res Function(QRCodeValue) _then) =
      _$QRCodeValueCopyWithImpl;
  @override
  @useResult
  $Res call({String? id, String elementId, String value});
}

/// @nodoc
class _$QRCodeValueCopyWithImpl<$Res> implements $QRCodeValueCopyWith<$Res> {
  _$QRCodeValueCopyWithImpl(this._self, this._then);

  final QRCodeValue _self;
  final $Res Function(QRCodeValue) _then;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? elementId = null,
    Object? value = null,
  }) {
    return _then(QRCodeValue(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      elementId: null == elementId
          ? _self.elementId
          : elementId // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class SerialValue extends ElementValue {
  SerialValue(
      {final String? id,
      required final String elementId,
      this.prefix,
      this.suffix,
      @JsonKey(fromJson: _parseStartNumber) this.startNumber,
      @JsonKey(fromJson: _parseFixValue) this.fixValue,
      this.fixLength,
      required this.incrementValue,
      this.format,
      this.endNumber,
      final String? $type})
      : $type = $type ?? 'serial',
        super._(id: id, elementId: elementId);
  factory SerialValue.fromJson(Map<String, dynamic> json) =>
      _$SerialValueFromJson(json);

  /// 前缀
  final String? prefix;

  /// 后缀
  final String? suffix;

  /// 起始值
  @JsonKey(fromJson: _parseStartNumber)
  final int? startNumber;

  /// 补零
  @JsonKey(fromJson: _parseFixValue)
  final int? fixValue;

  /// 补位
  final int? fixLength;

  /// 增量
  final int incrementValue;

  /// 格式
  final SerialElementFormat? format;

  /// 结束值
  final int? endNumber;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SerialValueCopyWith<SerialValue> get copyWith =>
      _$SerialValueCopyWithImpl<SerialValue>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SerialValueToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SerialValue &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.elementId, elementId) ||
                other.elementId == elementId) &&
            (identical(other.prefix, prefix) || other.prefix == prefix) &&
            (identical(other.suffix, suffix) || other.suffix == suffix) &&
            (identical(other.startNumber, startNumber) ||
                other.startNumber == startNumber) &&
            (identical(other.fixValue, fixValue) ||
                other.fixValue == fixValue) &&
            (identical(other.fixLength, fixLength) ||
                other.fixLength == fixLength) &&
            (identical(other.incrementValue, incrementValue) ||
                other.incrementValue == incrementValue) &&
            (identical(other.format, format) || other.format == format) &&
            (identical(other.endNumber, endNumber) ||
                other.endNumber == endNumber));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, elementId, prefix, suffix,
      startNumber, fixValue, fixLength, incrementValue, format, endNumber);

  @override
  String toString() {
    return 'ElementValue.serial(id: $id, elementId: $elementId, prefix: $prefix, suffix: $suffix, startNumber: $startNumber, fixValue: $fixValue, fixLength: $fixLength, incrementValue: $incrementValue, format: $format, endNumber: $endNumber)';
  }
}

/// @nodoc
abstract mixin class $SerialValueCopyWith<$Res>
    implements $ElementValueCopyWith<$Res> {
  factory $SerialValueCopyWith(
          SerialValue value, $Res Function(SerialValue) _then) =
      _$SerialValueCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? id,
      String elementId,
      String? prefix,
      String? suffix,
      @JsonKey(fromJson: _parseStartNumber) int? startNumber,
      @JsonKey(fromJson: _parseFixValue) int? fixValue,
      int? fixLength,
      int incrementValue,
      SerialElementFormat? format,
      int? endNumber});
}

/// @nodoc
class _$SerialValueCopyWithImpl<$Res> implements $SerialValueCopyWith<$Res> {
  _$SerialValueCopyWithImpl(this._self, this._then);

  final SerialValue _self;
  final $Res Function(SerialValue) _then;

  /// Create a copy of ElementValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? elementId = null,
    Object? prefix = freezed,
    Object? suffix = freezed,
    Object? startNumber = freezed,
    Object? fixValue = freezed,
    Object? fixLength = freezed,
    Object? incrementValue = null,
    Object? format = freezed,
    Object? endNumber = freezed,
  }) {
    return _then(SerialValue(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      elementId: null == elementId
          ? _self.elementId
          : elementId // ignore: cast_nullable_to_non_nullable
              as String,
      prefix: freezed == prefix
          ? _self.prefix
          : prefix // ignore: cast_nullable_to_non_nullable
              as String?,
      suffix: freezed == suffix
          ? _self.suffix
          : suffix // ignore: cast_nullable_to_non_nullable
              as String?,
      startNumber: freezed == startNumber
          ? _self.startNumber
          : startNumber // ignore: cast_nullable_to_non_nullable
              as int?,
      fixValue: freezed == fixValue
          ? _self.fixValue
          : fixValue // ignore: cast_nullable_to_non_nullable
              as int?,
      fixLength: freezed == fixLength
          ? _self.fixLength
          : fixLength // ignore: cast_nullable_to_non_nullable
              as int?,
      incrementValue: null == incrementValue
          ? _self.incrementValue
          : incrementValue // ignore: cast_nullable_to_non_nullable
              as int,
      format: freezed == format
          ? _self.format
          : format // ignore: cast_nullable_to_non_nullable
              as SerialElementFormat?,
      endNumber: freezed == endNumber
          ? _self.endNumber
          : endNumber // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

// dart format on
