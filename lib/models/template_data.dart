import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/plugin/goods_model.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/elements/table_cell_element.dart';
import 'package:niimbot_template/models/elements/table_combine_cell_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/template/constants.dart';
import 'package:niimbot_template/models/template/file_data_source.dart';
import 'package:niimbot_template/models/template/label_name_info.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_info.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template/template_enum.dart';
import 'package:niimbot_template/models/template/template_profile.dart';
import 'package:niimbot_template/models/template/tube_file_setting.dart';
import 'package:niimbot_template/models/values/element_value.dart';
import 'package:niimbot_template/utils/platform_utils.dart';

part 'template_data.freezed.dart';

final Logger _logger = Logger("TemplateData", on: kDebugMode);

/// 1.excel 列绑定信息刷新，将原有的 value "${0👁2}" 转换到 isBinding = 1  bindingColumn = 2
/// int isBinding;
/// int bindingColumn;

/// 2.固定的镜像 id 值，将原有的镜像 id 刷新到新的规则
/// String generateFixedMirrorId() {
///   return id.split('').reversed.join();
/// }

/// 3. 新增 dataBindingMode 和 goodsIds 字段
/// 外部数据模式, 参见 [DataBindingMode]
/// excel: Excel 导入
/// goods: 商品库导入
/// String dataBindingMode;
/// 已导入的商品列表
/// String goodsIds;

/// 数据绑定模式
/// excel: Excel 导入
/// goods: 商品库导入
class DataBindingMode {
  static const String none = '';
  static const String excel = 'excel';
  static const String commodity = 'commodity';
}

@freezed
class TemplateData with _$TemplateData {
  ///模板id
  final String? id;

  ///模版相似度
  final String similarity;

  ///云模板id
  final String? cloudTemplateId;

  ///模板名称
  final String name;

  ///模板多语言名称集合
  final List<LabelNameInfo>? names;

  ///标签多语言名称集合
  final List<LabelNameInfo> labelNames;

  ///模板描述
  final String? description;

  ///缩略图url
  final String thumbnail;

  ///背景图url
  final String backgroundImage;

  ///多背景图时的选择索引
  final int multipleBackIndex;

  ///模板宽度
  final num width;

  ///模板高度
  final num height;

  ///出纸方向 向上-0 向右-90 向下-180 向左-270
  final num rotate;

  ///耗材类型
  final int consumableType;

  ///纸张类型
  final int paperType;

  ///是否是线缆标签，0-非线缆标签，1-线缆标签
  final bool isCable;

  ///尾巴长度（耗材类型为标签类，并且为线缆标签时处理该数据）
  final num cableLength;

  ///尾巴方向，0-上，1-右，2-下，3-左
  final NetalCableDirection? cableDirection;

  ///模板上右下左边距
  final List<num> margin;

  ///模板使用到的字体集合
  final Map<String, String> usedFonts;

  ///模板的元素集合
  final List<BaseElement> elements;

  final TemplateProfile profile;

  ///标签纸id
  final String? labelId;

  /// 附加数据：excel 数据源
  // final ExternalData? externalData;

  ///导入Excel的总页码
  final int totalPage;

  /// 辅助变量：excel/商品库 数据绑定场景下 当前页 index, 0 开始
  final int currentPageIndex;

  ///模板或者标签携带的打印模式属性
  final num templatePrintMode;

  ///模板来源，创建和更新时由服务端负责更新，本地只接收不做修改
  final TemplatePlatformCode platformCode;

  ///模板精度 默认打印机点数为203
  final num accuracyName;

  ///是否包含VIP资源
  final bool hasVipRes;

  ///是否VIP模板
  final bool vip;

  ///是否商品模板
  final bool commodityTemplate;

  final String? version;

  // final bool isPrintHistory = false;

  ///画板旋转需求新增字段
  ///标明画板是否经过旋转，如果旋转过，需要手动修改画板背景和预览背景显示方向
  final num canvasRotate;

  ///原模板的id:另存为或从云模板创建
  final String? originTemplateId;

  ///市场运营需求字段，标明是否用户模板可转化为云模板*/
  final TemplateEditStatus isEdited;

  ///模板前景图片*/
  final String contentThumbnail;

  final String localContentThumb;

  final List<String> localBackground;

  final Map<String, dynamic>? business;

  /// 数据绑定模式, 参见 [ExternalDataMode]
  /// excel: Excel 导入
  /// goods: 商品库导入
  final String? dataBindingMode;

  /// 已导入的商品列表
  /// 用户打开已保存的模板时重新拉取最新的商品详情
  /// 需处理已经删除的商品
  final String? goodsIds;

  /// 绑定的商品列表
  final List<GoodsModel>? goodsData;

  /// excel 值修改记录
  // final TemplateTask? task;

  ///excel导入新结构 相关字段 excel修改
  final Map<String, Map<String, TemplateDataSourceModify>>? dataSourceModifies;

  ///excel导入新结构 相关字段 excel数据源
  final List<TemplateDataSource>? dataSources;

  final TemplateDataSourceInfo? dataSourceBindInfo;

  final String? templateVersion;

  /// 模板值集合
  final List<ElementValue>? values;

  final List<TemplateAttributes>? templateAttributes;

  final TubeFileSetting? tubeFileSetting;

  final List<FileDataSource>? fileDataSources;

  TemplateData({
    this.id,
    String? name,
    this.cloudTemplateId,
    this.names,
    this.labelNames = const [],
    this.description,
    String? thumbnail,
    String? backgroundImage,
    int? multipleBackIndex,
    num? width = 50.0,
    num? height = 30.0,
    num? rotate,
    num? canvasRotate,
    int? consumableType,
    int? paperType,
    bool? isCable,
    num? cableLength,
    this.cableDirection,
    List<num>? margin,
    required this.usedFonts,
    List<BaseElement>? elements,
    this.labelId = '',
    this.totalPage = 1,
    this.localBackground = const [],
    this.templatePrintMode = -1,
    TemplatePlatformCode? platformCode,
    num? accuracyName,
    bool? hasVipRes,
    bool? vip,
    bool? commodityTemplate,
    this.version,
    this.originTemplateId,
    this.isEdited = TemplateEditStatus.none,
    this.contentThumbnail = "",
    this.localContentThumb = "",
    this.business,
    this.currentPageIndex = 0,
    this.dataSources,
    this.dataSourceBindInfo,
    this.dataSourceModifies,
    this.templateVersion,
    this.goodsData,
    TemplateProfile? profile,
    this.dataBindingMode,
    this.similarity = '0',
    this.goodsIds,
    this.values,
    this.templateAttributes,
    this.tubeFileSetting,
    this.fileDataSources,
  })  : name = name ?? '',
        thumbnail = thumbnail ?? '',
        backgroundImage = backgroundImage ?? '',
        width = width ?? 50,
        height = height ?? 30,
        profile = profile ?? const TemplateProfile(),
        multipleBackIndex = multipleBackIndex ?? 0,
        rotate = rotate ?? 0,
        canvasRotate = canvasRotate ?? 0,
        consumableType = consumableType ?? 1,
        paperType = paperType ?? 1,
        cableLength = cableLength ?? 0,
        margin = margin ?? const [0, 0, 0, 0],
        elements = elements ?? const [],
        accuracyName = accuracyName ?? 203,
        hasVipRes = hasVipRes ?? false,
        vip = vip ?? false,
        isCable = isCable ?? false,
        commodityTemplate = commodityTemplate ?? false,
        platformCode = platformCode ??
            (PlatformUtils.isDesktop
                ? TemplatePlatformCode.CP001PC
                : TemplatePlatformCode.CP001Mobile);

  Map<String, dynamic> toJson() {
    return ({
      'id': id,
      'name': name,
      'names': names,
      'labelNames': labelNames.map((e) => e.toJson()).toList(),
      'description': description,
      'thumbnail': thumbnail,
      'backgroundImage': backgroundImage,
      'multipleBackIndex': multipleBackIndex,
      'width': width,
      'height': height,
      'rotate': rotate,
      'canvasRotate': canvasRotate,
      'consumableType': consumableType,
      'paperType': paperType,
      'isCable': isCable ? 1 : 0,
      'cableLength': cableLength,
      'cableDirection': cableDirection?.value ?? 0,
      'margin': margin,
      'usedFonts': usedFonts,
      'elements': elements.map((e) => e.toJson()).toList(),
      'platformCode': platformCode.name,
      'paccuracyName': accuracyName,
      'hasVipRes': hasVipRes,
      'vip': vip,
      'commodityTemplate': commodityTemplate,
      'dataSources': dataSources?.map((e) => e.toJson()).toList(),
      'dataSourceModifies': dataSourceModifies?.map(
        (key, value) =>
            MapEntry(key, value.map((k, v) => MapEntry(k, v.toJson()))),
      ),
      'dataSourceBindInfo': dataSourceBindInfo?.toJson(),
      'profile': profile.toJson(),
      'originTemplateId': originTemplateId,
      // 'isEdited': isEdited,
      'cloudTemplateId': cloudTemplateId,
      'templateVersion': TemplateConstants.SAVE_TEMPLATE_VERSION,
      'localBackground': localBackground,
      'fileDataSources': fileDataSources?.fold([], (res, e) {
        if (e.type != 'pdf') {
          res.add(e.toJson());
        }
        return res;
      }),
    })
      ..removeWhere((k, v) => v == null);
  }

  int getCurrentPage() {
    return currentPageIndex + 1;
  }

  /// 是否为商品标签模板
  bool isGoodsLabelTemplate() {
    return dataBindingMode == DataBindingMode.commodity;
  }

  /// 是否为 Excel 绑定模板
  bool isExcelBindingTemplate() {
    return dataBindingMode == DataBindingMode.excel &&
        (dataSources?.isNotEmpty ?? false);
  }

  /// 已绑定列数
  int getBindExcelColumnCount() {
    final bindingJsonElements = elements
        .where((element) => element.isBindingElement)
        .map((e) => e)
        .toList();
    if (bindingJsonElements == null || bindingJsonElements.isEmpty) {
      return 0;
    }
    List<int> columns = [];
    int count = 0;
    for (var item in bindingJsonElements) {
      if (item is TableElement) {
        item.getBindingExcelCells().forEach((cell) {
          int column = cell.bindingColumn;
          if (!columns.contains(column)) {
            count++;
            columns.add(column);
          }
        });
        item.getBindingExcelCombineCells().forEach((cell) {
          int column = cell.bindingColumn;
          if (!columns.contains(column)) {
            count++;
            columns.add(column);
          }
        });
      } else {
        int column = (item as BindElement).bindingColumn;
        if (!columns.contains(column)) {
          count++;
          columns.add(column);
        }
      }
    }
    return count;
  }

  /// 已绑定列数
  int getBindGoodsDataCount() {
    final bindingJsonElements =
        elements.where((e) => e.isBindingElement).map((e) => e).toList();
    if (bindingJsonElements == null || bindingJsonElements.isEmpty) {
      return 0;
    }
    List<int> columns = [];
    int count = 0;
    for (var item in bindingJsonElements) {
      if (item is TableElement) {
        item.getBindingExcelCells().forEach((cell) {
          int column = cell.bindingColumn;
          if (!columns.contains(column)) {
            count++;
            columns.add(column);
          }
        });
        item.getBindingExcelCombineCells().forEach((cell) {
          int column = cell.bindingColumn;
          if (!columns.contains(column)) {
            count++;
            columns.add(column);
          }
        });
      } else {
        int column = (item as BindElement).bindingColumn;
        if (!columns.contains(column)) {
          count++;
          columns.add(column);
        }
      }
    }
    return count;
  }

  bool isBindExcelWithHeader() {
    final bindingJsonElements = elements
        .where((e) {
          final element = e;
          return element is BindElement && element.isBindingElement;
        })
        .map((e) => e)
        .toList();
    if (bindingJsonElements == null || bindingJsonElements.isEmpty) {
      return false;
    }
    for (int i = 0; i < bindingJsonElements.length; i++) {
      final element = bindingJsonElements[i];
      if (element is TextElement) {
        // if (element.isTitle == true || element.contentTitle != null) {
        //   return true;
        // }
        // final dataBindModify = ExcelTransformManager().getElementModify(
        //   elementId: element.id,
        //   elementValue: element.value,
        //   isBindingElement: element.isBindingElement,
        //   isBindingCommodity: element.isBindingCommodity,
        // );
        // if (dataBindModify != null && dataBindModify.useTitle == true) {
        //   return true;
        // }
      }
      if (element is TableElement) {
        List<TableCellElement> cells = element.getBindingExcelCells();
        for (int i = 0; i < cells.length; i++) {
          TableCellElement cell = cells[i];
          // if (cell.isTitle == true || cell.contentTitle != null) {
          //   return true;
          // }
          // final dataBindModify = ExcelTransformManager().getElementModify(
          //   elementId: cell.id,
          //   elementValue: cell.value,
          //   isBindingElement: cell.isBindingElement,
          //   isBindingCommodity: cell.isBindingCommodity,
          // );
          // if (dataBindModify != null && dataBindModify.useTitle == true) {
          //   return true;
          // }
        }

        List<TableCombineCellElement> combineCells =
            element.getBindingExcelCombineCells();
        for (int i = 0; i < combineCells.length; i++) {
          TableCombineCellElement cell = combineCells[i];
          // if (cell.isTitle == true || cell.contentTitle != null) {
          //   return true;
          // }
          // final dataBindModify = ExcelTransformManager().getElementModify(
          //   elementId: cell.id,
          //   elementValue: cell.value,
          //   isBindingElement: cell.isBindingElement,
          //   isBindingCommodity: cell.isBindingCommodity,
          // );
          // if (dataBindModify != null && dataBindModify.useTitle == true) {
          //   return true;
          // }
        }
      }
    }
    return false;
  }

  /// 返回所有绑定Excel的元素集合
  /// [containMirror] 是否返回绑定Excel的镜像元素
  List<BaseElement>? getAllBindingExcelCanvasElements(bool containMirror) {
    if (containMirror) {
      return elements.where((element) {
        return (element).isBindingElement;
      }).toList();
    } else {
      return elements.where((element) {
        final bindElement = element;
        if ((element).isBindingElement && !bindElement.isOpenMirror) {
          return true;
        }
        return false;
      }).toList();
    }
  }

  List<BaseElement>? getAllBindingExcelElements(bool containMirror) {
    return getAllBindingExcelCanvasElements(containMirror)?.toList();
  }

  /// 包含的商品字段
  List<String> goodsFields() {
    final fields = elements.map((e) {
      final bindElement = e;
      if (bindElement is BindElement) {
        return bindElement.fieldName;
      }
      return null;
    }).toSet();
    fields.remove(null);
    return fields.toList() as List<String>;
  }

  /// 存在批量的数据 (excel, 商品导入)
  bool existBatchBindingData() {
    if (isGoodsLabelTemplate()) {
      /// 商品导入
      return true;
    } else if (isExcelBindingTemplate()) {
      /// excel
      // if (externalData?.externalDataList == null || externalData.externalDataList.isEmpty) {
      //   return false;
      // } else {
      //   return /*(externalData?.externalDataList?.first?.data?.columns?.first?.length ?? 0) > 0 ||*/
      //       (externalData?.externalDataList?.first?.data?.columnHeaders?.length ?? 0) > 0;
      // }
      return true;
    }
    return false;
  }

  /// 批量数据的数量
  int pagesOfBatchData() {
    if (!existBatchBindingData()) {
      return 0;
    }
    if (isExcelBindingTemplate() || isGoodsLabelTemplate()) {
      /// excel
      // int length = externalData.externalDataList.first.data.columns.first.length;
      // int length = (totalPage != null && totalPage>1)?totalPage:ExcelTransformManager().getExcelRowCount()-1;
      // int length = ExcelTransformManager().rowData?.length ?? 0;
      // if (length == 0) {
      //   length = 1;
      // }
      // return length;
      if (totalPage != null && totalPage != 0) {
        return totalPage;
      }
      return totalPage;
    }

    return 0;
  }

  /// 清空 excel 列绑定关系
  List<BaseElement> clearExcelColumnBinding() {
    List<BaseElement> clearedCanvasElements = [];
    for (var element in elements) {
      if (element is TableElement) {
        bool flag = false;
        final cells = element.cells.map((e) {
          if (e.isBindingElement) {
            flag = true;
            return e.copyWith(dataBind: null, value: '');
          }
          return e.copyWith();
        }).toList();
        final combineCells = element.combineCells.map((e) {
          if (e.isBindingElement) {
            flag = true;
            return e.copyWith(dataBind: null, value: '');
          }
          return e.copyWith();
        }).toList();
        if (flag) {
          clearedCanvasElements.add(
            (element.copyWith(cells: cells, combineCells: combineCells)),
          );
        }
      } else if (element is BindElement) {
        if (element.isBindingElement) {
          clearedCanvasElements.add(
            (element.copyWith(dataBind: null, value: '')),
          );
        }
      }
    }
    return clearedCanvasElements;
  }

  /// 清空 excel 列绑定关系
  List<BaseElement> clearGoodsFieldBinding() {
    List<BaseElement> clearedCanvasElements = [];
    for (var element in elements) {
      if (element is BindElement &&
          ((element.fieldName)?.isNotEmpty ?? false)) {
        clearedCanvasElements.add(
          (element.copyWith(fieldName: const CopyWrapper.value(null))),
        );
      }
    }
    return clearedCanvasElements;
  }

  swapElementOrderByFocus(BaseElement focusElement) {
    int index = elements.indexOf(focusElement);
    if (index >= 0) {
      elements.remove(focusElement);
      elements.add(focusElement);
    }
  }

  /// 判断是否同一模板
  bool isMatchRFID(String barcode) {
    bool result = profile.barcode == barcode ||
        profile.extra.sparedCode == barcode ||
        profile.extra.virtualBarCode == barcode ||
        profile.extra.amazonCodeBeijing == barcode ||
        profile.extra.amazonCodeWuhan == barcode ||
        (profile.extra.barcodeCategoryMap?.values.contains(barcode) ?? false);
    _logger.log("======RFID判断，matchResult：$result");
    return result;
  }
}
