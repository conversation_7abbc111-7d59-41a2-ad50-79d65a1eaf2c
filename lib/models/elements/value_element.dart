import 'package:flutter/cupertino.dart';

import 'package:niimbot_template/models/elements/color_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';

/// 值元素
@immutable
abstract class ValueElement extends ColorElement {
  final String? value;

  ValueElement({
    super.id,
    super.x,
    super.y,
    super.rotate,
    super.colorChannel,
    super.elementColor,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    required super.width,
    required super.height,
    required super.type,
    this.value,
  });

  @override
  Map<String, dynamic> toJson() {
    return {
      ...super.toJson(),
      "value": value,
    }..removeWhere((k, v) => v == null);
  }

  @override
  ValueElement.fromJson(super.json)
      : value = json['value'],
        super.fromJson();

  @override
  ValueElement copyWith({
    num? x,
    num? y,
    int? rotate,
    num? width,
    num? height,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
    String? value,
    int? colorChannel,
    Color? elementColor,
  });

  @override
  List<Object?> get props => [...super.props, value];
}
