import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/utils/element_utils.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

///一维码
@immutable
class BarCodeElement extends BindElement {
  final NetalBarcodeType codeType;

  final num fontSize;

  final num textHeight;

  final NetalBarcodeTextPosition textPosition;

  BarCodeElement({
    super.id,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    super.x,
    super.y,
    super.rotate,
    super.colorChannel,
    super.elementColor,
    super.dataBind,
    super.fieldName,
    super.isBinding,
    num? width,
    num? height,
    NetalBarcodeType? codeType,
    num? textHeight,
    NetalBarcodeTextPosition? textPosition,
    num? fontSize,
    super.value,
  }) : fontSize = fontSize ?? 3.2,
       textHeight = textHeight ?? 3.5,
       textPosition = textPosition ?? NetalBarcodeTextPosition.bottom,
       codeType = codeType ?? NetalBarcodeType.CODE128,
       super(
         width: width ?? 25,
         height: height ?? 8,
         type: NetalElementType.barcode,
       );

  @override
  BarCodeElement.fromJson(super.json)
    : codeType = _parseCodeType(json['codeType']),
      textHeight =
          TemplateParseUtils.parseNumberFromJSON(json['textHeight']) ?? 3.5,
      textPosition = _parseTextPosition(json['textPosition']),
      fontSize =
          TemplateParseUtils.parseNumberFromJSON(json['fontSize']) ?? 3.2,
      super.fromJson();

  @override
  Map<String, dynamic> toJson() {
    final data = ({
      ...super.toJson(),
      "fontSize": fontSize.digits(1),
      "textHeight": textHeight.digits(6),
      "textPosition": textPosition.value,
      "codeType": codeType.value,
      'dataBind': dataBind,
    }..removeWhere((k, v) => v == null));
    ElementUtils.completionFontDefault(data);
    return data;
  }

  @override
  BarCodeElement copyWith({
    num? x,
    num? y,
    int? rotate,
    num? width,
    num? height,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    Color? elementColor,
    String? value,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
    int? colorChannel,
    NetalBarcodeType? codeType,
    num? fontSize,
    num? textHeight,
    NetalBarcodeTextPosition? textPosition,
    bool? checkResult,
    CopyWrapper<List<String>?>? dataBind,
    bool? isBinding,
    CopyWrapper<String?>? fieldName,
  }) {
    return BarCodeElement(
      x: x ?? this.x,
      y: y ?? this.y,
      rotate: rotate ?? this.rotate,
      width: width ?? this.width,
      height: height ?? this.height,
      id: id ?? this.id,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      value: value ?? this.value,
      codeType: codeType ?? this.codeType,
      fontSize: fontSize ?? this.fontSize,
      textHeight: textHeight ?? this.textHeight,
      textPosition: textPosition ?? this.textPosition,
      dataBind: dataBind != null ? dataBind.value : this.dataBind,
      isBinding: isBinding ?? this.isBinding,
      fieldName: fieldName != null ? fieldName.value : this.fieldName,
    );
  }

  static String get defaultValue => '123456789';

  @override
  List<Object?> get props => [
    ...super.props,
    codeType,
    fontSize,
    textHeight,
    textPosition,
  ];
}

NetalBarcodeType _parseCodeType(dynamic codeType) {
  return NetalBarcodeType.byValue(
        TemplateParseUtils.parseNumberFromJSON(codeType)?.toInt() ?? 20,
      ) ??
      NetalBarcodeType.CODE128;
}

NetalBarcodeTextPosition _parseTextPosition(dynamic textPosition) {
  return NetalBarcodeTextPosition.byValue(
        TemplateParseUtils.parseNumberFromJSON(textPosition)?.toInt() ?? 0,
      ) ??
      NetalBarcodeTextPosition.bottom;
}

String _fixedValueLength(String value, NetalBarcodeType codeType) {
  if (codeType == NetalBarcodeType.CODEBAR && value.length > 57) {
    return value.substring(0, 57);
  }
  return value;
}
