import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/models/netal_graph_element.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/line_element.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

/// 图形元素
@immutable
class GraphElement extends LineElement implements NetalGraphElement {
  @override
  final num cornerRadius;

  @override
  final NetalGraphType graphType;

  @override
  final num lineWidth;

  GraphElement({
    super.id,
    super.x,
    super.y,
    super.width = 10,
    super.height = 10,
    super.hasVipRes,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.colorChannel,
    super.elementColor,
    super.lineType,
    super.dashWidth,
    NetalGraphType? graphType,
    num? cornerRadius,
    num? lineWidth = 0.4,
  })  : lineWidth = lineWidth ?? 0.4,
        graphType = graphType ?? NetalGraphType.rectangle,
        cornerRadius = cornerRadius ?? 2,
        super(type: NetalElementType.graph);

  GraphElement.fromJson(super.json)
      : graphType = _parseNetalGraphType(json['graphType']),
        cornerRadius =
            TemplateParseUtils.parseNumberFromJSON(json['cornerRadius']) ?? 2,
        lineWidth =
            TemplateParseUtils.parseNumberFromJSON(json['lineWidth']) ?? 0.4,
        super.fromJson();

  @override
  Map<String, dynamic> toJson() {
    return ({
      ...super.toJson(),
      'graphType': graphType.value,
      'cornerRadius': cornerRadius,
      'lineWidth': lineWidth.digits(2),
    }..removeWhere((k, v) => v == null));
  }

  @override
  NetalGraphElement toNetal() {
    return NetalGraphElement(
      x: x,
      y: y,
      rotate: rotate,
      elementColor: elementColor,
      colorChannel: colorChannel,
      width: width,
      height: height,
      dashWidth: dashWidth,
      cornerRadius: cornerRadius,
      lineWidth: lineWidth,
      graphType: graphType,
      lineType: lineType,
    );
  }

  GraphElement copyWith({
    num? x,
    num? y,
    int? rotate,
    num? width,
    num? height,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    Color? elementColor,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
    int? colorChannel,
    List<num>? dashWidth,
    NetalLineType? lineType,
    num? cornerRadius,
    NetalGraphType? graphType,
    num? lineWidth,
  }) {
    return GraphElement(
      x: x ?? this.x,
      y: y ?? this.y,
      rotate: rotate ?? this.rotate,
      width: width ?? this.width,
      height: height ?? this.height,
      id: id ?? this.id,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      dashWidth: dashWidth ?? this.dashWidth,
      lineType: lineType ?? this.lineType,
      cornerRadius: cornerRadius ?? this.cornerRadius,
      graphType: graphType ?? this.graphType,
      lineWidth: lineWidth ?? this.lineWidth,
    );
  }

  factory GraphElement.fromLineElement(LineElement lineElement) {
    return GraphElement(
      id: lineElement.id,
      x: lineElement.x,
      y: lineElement.y,
      width: lineElement.width,
      height: lineElement.height,
      graphType: NetalGraphType.rectangle,
      cornerRadius: 2,
      rotate: lineElement.rotate,
      isLock: lineElement.isLock,
      isOpenMirror: lineElement.isOpenMirror,
      mirrorType: lineElement.mirrorType,
      lineType: lineElement.lineType,
      lineWidth: lineElement.height,
      dashWidth: lineElement.dashWidth,
      colorChannel: lineElement.colorChannel,
      elementColor: lineElement.elementColor,
    );
  }

  LineElement fromGraph() {
    return LineElement(
      id: id,
      x: x,
      y: y,
      width: width,
      height: lineWidth,
      rotate: rotate,
      isLock: isLock,
      isOpenMirror: isOpenMirror,
      mirrorType: mirrorType,
      lineType: lineType,
      dashWidth: dashWidth,
      colorChannel: colorChannel,
      elementColor: elementColor,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is GraphElement &&
          runtimeType == other.runtimeType &&
          super == other &&
          cornerRadius == other.cornerRadius &&
          graphType == other.graphType &&
          lineWidth == other.lineWidth;

  @override
  int get hashCode =>
      super.hashCode ^
      cornerRadius.hashCode ^
      graphType.hashCode ^
      lineWidth.hashCode;

  @override
  List<Object?> get props => [
        ...super.props,
        cornerRadius,
        graphType,
        lineWidth,
      ];
}

NetalGraphType _parseNetalGraphType(dynamic value) {
  return NetalGraphType.byValue(
          TemplateParseUtils.parseNumberFromJSON(value)?.toInt() ?? 3) ??
      NetalGraphType.rectangle;
}
