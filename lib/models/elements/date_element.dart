import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/utils/element_utils.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

final Logger _logger = Logger("DateElement", on: kDebugMode);

/// 时间元素
@immutable
class DateElement extends TextElement {
  /// 日期格式
  final ElementDateFormat? dateFormat;

  // 日期格式兼容字段
  final String? dateFormatCompat;

  /// 时间格式
  final ElementTimeFormat? timeFormat;

  // 时间格式兼容字段
  final String? timeFormatCompat;

  /// 时间戳 单位:毫秒
  final int time;

  /// 是否使用当前时间
  final bool dateIsRefresh;

  /// 12小时制
  final ElementTimeUnit? timeUnit;

  /// 时间偏移
  final int timeOffset;

  /// 是否被关联（true：第一时间, false：第二时间）
  final bool associated;

  /// 关联Id
  final String associateId;

  /// 关联时间值
  final int validityPeriod;

  int get validityPeriodNew {
    int validityPeriodNewValue = validityPeriod;
    if (validityPeriodUnit == ElementDateAssociatedUnit.day) {
      validityPeriodNewValue -= validityPeriodNewValue;
    }
    return validityPeriodNewValue;
  }

  /// 关联时间单位
  final ElementDateAssociatedUnit validityPeriodUnit;

  DateElement({
    super.id,
    super.x,
    super.y,
    super.width,
    super.height,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.typesettingMode,
    super.typesettingParam,
    super.textAlignHorizontal,
    super.textAlignVertical,
    super.lineBreakMode,
    super.wordSpacing,
    super.letterSpacing,
    super.lineSpacing,
    super.fontFamily,
    super.fontCode,
    super.fontStyle,
    super.fontSize,
    super.fontColor,
    super.isTitle,
    super.colorReverse,
    // super.paperColorIndex,
    super.colorChannel,
    super.elementColor,
    super.hasVipRes,
    super.isEditing = false,
    super.boxStyle,
    super.textStyle,
    super.contentTitle,
    super.lineMode,
    super.wordsRotate,
    this.dateFormat,
    this.dateFormatCompat,
    this.timeFormat,
    this.timeFormatCompat,
    required this.time,
    bool? dateIsRefresh,
    this.timeUnit,
    timeOffset,
    associated,
    associateId,
    validityPeriod,
    validityPeriodUnit,
    validityPeriodNew,
  })  : assert(
          dateFormat != null ||
              dateFormatCompat != null ||
              timeFormat != null ||
              timeFormatCompat != null,
          '时间格式跟日期格式 必须传一个',
        ),
        dateIsRefresh = dateIsRefresh ?? false,
        timeOffset = timeOffset ?? 0,
        associated = associated ?? false,
        associateId = associateId ?? '',
        validityPeriod = validityPeriod ?? 1,
        validityPeriodUnit =
            validityPeriodUnit ?? ElementDateAssociatedUnit.month,
        super(type: NetalElementType.date, value: '');

  DateElement.fromJson(super.json)
      : dateFormat = _parseElementDateFormat(json['dateFormat']),
        dateFormatCompat = json['dateFormat'],
        timeFormat = _parseElementTimeFormat(json['timeFormat']),
        timeFormatCompat = json['timeFormat'],
        time = _parseTime(json['time'], json['value']),
        dateIsRefresh = TemplateParseUtils.parseBoolFromJSON(
          json['dateIsRefresh'],
        ),
        timeUnit = ElementTimeUnit.byValue(json['timeUnit']),
        timeOffset = json['timeOffset'] ?? 0,
        associated = TemplateParseUtils.parseBoolFromJSON(json['associated']),
        associateId = json['associateId'] ?? '',
        validityPeriod = json['validityPeriod'] ?? 1,
        validityPeriodUnit =
            ElementDateAssociatedUnit.byValue(json['validityPeriodUnit']) ??
                ElementDateAssociatedUnit.month,
        super.fromJson();

  @override
  Map<String, dynamic> toJson() {
    /// 按指定格式处理时间
    final timeFormatStr = timeUnit?.name != null
        ? timeFormat?.value.replaceAll('HH', 'H')
        : timeFormat?.value;
    final dateFormatStr = dateFormat?.value ?? (dateFormatCompat ?? "");
    final data = ({
      ...super.toJson(),
      'dateFormat': dateFormatStr,
      'dateFormatCompat': dateFormatCompat,
      'timeFormat': timeFormatStr ?? '',
      'timeFormatCompat': timeFormatCompat,
      'time': time,
      'dateIsRefresh': dateIsRefresh ? 1 : 0,
      'value': time.toString(),
      'timeUnit': timeUnit?.name ?? '',
      'timeOffset': timeOffset,
      'associated': associated,
      'associateId': associateId,
      'validityPeriod': validityPeriod,
      'validityPeriodNew': validityPeriodNew,
      'validityPeriodUnit': validityPeriodUnit.value,
    }..removeWhere((k, v) => v == null));
    ElementUtils.completionFontDefault(data);
    return data;
  }

  @override
  DateElement copyWith({
    final num? x,
    final num? y,
    final int? rotate,
    final num? width,
    final num? height,
    final num? lastWidth,
    final num? lastHeight,
    final String? id,
    final bool? isLock,
    final bool? isOpenMirror,
    final ElementMirrorType? mirrorType,
    final bool? hasVipRes,
    final bool? colorReverse,
    final NetalTextWriteMode? write_mode,
    final NetalTypesettingMode? typesettingMode,
    final List<int>? typesettingParam,
    final NetalTextAlign? textAlignHorizontal,
    final NetalTextAlign? textAlignVertical,
    final num? wordSpacing,
    final num? letterSpacing,
    final num? lineSpacing,
    final String? fontFamily,
    final String? fontCode,
    final List<NetalTextFontStyle>? fontStyle,
    final num? fontSize,
    final List<int>? fontColor,
    final bool? isTitle,
    final NetalTextLineBreakMode? lineBreakMode,
    final String? value,
    final CopyWrapper<List<String>?>? dataBind,
    final CopyWrapper<String?>? fieldName,
    final bool? isBinding,
    final int? tagForRelocation,
    final int? colorChannel,
    final Color? elementColor,
    final NetalTextBoxStyle? boxStyle,
    final List<NetalTextStyle>? textStyle,
    final CopyWrapper<ElementDateFormat?>? dateFormat,
    final CopyWrapper<ElementTimeFormat?>? timeFormat,
    final int? time,
    final bool? dateIsRefresh,
    final CopyWrapper<String?>? contentTitle,
    final CopyWrapper<ElementTimeUnit?>? timeUnit,
    final int? timeOffset,
    final bool? associated,
    final CopyWrapper<String?>? associateId,
    final CopyWrapper<int?>? validityPeriod,
    final CopyWrapper<ElementDateAssociatedUnit?>? validityPeriodUnit,
    final CopyWrapper<NetalTextLineMode?>? lineMode,
    final CopyWrapper<int?>? validityPeriodNew,
    final int? wordsRotate,
  }) {
    return DateElement(
      x: x ?? this.x,
      y: y ?? this.y,
      rotate: rotate ?? this.rotate,
      width: width ?? this.width,
      height: height ?? this.height,
      id: id ?? this.id,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      colorReverse: colorReverse ?? this.colorReverse,
      fontCode: fontCode ?? this.fontCode,
      fontFamily: fontFamily ?? this.fontFamily,
      fontSize: fontSize ?? this.fontSize,
      fontStyle: fontStyle ?? this.fontStyle,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      lineBreakMode: lineBreakMode ?? this.lineBreakMode,
      lineSpacing: lineSpacing ?? this.lineSpacing,
      textAlignHorizontal: textAlignHorizontal ?? this.textAlignHorizontal,
      textAlignVertical: textAlignVertical ?? this.textAlignVertical,
      typesettingMode: typesettingMode ?? this.typesettingMode,
      typesettingParam: typesettingParam ?? this.typesettingParam,
      wordSpacing: wordSpacing ?? this.wordSpacing,
      dateFormat: dateFormat != null ? dateFormat.value : this.dateFormat,
      timeFormat: timeFormat != null ? timeFormat.value : this.timeFormat,
      time: time ?? this.time,
      dateIsRefresh: dateIsRefresh ?? this.dateIsRefresh,
      boxStyle: boxStyle ?? this.boxStyle,
      textStyle: textStyle ?? this.textStyle,
      contentTitle:
          contentTitle != null ? contentTitle.value : this.contentTitle,
      timeUnit: timeUnit != null ? timeUnit.value : this.timeUnit,
      timeOffset: timeOffset ?? this.timeOffset,
      associated: associated ?? this.associated,
      associateId: associateId != null ? associateId.value : this.associateId,
      validityPeriod:
          validityPeriod != null ? validityPeriod.value : this.validityPeriod,
      validityPeriodUnit: validityPeriodUnit != null
          ? validityPeriodUnit.value
          : this.validityPeriodUnit,
      validityPeriodNew: validityPeriodNew != null
          ? validityPeriodNew.value
          : this.validityPeriodNew,
      lineMode: lineMode != null ? lineMode.value : this.lineMode,
      wordsRotate: wordsRotate ?? this.wordsRotate,
    );
  }

  Map<String, dynamic> antiEscapeValueToJson() {
    Map<String, dynamic> data = toJson();
    data['value'] = time.toString();
    return data;
  }

  // ElementDateFormat getSelectorDateFormat() {
  //   if (dateFormat?.value.isEmpty ?? true) {
  //     return DateElementHelper.getDateFormat();
  //   }
  //   return dateFormat!;
  // }

  // ElementTimeFormat getSelectorTimeFormat() {
  //   if (timeFormat?.value.isEmpty ?? true) {
  //     ElementTimeFormat? format = DateElementHelper.getTimeFormat();
  //     if (format == null) {
  //       return ElementTimeFormat.HM;
  //     }
  //     return format;
  //   }
  //   return timeFormat!;
  // }

  @override
  bool get hasVipSource {
    if (super.hasVipSource || dateIsRefresh) {
      return true;
    }
    return false;
  }

  /// 是否展示实时时间，需要同时满足：
  /// 1、当前为vip用户
  /// 2、实时时间开关打开
  // bool get isRefreshDateEnable {
  //   return false;
  //
  //   if (CanvasUserCenter().vipType != VIPType.valid) {
  //     return false;
  //   }
  //   if (dateIsRefresh) {
  //     return false;
  //   }
  //   return true;
  // }

  /// 是否是关联元素
  bool get isAssociate {
    if (associateId.isNotEmpty && !associated) {
      /* 关联Id不为空 且已关联为false */
      return true;
    }
    return false;
  }

  ///是否是关联元素源元素
  bool get isAssociateOrigin {
    if (associateId.isNotEmpty && associated) {
      /* 关联Id不为空 且已关联为true */
      return true;
    }
    return false;
  }

  @override
  List<Object?> get props => [
        ...super.props,
        dateFormat,
        timeFormat,
        time,
        dateIsRefresh,
        timeUnit,
        timeOffset,
        associated,
        associateId,
        validityPeriod,
        validityPeriodNew,
        validityPeriodUnit,
      ];

  /// 根据[index]索引构建值
  String buildValue({
    ElementDateFormat? dateFormat,
    ElementTimeFormat? timeFormat,
    ElementTimeUnit? timeUnit,
  }) =>
      ElementUtils.buildDateValue(
        time: (isAssociate ? false : dateIsRefresh)
            ? null
            : isAssociate
                ? ElementUtils.buildDateElementAssociateTime(
                    dateIsRefresh,
                    time,
                    validityPeriodUnit,
                    validityPeriodNew,
                  )
                : time, //关联元素实时时间由_buildAssociateTime函数构建
        // timeOffset: timeOffset,
        dateFormat: dateFormat?.value ?? dateFormatCompat,
        timeFormat: timeFormat?.value ?? timeFormatCompat,
        militaryTime: timeUnit == null,
      );
}

int _parseTime(dynamic field, dynamic value) {
  var time = field ?? 0;
  if (time == 0) {
    try {
      time = int.parse(value);
    } catch (e) {
      time = DateTime.now().millisecondsSinceEpoch;
    }
  }
  return time;
}

// String _parseValue(dynamic field) {
//   /// 字段兼容处理
//   if ((field ?? '').contains('T')) {
//     return DateFormat(
//       'yyyyMMddHHmmss',
//     ).format(DateTime.fromMillisecondsSinceEpoch(int.parse(field)));
//   }
//   return field;
// }

final _coverMap = {
  'MMMM DD,YYYY': 'MMMM D,YYYY',
  'MMM DD,YYYY': 'MMM D,YYYY',
  'DD MMM YYYY': 'D MMM,YYYY',
  'DD MMMM YYYY': 'D MMMM,YYYY',
};

ElementDateFormat? _parseElementDateFormat(dynamic value) {
  if (value != null && value?.isNotEmpty) {
    final val = _coverMap[value] ?? value;
    return ElementDateFormat.byValue(val) ?? ElementDateFormat.byLabel(val);
  }
  return null;
}

ElementTimeFormat? _parseElementTimeFormat(dynamic value) {
  if (value != null && value?.isNotEmpty) {
    if (value is String) {
      final newVal = value.contains('HH') ? value : value.replaceAll('H', 'HH');
      return ElementTimeFormat.byValue(newVal);
    }
    return ElementTimeFormat.byValue(value);
  }
  return null;
}
