import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';

import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

/// 二维码元素
@immutable
class QRCodeElement extends BindElement {
  final NetalQRCodeType codeType;

  final NetalQRCodeCorrectLevel correctLevel;

  /// 是否活码类型
  final bool isLive;

  /// 活码ID
  final String? liveCodeId;

  /// 是否表单类型
  final bool isForm;

  /// 表单ID
  final String? formId;

  /// 是否为高级二维码
  bool get isAdvanceQRCode {
    return isForm || isLive;
  }

  QRCodeElement({
    super.id,
    super.x,
    super.y,
    super.width = 10,
    super.height = 10,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    super.colorChannel,
    super.elementColor,
    super.isBinding,
    super.dataBind,
    super.fieldName,
    NetalQRCodeType? codeType,
    NetalQRCodeCorrectLevel? correctLevel,
    String? value,
    this.isForm = false,
    this.isLive = false,
    this.liveCodeId,
    this.formId,
  }) : codeType = codeType ?? NetalQRCodeType.QR_CODE,
       correctLevel = correctLevel ?? NetalQRCodeCorrectLevel.M,
       super(value: value ?? defaultValue, type: NetalElementType.qrcode);

  QRCodeElement.fromJson(super.json)
    : codeType = _parseNetalQRCodeType(json['codeType']),
      correctLevel = _parseNetalQRCodeCorrectLevel(json['correctLevel']),
      isLive = TemplateParseUtils.parseBoolFromJSON(json['isLive']),
      liveCodeId = json['liveCodeId'],
      isForm = TemplateParseUtils.parseBoolFromJSON(json['isForm']),
      formId = json['formId'],
      super.fromJson();

  @override
  Map<String, dynamic> toJson() {
    return ({
      ...super.toJson(),
      'codeType': codeType.value,
      'correctLevel': correctLevel.value,
      'isLive': isLive,
      'liveCodeId': liveCodeId,
      'isForm': isForm,
      'formId': formId,
    }..removeWhere((k, v) => v == null));
  }

  @override
  QRCodeElement copyWith({
    num? x,
    num? y,
    int? rotate,
    num? width,
    num? height,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    Color? elementColor,
    String? value,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
    int? colorChannel,
    NetalQRCodeType? codeType,
    NetalQRCodeCorrectLevel? correctLevel,
    CopyWrapper<List<String>?>? dataBind,
    CopyWrapper<String?>? fieldName,
    bool? isBinding,
  }) {
    return QRCodeElement(
      x: x ?? this.x,
      y: y ?? this.y,
      rotate: rotate ?? this.rotate,
      width: width ?? this.width,
      height: height ?? this.height,
      id: id ?? this.id,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      value: value ?? this.value,
      codeType: codeType ?? this.codeType,
      correctLevel: correctLevel ?? this.correctLevel,
      dataBind: dataBind != null ? dataBind.value : this.dataBind,
      fieldName: fieldName != null ? fieldName.value : this.fieldName,
    );
  }

  static String get defaultValue => '123456789';

  @override
  List<Object?> get props => [
    ...super.props,
    codeType,
    correctLevel,
    isLive,
    liveCodeId,
    isForm,
    formId,
  ];
}

NetalQRCodeType _parseNetalQRCodeType(dynamic value) {
  return NetalQRCodeType.byValue(
        TemplateParseUtils.parseNumberFromJSON(value)?.toInt() ?? 31,
      ) ??
      NetalQRCodeType.QR_CODE;
}

NetalQRCodeCorrectLevel _parseNetalQRCodeCorrectLevel(dynamic value) {
  return NetalQRCodeCorrectLevel.byValue(
        TemplateParseUtils.parseNumberFromJSON(value)?.toInt() ?? 0,
      ) ??
      NetalQRCodeCorrectLevel.L;
}
