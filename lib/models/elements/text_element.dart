import 'package:flutter/cupertino.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/models/netal_text_element.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_intl/niimbot_intl.dart';

import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/template/constants.dart';
import 'package:niimbot_template/utils/element_utils.dart';
import 'package:niimbot_template/utils/string_utils.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

class TypeSettingMode {
  static const int TEXT_HORIZONTAL = 1;
  static const int TEXT_VERTICAL = 2;
  static const int TEXT_CURVE = 3;
}

class LanguageName {
  static const String LANGUAGE_ZH_CN = "zh-cn";
  static const String LANGUAGE_ZH_TW = "zh-cn-t";
  static const String LANGUAGE_EN = "en";
  static const String LANGUAGE_JA = "ja";
  static const String LANGUAGE_KO = "ko";
}

/// 文本元素
@immutable
class TextElement extends BindElement {
  final bool colorReverse;

  final NetalTextWriteMode write_mode;

  /// 排版模式：1.横向排版（默认） 2.纵向（垂直）排版  3.弧形文字排版
  final NetalTypesettingMode typesettingMode;

  final List<int> typesettingParam;

  final NetalTextAlign textAlignHorizontal;

  final NetalTextAlign textAlignVertical;

  final num wordSpacing;

  final num letterSpacing;

  final num lineSpacing;

  final String fontFamily;

  final String fontCode;

  final List<NetalTextFontStyle> fontStyle;

  final num fontSize;
  final List<int>? fontColor;
  final bool? isTitle;
  final String? contentTitle;

  /// 0.英文数字按字符换行(默认)  1.英文数字按单词换行

  final NetalTextLineBreakMode lineBreakMode;
  final double lastWidth;
  final double lastHeight;

  /// 文本样式

  final NetalTextBoxStyle boxStyle;

  final List<NetalTextStyle> textStyle;

  /// 记录上次不同模式下的文本样式
  final Map<int, NetalTextBoxStyle> lastBoxStyles;

  /// 记录上次非模式3下的文本框模式
  /// 用于在复选框在反选时可以回到上次操作的文本框模式
  final NetalTextBoxStyle lastUnlessFixWidthHeightBoxStyle;

  /// 当前文字内容固定宽高最大字号
  final double fixedWidthHeightMaxFontSize;

  /// 编辑中
  final bool isEditing;

  /// 字号变更
  final bool fontSizeChanged = false;

  @override
  NetalTextLineMode lineMode;

  int wordsRotate;

  TextElement({
    super.id,
    super.x,
    super.y,
    super.width = 15,
    super.height = 4,
    super.hasVipRes,
    super.type = NetalElementType.text,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.colorChannel,
    super.elementColor,
    super.fieldName,
    super.dataBind,
    super.isBinding,
    super.value,
    bool? colorReverse,
    NetalTypesettingMode? typesettingMode,
    List<int>? typesettingParam,
    NetalTextAlign? textAlignHorizontal,
    NetalTextAlign? textAlignVertical,
    num? wordSpacing,
    num? letterSpacing,
    num? lineSpacing,
    String? fontFamily,
    String? fontCode,
    List<NetalTextFontStyle>? fontStyle,
    num? fontSize,
    this.fontColor,
    this.isTitle,
    this.contentTitle,
    NetalTextLineBreakMode? lineBreakMode,
    this.isEditing = false,
    NetalTextWriteMode? write_mode,
    NetalTextBoxStyle? boxStyle,
    List<NetalTextStyle>? textStyle,
    NetalTextLineMode? lineMode,
    int? wordsRotate,
  })
      : colorReverse = colorReverse ?? false,
        typesettingMode = typesettingMode ?? NetalTypesettingMode.horizontal,
        typesettingParam = typesettingParam ?? [0, 180],
        textAlignHorizontal = textAlignHorizontal ?? NetalTextAlign.start,
        textAlignVertical = textAlignVertical ?? NetalTextAlign.start,
        wordSpacing = wordSpacing ?? 0,
        letterSpacing = letterSpacing ?? 0,
        lineSpacing = lineSpacing ?? 0,
        fontFamily = fontFamily ?? 'Harmony',
        fontCode = fontCode ?? 'ZT001',
        fontStyle = fontStyle ?? [],
        fontSize = fontSize ?? 3.2,
        lineBreakMode = lineBreakMode ?? NetalTextLineBreakMode.char,
        write_mode = write_mode ?? NetalTextWriteMode.ltr,
        lastWidth = width.toDouble(),
        lastHeight = height.toDouble(),
        boxStyle = boxStyle ?? NetalTextBoxStyle.autoHeight,
        textStyle = textStyle ?? const [NetalTextStyle.norm],
        lastUnlessFixWidthHeightBoxStyle =
            boxStyle ?? NetalTextBoxStyle.autoHeight,
        lastBoxStyles = {
          (typesettingMode ?? NetalTypesettingMode.horizontal).value:
          boxStyle ?? NetalTextBoxStyle.autoHeight
        },
        fixedWidthHeightMaxFontSize = TemplateConstants.FONT_SIZE_LIST.last.mm,
        lineMode = lineMode ?? NetalTextLineMode.normal,
        wordsRotate = wordsRotate ?? 0;

  TextElement.fromJson(super.json)
      : typesettingMode = TemplateParseUtils.parseNetalTypesettingModeFromJSON(
      json['typesettingMode']) ??
      NetalTypesettingMode.horizontal,
        typesettingParam =
            TemplateParseUtils.parseListFromJSON(json['typesettingParam']) ??
                [0, 180],
        textAlignHorizontal = TemplateParseUtils.parseNetalTextAlignFromJSON(
            json['textAlignHorizonral']) ??
            NetalTextAlign.start,
        textAlignVertical = TemplateParseUtils.parseNetalTextAlignFromJSON(
            json['textAlignVertical']) ??
            NetalTextAlign.start,
        wordSpacing =
            TemplateParseUtils.parseNumberFromJSON(json['wordSpacing']) ?? 0,
        letterSpacing =
            TemplateParseUtils.parseNumberFromJSON(json['letterSpacing']) ?? 0,
        lineSpacing =
            TemplateParseUtils.parseNumberFromJSON(json['lineSpacing']) ?? 0,
        fontFamily = json['fontFamily'] ?? 'Harmony',
        fontCode = json['fontCode'] ?? 'ZT001',
        fontStyle = TemplateParseUtils.parseNetalTextFontStyleFromJSON(
            json['fontStyle']) ??
            [],
        fontSize = json['fontSize'],
        fontColor = TemplateParseUtils.parseListFromJSON(json['fontColor']),
        isTitle = TemplateParseUtils.parseBoolFromJSON(json['isTitle']),
        contentTitle = json['contentTitle'],
        lineBreakMode = TemplateParseUtils.parseNetalTextLineBreakModeFromJSON(
            json['lineBreakMode']) ??
            NetalTextLineBreakMode.char,
        lastWidth =
            TemplateParseUtils.parseNumberFromJSON(json['width'])?.toDouble() ??
                30,
        lastHeight = TemplateParseUtils.parseNumberFromJSON(json['height'])
            ?.toDouble() ??
            4,
        colorReverse =
        TemplateParseUtils.parseBoolFromJSON(json['colorReverse']),
        write_mode = TemplateParseUtils.parseNetalTextWriteModeFromJSON(
            json['write_mode']) ??
            NetalTextWriteMode.ltr,
        lineMode = TemplateParseUtils.parseNetalTextLineModeFromJSON(
            json['lineMode']) ??
            NetalTextLineMode.normal,
        textStyle = TemplateParseUtils.parseTemplateNetalTextStyleFromJSON(
            json['textStyle']) ??
            [NetalTextStyle.norm],
        boxStyle = TemplateParseUtils.parseTemplateNetalTextBoxStyleFromJSON(
            json["boxStyle"]) ??
            NetalTextBoxStyle.autoHeight,
        lastUnlessFixWidthHeightBoxStyle =
            TemplateParseUtils.parseTemplateNetalTextBoxStyleFromJSON(
                json["lastUnlessFixWidthHeightBoxStyle"]) ??
                NetalTextBoxStyle.autoHeight,
        lastBoxStyles = {
          (TemplateParseUtils.parseNetalTypesettingModeFromJSON(
              json['typesettingMode']) ??
              NetalTypesettingMode.horizontal)
              .value: TemplateParseUtils.parseTemplateNetalTextBoxStyleFromJSON(
              json["boxStyle"]) ??
              NetalTextBoxStyle.autoHeight
        },
        fixedWidthHeightMaxFontSize = TemplateConstants.FONT_SIZE_LIST.last.mm,
        isEditing = false,
        wordsRotate = json['wordsRotate'] ?? 0,
        super.fromJson();

  @override
  Map<String, dynamic> toJson() {
    final data = ({
      ...super.toJson(),
      "textAlignHorizonral": textAlignHorizontal.value,
      "textAlignVertical": textAlignVertical.value,
      "lineSpacing": lineSpacing,
      "letterSpacing": letterSpacing,
      "fontCode": fontCode,
      "fontFamily": fontFamily,
      "fontStyle": fontStyle.map((e) => e.name).toList(),
      "fontSize": fontSize.digits(1),
      "contentTitle": contentTitle,
      "lineBreakMode": lineBreakMode.value,
      'typesettingMode': typesettingMode.value,
      "typesettingParam": typesettingParam,
      "colorReverse": colorReverse ? 1 : 0,
      "write_mode": write_mode.value,
      "wordSpacing": letterSpacing,
      'dataBind': dataBind,
      'fieldName': fieldName,
      'boxStyle': boxStyle.value,
      'textStyle': textStyle.map((e) => e.name).toList(),
      'lineMode': lineMode.value,
      'wordsRotate': wordsRotate,
    }
      ..removeWhere((k, v) => v == null));

    if (escapeValue && runtimeType == TextElement) {
      if (isBindingElement && (data['value'] ?? '').length == 0) {
        data['value'] =
            NiimbotIntl.getIntlMessage('app00364', '双击文本框编辑');
      }
    }
    ElementUtils.completionFontDefault(data);
    return data;
  }

  @override
  NetalTextElement toNetal() {
    return NetalTextElement(
      x: x,
      y: y,
      rotate: rotate,
      elementColor: elementColor,
      colorChannel: colorChannel,
      width: width,
      height: height,
      value: StringUtils.covertNewLineChar(
          ((value != null || value!.isEmpty) && !isBindingElement
              ? defaultValue
              : value) ??
              '')!,
      colorReverse: colorReverse ? 1 : 0,
      // 上层使用fontCode标记字体 ZT001表示默认字体即为Harmony
      fontFamily: fontCode == 'ZT001' ? 'Harmony' : fontCode,
      fontSize: fontSize,
      fontStyle: fontStyle,
      letterSpacing: letterSpacing,
      lineBreakMode: lineBreakMode,
      lineSpacing: lineSpacing,
      textAlignVertical: textAlignVertical,
      textAlignHorizontal: textAlignHorizontal,
      typesettingMode: typesettingMode,
      typesettingParam: typesettingParam,
      wordSpacing: wordSpacing,
      write_mode: write_mode,
      boxStyle: boxStyle,
      textStyle: textStyle,
      lineMode: lineMode,
      wordsRotate: wordsRotate,
    );
  }

  double getMinTextWidth() {
    /// 文本框最小宽度33像素
    return (40.px2mm()).mm2px().toDouble();
    // if (value == null || value.isEmpty) {
    //   return 0;
    // }
    // //计算一个汉字的宽度作为最小宽度
    // Size textSize = InputUtils.boundingTextSize("精", TextStyle(fontSize: fontSize.mm2dp()), maxLines: 1);
    // return textSize.width;
  }

  double getMinTextHeight() {
    /// 文本框最小高度86像素
    return (40.px2mm()).mm2px().toDouble();
    // if (value == null || value.isEmpty) {
    //   return 0;
    // }
    // Size textSize = InputUtils.boundingTextSize(value, TextStyle(fontSize: fontSize.mm2dp()), maxLines: 1);
    // return textSize.height;
  }

  bool isTextVertical() {
    return typesettingMode == NetalTypesettingMode.vertical;
  }

  TextElement sizeSwapByTextDirectionChanged() {
    return copyWith(
      lastWidth: width.toDouble(),
      lastHeight: height.toDouble(),
      width: height.toDouble(),
      height: width.toDouble(),
    );
  }

  ///是否支持竖排文字
  bool checkSupportVerticalText() {
    // 竖排文字仅支持应用内 简中/繁中/韩文/日文/英文
    String currentLanguage = CanvasUserCenter().languageCode;
    return currentLanguage == LanguageName.LANGUAGE_ZH_CN ||
        currentLanguage == LanguageName.LANGUAGE_ZH_TW ||
        currentLanguage == "zh" ||
        currentLanguage == LanguageName.LANGUAGE_KO ||
        currentLanguage == LanguageName.LANGUAGE_JA ||
        currentLanguage == LanguageName.LANGUAGE_EN;
  }

  ///变更文本方向时 对齐方式做下映射
  TextElement alignChangeFun(bool isVertical) {
    if (isVertical) {
      return copyWith(textAlignVertical: textAlignHorizontal);
    } else {
      return copyWith(textAlignHorizontal: textAlignVertical);
    }
  }

  @override
  bool get hasVipSource {
    return hasVipRes;
  }

  bool get showExcelTitle {
    return false;
    // return ExcelTransformManager()
    //         .getElementModify(
    //           elementId: id,
    //           elementValue: value,
    //           isBindingElement: isBindingElement,
    //           isBindingCommodity: isBindingCommodity,
    //         )
    //         ?.useTitle ??
    //     false;
  }

  @override
  TextElement copyWith({
    num? x,
    num? y,
    int? rotate,
    num? width,
    num? height,
    num? lastWidth,
    num? lastHeight,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
    bool? colorReverse,
    NetalTextWriteMode? write_mode,
    NetalTypesettingMode? typesettingMode,
    List<int>? typesettingParam,
    NetalTextAlign? textAlignHorizontal,
    NetalTextAlign? textAlignVertical,
    num? wordSpacing,
    num? letterSpacing,
    num? lineSpacing,
    String? fontFamily,
    String? fontCode,
    List<NetalTextFontStyle>? fontStyle,
    num? fontSize,
    List<int>? fontColor,
    bool? isTitle,
    CopyWrapper<String?>? contentTitle,
    NetalTextLineBreakMode? lineBreakMode,
    String? value,
    CopyWrapper<List<String>?>? dataBind,
    CopyWrapper<String?>? fieldName,
    bool? isBinding,
    int? colorChannel,
    Color? elementColor,
    NetalTextBoxStyle? boxStyle,
    List<NetalTextStyle>? textStyle,
    CopyWrapper<NetalTextLineMode?>? lineMode,
    int? wordsRotate,
  }) {
    return TextElement(
      id: id ?? this.id,
      x: x ?? this.x,
      y: y ?? this.y,
      rotate: rotate ?? this.rotate,
      width: width ?? this.width,
      height: height ?? this.height,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      colorReverse: colorReverse ?? this.colorReverse,
      write_mode: write_mode ?? this.write_mode,
      typesettingMode: typesettingMode ?? this.typesettingMode,
      typesettingParam: typesettingParam ?? this.typesettingParam,
      textAlignHorizontal: textAlignHorizontal ?? this.textAlignHorizontal,
      textAlignVertical: textAlignVertical ?? this.textAlignVertical,
      wordSpacing: wordSpacing ?? this.wordSpacing,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      lineSpacing: lineSpacing ?? this.lineSpacing,
      fontFamily: fontFamily ?? this.fontFamily,
      fontCode: fontCode ?? this.fontCode,
      fontStyle: fontStyle ?? List.from(this.fontStyle),
      fontSize: fontSize ?? this.fontSize,
      fontColor: fontColor ?? this.fontColor,
      isTitle: isTitle ?? this.isTitle,
      contentTitle:
      contentTitle != null ? contentTitle.value : this.contentTitle,
      lineBreakMode: lineBreakMode ?? this.lineBreakMode,
      value: value ?? this.value,
      isBinding: isBinding ?? this.isBinding,
      dataBind: dataBind != null ? dataBind.value : this.dataBind,
      fieldName: fieldName != null ? fieldName.value : this.fieldName,
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      // paperColorIndex: paperColorIndex ?? this.paperColorIndex,
      boxStyle: boxStyle ?? this.boxStyle,
      textStyle: textStyle ?? this.textStyle,
      lineMode: lineMode != null ? lineMode.value : this.lineMode,
      wordsRotate: wordsRotate ?? this.wordsRotate,
    );
  }

  TextTypesettingMode get textTypesettingMode {
    if (typesettingMode == NetalTypesettingMode.vertical) {
      if (rotate == 270) {
        return TextTypesettingMode.Horizontal_90;
      } else {
        return TextTypesettingMode.Vertical;
      }
    }
    if (typesettingMode == NetalTypesettingMode.arc) {
      return TextTypesettingMode.Arc;
    }
    return TextTypesettingMode.Horizontal;
  }

  static String get defaultValue =>
      NiimbotIntl.getIntlMessage('pc0371', '双击编辑');

  @override
  List<Object?> get props =>
      [
        ...super.props,
        colorReverse,
        write_mode,
        typesettingMode,
        typesettingParam,
        textAlignHorizontal,
        textAlignVertical,
        wordSpacing,
        letterSpacing,
        lineSpacing,
        fontFamily,
        fontCode,
        fontStyle,
        fontSize,
        fontColor,
        isTitle,
        contentTitle,
        lineBreakMode,
        lastWidth,
        lastHeight,
        boxStyle,
        textStyle,
        lastBoxStyles,
        lastUnlessFixWidthHeightBoxStyle,
        fixedWidthHeightMaxFontSize,
        isEditing,
        fontSizeChanged,
      ];
}
