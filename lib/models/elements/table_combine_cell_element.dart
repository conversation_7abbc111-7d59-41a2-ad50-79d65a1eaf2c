import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/models/netal_table_combine_cell_element.dart';
import 'package:niimbot_excel/models/interface.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:collection/collection.dart';

import 'package:niimbot_template/utils/element_utils.dart';
import 'package:niimbot_template/utils/string_utils.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

/// 表格合并单元格
@immutable
class TableCombineCellElement extends NetalTableCombineCellElement
    with EquatableMixin {
  /// excel 列绑定信息
  final bool isBinding;

  /// 第一个参数为excel唯一表示hash值，第二个参数为excel中第一个sheet的名字 eg:"dataBind": ["d455be4adf7c208fccd5a3c17016d036d4821e20", "sheet1"] // 数据源标识,sheet名
  /// dataBind有值的话代表绑定过excel
  final List<String>? dataBind;

  /// 商品标签模板绑定字段
  final String? fieldName;

  final bool? isTitle;

  final String? contentTitle;

  final String fontCode;

  final bool hasVipRes;

  bool get isBindingElement => dataBind?.isNotEmpty ?? false;

  int get bindingColumn {
    if (isBindingElement) {
      if ((value.isEmpty) || (value.contains("⊙"))) {
        return -1;
      }
      CellAddress cellAddress = NiimbotExcelUtils.decodeCellIndex(value);
      return cellAddress.c;
    }
    return -1;
  }

  bool get isBindingCommodity {
    if ((dataBind?.isNotEmpty ?? false) &&
        (dataBind?.firstOrNull?.isEmpty ?? true)) {
      return true;
    }
    return false;
  }

  TableCombineCellElement({
    String? id,
    super.textAlignHorizontal = NetalTextAlign.start,
    super.textAlignVertical,
    super.wordSpacing,
    super.letterSpacing,
    super.lineSpacing,
    super.fontFamily,
    super.fontStyle,
    super.fontSize = 3.2,
    super.lineBreakMode,
    // super.typesettingMode,
    // super.typesettingParam,
    // super.colorReverse,
    // super.write_mode,
    super.value,
    this.dataBind,
    this.isBinding = false,
    this.fieldName = '',
    this.contentTitle,
    this.isTitle = false,
    this.fontCode = 'ZT001',
    this.hasVipRes = false,
  }) : super(id: id ?? ElementUtils.generateId());

  @override
  Map<String, dynamic> toJson() {
    return ({
      ...super.toJson(),
      'dataBind': dataBind,
      'fieldName': fieldName,
      'fontCode': fontCode,
      'hasVipRes': hasVipRes,

      /// 覆盖默认值
      "textAlignVertical": textAlignVertical.value,
    }..removeWhere((k, v) => v == null));
  }

  TableCombineCellElement.fromJson(Map<String, dynamic> json)
      : dataBind = TemplateParseUtils.parseListFromJSON(json['dataBind']),
        isBinding = TemplateParseUtils.parseBoolFromJSON(json['isBinding']),
        fieldName = json['fieldName'],
        contentTitle = json['contentTitle'],
        isTitle = TemplateParseUtils.parseBoolFromJSON(json['isTitle']),
        fontCode = json['fontCode'] ?? 'ZT001',
        hasVipRes = json['hasVipRes'] ?? false,
        super(
          id: json['id'] ?? ElementUtils.generateId(),
          textAlignHorizontal: TemplateParseUtils.parseNetalTextAlignFromJSON(
              json['textAlignHorizonral']),
          textAlignVertical: TemplateParseUtils.parseNetalTextAlignFromJSON(
              json['textAlignVertical']),
          wordSpacing:
              TemplateParseUtils.parseNumberFromJSON(json['wordSpacing']),
          letterSpacing:
              TemplateParseUtils.parseNumberFromJSON(json['letterSpacing']),
          lineSpacing:
              TemplateParseUtils.parseNumberFromJSON(json['lineSpacing']),
          fontFamily: json['fontFamily'],
          fontStyle: TemplateParseUtils.parseNetalTextFontStyleFromJSON(
              json['fontStyle']),
          fontSize: TemplateParseUtils.parseNumberFromJSON(json['fontSize']),
          lineBreakMode: TemplateParseUtils.parseNetalTextLineBreakModeFromJSON(
              json['lineBreakMode']),
          typesettingMode: TemplateParseUtils.parseNetalTypesettingModeFromJSON(
              json['typesettingMode']),
          typesettingParam:
              TemplateParseUtils.parseListFromJSON(json['typesettingParam']),
          colorReverse:
              TemplateParseUtils.parseBoolFromJSON(json['colorReverse']),
          write_mode: TemplateParseUtils.parseNetalTextWriteModeFromJSON(
              json['write_mode']),
          value: json['value'],
        );

  NetalTableCombineCellElement toNetal() {
    return NetalTableCombineCellElement(
      id: id,
      value: StringUtils.covertNewLineChar(value),
      textAlignVertical: textAlignVertical,
      textAlignHorizontal: textAlignHorizontal,
      wordSpacing: wordSpacing,
      letterSpacing: letterSpacing,
      lineSpacing: lineSpacing,
      // 上层使用fontCode标记字体 ZT001表示默认字体即为Harmony
      fontFamily: fontCode == 'ZT001' ? 'Harmony' : fontCode,
      fontStyle: fontStyle,
      fontSize: fontSize,
      lineBreakMode: lineBreakMode,
      typesettingMode: typesettingMode,
      typesettingParam: typesettingParam,
      colorReverse: colorReverse,
      write_mode: write_mode,
    );
  }

  bool get hasVipSource {
    // if ((fontCode.isNotEmpty) && FontManager().isFontVip(fontCode)) {
    //   return true;
    // }
    return hasVipRes;
  }

  TableCombineCellElement copyWith({
    String? id,
    bool? isBinding,
    CopyWrapper<List<String>?>? dataBind,
    String? fieldName,
    bool? isTitle,
    String? contentTitle,
    String? value,
    String? fontFamily,
    String? fontCode,
    bool? hasVipRes,
    NetalTextAlign? textAlignHorizontal,
    NetalTextAlign? textAlignVertical,
    num? wordSpacing,
    num? letterSpacing,
    num? lineSpacing,
    num? fontSize,
    NetalTextLineBreakMode? lineBreakMode,
    List<NetalTextFontStyle>? fontStyle,
  }) {
    return TableCombineCellElement(
      textAlignHorizontal: textAlignHorizontal ?? this.textAlignHorizontal,
      textAlignVertical: textAlignVertical ?? this.textAlignVertical,
      wordSpacing: wordSpacing ?? this.wordSpacing,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      lineSpacing: lineSpacing ?? this.lineSpacing,
      fontFamily: fontFamily ?? this.fontFamily,
      fontCode: fontCode ?? this.fontCode,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      fontSize: fontSize ?? this.fontSize,
      lineBreakMode: lineBreakMode ?? this.lineBreakMode,
      id: id ?? this.id,
      isBinding: isBinding ?? this.isBinding,
      dataBind: dataBind != null ? dataBind.value : this.dataBind,
      fieldName: fieldName ?? this.fieldName,
      isTitle: isTitle ?? this.isTitle,
      contentTitle: contentTitle ?? this.contentTitle,
      value: value ?? this.value,
      fontStyle: fontStyle ?? this.fontStyle,
    );
  }

  @override
  List<Object?> get props => [
        id,
        value,
        colorReverse,
        write_mode,
        typesettingMode,
        typesettingParam,
        textAlignHorizontal,
        textAlignVertical,
        wordSpacing,
        letterSpacing,
        lineSpacing,
        fontFamily,
        fontCode,
        hasVipRes,
        fontStyle,
        fontSize,
        isTitle,
        contentTitle,
        lineBreakMode,
        boxStyle,
        textStyle,
        isBinding,
        dataBind,
        fieldName,
        isTitle,
        contentTitle
      ];
}
