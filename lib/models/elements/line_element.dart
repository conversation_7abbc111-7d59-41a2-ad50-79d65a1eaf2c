import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/models/netal_line_element.dart';
import 'package:netal_plugin/utils/ratio.dart';

import 'package:niimbot_template/models/elements/color_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

/// 线条元素
@immutable
class LineElement extends ColorElement implements NetalLineElement {
  @override
  final List<num> dashWidth;

  @override
  final NetalLineType lineType;

  LineElement({
    super.id,
    super.x,
    super.y,
    super.width = 10,
    super.height = 0.4,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    // super.paperColorIndex,
    super.hasVipRes,
    super.colorChannel,
    super.elementColor,
    NetalLineType? lineType,
    List<num>? dashWidth,
    super.type = NetalElementType.line,
  })  : lineType = lineType ?? NetalLineType.solid,
        dashWidth = dashWidth ?? const [0.75, 0.75];

  @override
  LineElement.fromJson(super.json)
      : lineType =
            TemplateParseUtils.parseNetalLineTypeFromJSON(json['lineType']) ??
                NetalLineType.solid,
        dashWidth = _parseDashWidth(json['dashwidth']),
        super.fromJson();

  @override
  Map<String, dynamic> toJson() {
    return ({
      ...super.toJson(),
      "lineType": lineType.value,
      "lineWidth": height,
      "dashwidth": dashWidth.map((e) => e.digits(1)).toList(),
    }..removeWhere((k, v) => v == null));
  }

  @override
  NetalLineElement toNetal() {
    return NetalLineElement(
      x: x,
      y: y,
      rotate: rotate,
      colorChannel: colorChannel,
      elementColor: elementColor,
      width: width,
      height: height,
      lineType: lineType,
      dashWidth: dashWidth,
    );
  }

  LineElement copyWith({
    num? x,
    num? y,
    int? rotate,
    num? width,
    num? height,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    Color? elementColor,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
    int? colorChannel,
    List<num>? dashWidth,
    NetalLineType? lineType,
  }) {
    return LineElement(
      x: x ?? this.x,
      y: y ?? this.y,
      rotate: rotate ?? this.rotate,
      width: width ?? this.width,
      height: height ?? this.height,
      id: id ?? this.id,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      dashWidth: dashWidth ?? this.dashWidth,
      lineType: lineType ?? this.lineType,
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        dashWidth,
        lineType,
      ];
}

List<num> _parseDashWidth(dynamic dashWidth) {
  final data = TemplateParseUtils.parseListFromJSON<num>(dashWidth);
  if (data?.isNotEmpty ?? false) {
    return data!;
  }
  return const [0.75, 0.75];
}
