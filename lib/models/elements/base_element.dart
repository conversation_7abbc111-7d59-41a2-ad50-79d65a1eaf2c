import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:netal_plugin/models/netal_element_base.dart';
import 'package:netal_plugin/models/netal_enum.dart';

import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';
import 'package:niimbot_template/utils/element_utils.dart';

/// 基础元素
@immutable
abstract class BaseElement extends NetalElementBase with EquatableMixin {
  /// 元素Id
  final String id;

  /// 是否锁定
  final bool isLock;

  /// 是否开启镜像
  final bool isOpenMirror;

  /// 元素镜像类型
  final ElementMirrorType mirrorType;

  /// 是否为vip元素
  final bool hasVipRes;

  BaseElement({
    super.x,
    super.y,
    super.rotate,
    required super.width,
    required super.height,
    required super.type,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
  }) : id = id ?? ElementUtils.generateId(),
       isLock = isLock ?? false,
       isOpenMirror = isOpenMirror ?? false,
       mirrorType = mirrorType ?? ElementMirrorType.canvasCenter,
       hasVipRes = hasVipRes ?? false;

  @override
  Map<String, dynamic> toJson() {
    return {
      ...super.toJson(),
      'id': id,
      'isLock': isLock ? 1 : 0,
      'isOpenMirror': isOpenMirror ? 1 : 0,
      'mirrorType': mirrorType.value,
      'hasVipRes': hasVipRes,
    };
  }

  BaseElement.fromJson(Map<String, dynamic> json)
    : id =
          TemplateParseUtils.parseStringFromJSON(json['id']) ??
          ElementUtils.generateId(),
      isLock = TemplateParseUtils.parseBoolFromJSON(json['isLock']),
      isOpenMirror = TemplateParseUtils.parseBoolFromJSON(json['isOpenMirror']),
      mirrorType = _parseMirrorType(json['mirrorType']),
      hasVipRes = TemplateParseUtils.parseBoolFromJSON(json['hasVipRes']),
      super(
        x: TemplateParseUtils.parseNumberFromJSON(json['x']),
        y: TemplateParseUtils.parseNumberFromJSON(json['y']),
        rotate: TemplateParseUtils.parseNumberFromJSON(json['rotate'])?.toInt(),
        type: NetalElementType.values.byName(json['type']),
        width: TemplateParseUtils.parseNumberFromJSON(json['width']) ?? 0,
        height: TemplateParseUtils.parseNumberFromJSON(json['height']) ?? 0,
      );

  BaseElement copyWith({
    num? x,
    num? y,
    int? rotate,
    num? width,
    num? height,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
  });

  bool get hasVipSource => hasVipRes;

  bool get isBindingElement => false;

  bool get isBindingCommodity => false;

  Rect get rect {
    return Rect.fromLTWH(
      x.toDouble(),
      y.toDouble(),
      width.toDouble(),
      height.toDouble(),
    );
  }

  @override
  List<Object?> get props => [
    x,
    y,
    width,
    height,
    type,
    rotate,
    id,
    isLock,
    isOpenMirror,
    mirrorType,
    hasVipRes,
  ];
}

ElementMirrorType _parseMirrorType(dynamic mirrorType) {
  return ElementMirrorType.byValue(
        TemplateParseUtils.parseNumberFromJSON(mirrorType)?.toInt() ?? 0,
      ) ??
      ElementMirrorType.canvasCenter;
}
