import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/models/netal_table_element.dart';
import 'package:netal_plugin/utils/ratio.dart';

import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/table_cell_element.dart';
import 'package:niimbot_template/models/elements/table_combine_cell_element.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

Logger _logger = Logger("CellPosition", on: kDebugMode);

class CellPosition {
  int columnIndex;
  int rowIndex;

  CellPosition(this.columnIndex, this.rowIndex);

  @override
  String toString() {
    return 'CellPosition(columnIndex:$columnIndex, rowIndex:$rowIndex)';
  }
}

/// 表格元素
@immutable
class TableElement<C extends TableCellElement,
        CC extends TableCombineCellElement> extends BaseElement
    implements NetalTableElement<C, CC> {
  @override
  final List<C> cells;
  @override
  final List<CC> combineCells;

  @override
  int get column => columnWidth.length;

  @override
  final List<num> columnWidth;

  @override
  final Color contentColor;

  @override
  final int contentColorChannel;

  @override
  final Color lineColor;

  @override
  final int lineColorChannel;

  @override
  final NetalLineType lineType;
  @override
  final num lineWidth;

  @override
  int get row => rowHeight.length;

  @override
  final List<num> rowHeight;

  @override
  num get width => _calc(columnWidth, lineWidth);

  @override
  num get height => _calc(rowHeight, lineWidth);

  /// 表格bar部分的宽高
  double get tableBarHeight => (height / row / 2).mm2px().toDouble();

  /// 获取所有单元格
  List<TableCombineCellElement> get allCells {
    return [...combineCells, ...cells];
  }

  ///获取表格最右下角单元格，用于添加行列时属性继承
  TableCellElement getBottomRightCell() {
    TableCellElement cell = cells.firstWhere((element) =>
        element.rowIndex == row - 1 && element.columnIndex == column - 1);
    return cell;
  }

  /// 获取合并单元格的原始单元格
  List<TableCellElement> getCellsOfCombine(String combineId) {
    return cells.where((e) => e.combineId == combineId).toList();
  }

  TableCellElement? getFirstCellOfCombine(String combineId) {
    final cells = getCellsOfCombine(combineId);
    var first = cells.firstOrNull;
    if (first != null) {
      for (var i = 1; i < cells.length; i++) {
        if (cells[i].rowIndex < first!.rowIndex ||
            cells[i].columnIndex < first.columnIndex) {
          first = cells[i];
        }
      }
      return first;
    }
    return null;
  }

  /// 获取单元格的行
  List<int> getCellRowIndex(TableCombineCellElement cell) {
    final _index = getCellIndex(cell);
    return _index.first;
  }

  /// 获取单元格的列
  List<int> getCellColumnIndex(TableCombineCellElement cell) {
    final _index = getCellIndex(cell);
    return _index.last;
  }

  /// 获取单元格索引
  /// 返回单元格索引 [[起始行,结束行],[起始列,结束列]]
  List<List<int>> getCellIndex(TableCombineCellElement cell) {
    if (cell is TableCellElement) {
      return [
        [cell.rowIndex, cell.rowIndex],
        [cell.columnIndex, cell.columnIndex]
      ];
    }
    final _cells = getCellsOfCombine(cell.id);
    final _index = [
      [_cells.first.rowIndex, _cells.first.rowIndex],
      [_cells.first.columnIndex, _cells.first.columnIndex]
    ];
    for (var i = 1; i < _cells.length; i++) {
      if (_cells[i].rowIndex < _index.first.first) {
        _index[0][0] = _cells[i].rowIndex;
      }
      if (_cells[i].rowIndex > _index.first.last) {
        _index[0][1] = _cells[i].rowIndex;
      }
      if (_cells[i].columnIndex < _index.last.first) {
        _index[1][0] = _cells[i].columnIndex;
      }
      if (_cells[i].columnIndex > _index.last.last) {
        _index[1][1] = _cells[i].columnIndex;
      }
    }
    return _index;
  }

  /// 获取单元格的高度
  num getCellHeight(List<int> rowIndex) {
    int max = rowIndex.last;
    int min = rowIndex.first;
    return rowHeight.slice(min, max + 1).sum + (max - min) * lineWidth;
  }

  /// 获取单元格宽度
  num getCellWidth(List<int> columnIndex) {
    int max = columnIndex.last;
    int min = columnIndex.first;
    return columnWidth.slice(min, max + 1).sum + (max - min) * lineWidth;
  }

  /// 获取单元格X坐标
  num getCellX(List<int> columnIndex) {
    return columnWidth.slice(0, columnIndex.first).sum +
        lineWidth * (columnIndex.first + 1);
  }

  /// 获取单元格Y坐标
  num getCellY(List<int> rowIndex) {
    return rowHeight.slice(0, rowIndex.first).sum +
        lineWidth * (rowIndex.first + 1);
  }

  /// 是否包含内容
  bool get hasContent {
    bool has = false;
    for (final cell in [...combineCells, ...cells]) {
      if (cell.value.isNotEmpty) {
        has = true;
        break;
      }
    }
    return has;
  }

  TableElement({
    super.id,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    super.x,
    super.y,
    super.rotate,
    List<num>? rowHeight,
    List<num>? columnWidth,
    num? lineWidth,
    NetalLineType? lineType,
    Color? lineColor,
    Color? contentColor,
    int? lineColorChannel,
    int? contentColorChannel,
    List<C>? cells,
    List<CC>? combineCells,
  })  : rowHeight = rowHeight ?? const [3.75, 3.75, 3.75],
        columnWidth = columnWidth ?? const [15, 15],
        lineWidth = lineWidth ?? 0.4,
        lineType = lineType ?? NetalLineType.solid,
        lineColor = lineColor ?? const Color(0xFF000000),
        contentColor = contentColor ?? const Color(0xFF000000),
        lineColorChannel = lineColorChannel ?? 0,
        contentColorChannel = contentColorChannel ?? 0,
        cells = cells ??
            List.generate(
              6,
              (i) => TableCellElement(
                rowIndex: i ~/ 2,
                columnIndex: i % 2,
              ),
            ) as List<C>,
        combineCells = combineCells ?? const [],
        super(type: NetalElementType.table, width: 0, height: 0);

  TableElement.fromJson(super.json)
      : rowHeight = TemplateParseUtils.parseListFromJSON(json['rowHeight']) ??
            [3.75, 3.75, 3.75],
        columnWidth =
            TemplateParseUtils.parseListFromJSON(json['columnWidth']) ??
                [15, 15],
        lineWidth =
            TemplateParseUtils.parseNumberFromJSON(json['lineWidth']) ?? 0.4,
        lineType =
            TemplateParseUtils.parseNetalLineTypeFromJSON(json['lineType']) ??
                NetalLineType.solid,
        lineColor = TemplateParseUtils.parseColorFromJSON(json['lineColor']) ??
            const Color(0xFF000000),
        contentColor =
            TemplateParseUtils.parseColorFromJSON(json['contentColor']) ??
                const Color(0xFF000000),
        lineColorChannel =
            TemplateParseUtils.parseNumberFromJSON(json['lineColorChannel'])
                    ?.toInt() ??
                0,
        contentColorChannel =
            TemplateParseUtils.parseNumberFromJSON(json['contentColorChannel'])
                    ?.toInt() ??
                0,
        cells = _parseCells(json['cells']) as List<C>,
        combineCells = _parseCombineCells(json['combineCells']) as List<CC>,
        super.fromJson();

  @override
  Map<String, dynamic> toJson() {
    return ({
      ...super.toJson(),
      'row': row,
      'column': column,
      'rowHeight': rowHeight.map((e) => e.digits(6)).toList(),
      'columnWidth': columnWidth.map((e) => e.digits(6)).toList(),
      'lineType': lineType.value,
      'lineWidth': lineWidth.digits(2),
      'lineColor': [
        lineColor.alpha,
        lineColor.red,
        lineColor.green,
        lineColor.blue
      ],
      'contentColor': [
        contentColor.alpha,
        contentColor.red,
        contentColor.green,
        contentColor.blue
      ],
      'lineColorChannel': lineColorChannel,
      'contentColorChannel': contentColorChannel,
      'cells': cells.map((e) => e.toJson()).toList(),
      'combineCells': combineCells.map((e) => e.toJson()).toList(),
      // 'allowFreeZoom': allowFreeZoom,
      'width': width.digits(6),
      'height': height.digits(6),
    }..removeWhere((k, v) => v == null));
  }

  @override
  NetalTableElement toNetal() {
    final lineSize = lineWidth == 0.2 ? 0.199999 : lineWidth;
    return NetalTableElement(
      x: x,
      y: y,
      rotate: rotate,
      rowHeight: rowHeight,
      columnWidth: columnWidth,
      lineWidth: lineSize,
      lineType: lineType,
      lineColor: lineColor,
      contentColor: contentColor,
      lineColorChannel: lineColorChannel,
      contentColorChannel: contentColorChannel,
      cells: cells.map((e) => e.toNetal()).toList(),
      combineCells: combineCells.map((e) => e.toNetal()).toList(),
    );
  }

  TableElement copyWith({
    num? x,
    num? y,
    int? rotate,
    num? width,
    num? height,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
    List<C>? cells,
    List<num>? columnWidth,
    List<CC>? combineCells,
    Color? contentColor,
    int? contentColorChannel,
    Color? lineColor,
    int? lineColorChannel,
    NetalLineType? lineType,
    num? lineWidth,
    List<num>? rowHeight,
  }) {
    return TableElement(
      id: id ?? this.id,
      x: x ?? this.x,
      y: y ?? this.y,
      rotate: rotate ?? this.rotate,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      cells: cells ?? this.cells.map((e) => e.copyWith()).toList(),
      columnWidth: columnWidth ?? this.columnWidth,
      combineCells:
          combineCells ?? this.combineCells.map((e) => e.copyWith()).toList(),
      contentColor: contentColor ?? this.contentColor,
      contentColorChannel: contentColorChannel ?? this.contentColorChannel,
      lineColor: lineColor ?? this.lineColor,
      lineColorChannel: lineColorChannel ?? this.lineColorChannel,
      lineType: lineType ?? this.lineType,
      lineWidth: lineWidth ?? this.lineWidth,
      rowHeight: rowHeight ?? this.rowHeight,
    );
  }

  @override
  bool get hasVipSource {
    ///只要有一个单元格有vip字体就返回true
    /// 如果有选中单元格的话，则判定选中单元格中是否有vip字体
    for (var cell in cells) {
      if (cell.combineId?.isEmpty ?? true) {
        if (cell.hasVipSource) {
          return true;
        }
      }
    }

    for (var cell in combineCells) {
      if (cell.hasVipSource) {
        return true;
      }
    }
    return false;
  }

  @override
  bool get isBindingElement {
    return getBindingExcelCells().isNotEmpty ||
        getBindingExcelCombineCells().isNotEmpty;
  }

  List<TableCellElement> get getBingCommodityCells {
    return cells.where((element) => element.isBindingCommodity).toList();
  }

  List<TableCombineCellElement> get getBingCommodityCombineCells {
    return combineCells.where((element) => element.isBindingCommodity).toList();
  }

  @override
  bool get isBindingCommodity {
    return getBingCommodityCells.isNotEmpty ||
        getBingCommodityCombineCells.isNotEmpty;
  }

  List<TableCellElement> getBindingExcelCells() {
    return cells.where((element) => element.isBindingElement).toList();
  }

  List<TableCombineCellElement> getBindingExcelCombineCells() {
    return combineCells.where((element) => element.isBindingElement).toList();
  }

  List<TableCellElement> getBindingCommodidyCells() {
    return cells.where((element) => element.isBindingCommodity).toList();
  }

  List<TableCombineCellElement> getBindingCommodidyCombineCells() {
    return combineCells.where((element) => element.isBindingCommodity).toList();
  }

  @override
  List<Object?> get props => [
        ...super.props,
        cells,
        combineCells,
        // allowFreeZoom,
        columnWidth,
        contentColor,
        contentColorChannel,
        lineColor,
        lineColorChannel,
        lineType,
        lineWidth,
        rowHeight
      ];
}

num _calc(List<num> val, num lineWidth) {
  return val.reduce((a, c) => a + c) + lineWidth * (val.length + 1);
}

List<TableCellElement> _parseCells(dynamic value) {
  if (value is List) {
    return value.map((e) {
      return TableCellElement.fromJson(e);
    }).toList();
  }
  return [];
}

List<TableCombineCellElement> _parseCombineCells(dynamic value) {
  if (value is List) {
    return value.map((e) {
      return TableCombineCellElement.fromJson(e);
    }).toList();
  }
  return [];
}
