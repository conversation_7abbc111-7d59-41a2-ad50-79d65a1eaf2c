import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

/// 素材元素
@immutable
class MaterialElement extends ImageElement {
  //素材id有值的情况下类型：图标=1，边框=2; 素材id 为空的情况下图片=1,涂鸦=2, 类型为2的能自由拉伸
  final MaterialElementType materialType;

  ///素材库图片的id，不是素材库图片时为空或"0"
  final int materialId;

  @override
  final bool colorReverse;

  /// 是否允许自由拉伸
  @override
  final bool allowFreeZoom;

  MaterialElement({
    super.id,
    super.x,
    super.y,
    required super.width,
    required super.height,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    super.colorChannel,
    super.elementColor,
    required super.imageData,
    super.imageProcessingType = NetalImageRenderType.threshold,
    super.imageProcessingValue = const [127],
    super.localImageUrl = '',
    super.imageUrl = '',
    this.colorReverse = false,
    required this.materialId,
    required this.materialType,
    this.allowFreeZoom = false,
  });

  @override
  MaterialElement.fromJson(super.json)
      : materialId = TemplateParseUtils.parseNumberFromJSON(json['materialId'])
                ?.toInt() ??
            0,
        materialType = _parseMaterialType(
            TemplateParseUtils.parseNumberFromJSON(json['materialType'])),
        colorReverse =
            TemplateParseUtils.parseBoolFromJSON(json['colorReverse']),
        allowFreeZoom = false,
        super.fromJson();

  @override
  Map<String, dynamic> toJson() {
    return ({
      ...super.toJson(),
      'materialId': materialId,
      'colorReverse': colorReverse,
      'materialType': materialType.value,
    }..removeWhere((k, v) => v == null));
  }

  @override
  MaterialElement copyWith({
    num? x,
    num? y,
    int? rotate,
    num? width,
    num? height,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    Color? elementColor,
    String? value,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
    int? colorChannel,
    NetalImageRenderType? imageProcessingType,
    List<num>? imageProcessingValue,
    String? imageData,
    CopyWrapper<String?>? imageUrl,
    int? materialId,
    String? localImageUrl,
    // int? paperColorIndex,
    bool? allowFreeZoom,
    bool? colorReverse,
    MaterialElementType? materialType,
  }) {
    return MaterialElement(
      x: x ?? this.x,
      y: y ?? this.y,
      rotate: rotate ?? this.rotate,
      width: width ?? this.width,
      height: height ?? this.height,
      id: id ?? this.id,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      imageProcessingType: imageProcessingType ?? this.imageProcessingType,
      imageProcessingValue: imageProcessingValue ?? this.imageProcessingValue,
      imageData: imageData ?? this.imageData,
      imageUrl: imageUrl != null ? imageUrl.value : this.imageUrl,
      materialId: materialId ?? this.materialId,
      localImageUrl: localImageUrl ?? this.localImageUrl,
      colorReverse: colorReverse ?? this.colorReverse,
      // paperColorIndex: paperColorIndex ?? this.paperColorIndex,
      materialType: materialType ?? this.materialType,
      allowFreeZoom: allowFreeZoom ?? this.allowFreeZoom,
    );
  }

  ///是否是边框类型的元素
  bool get isMaterialBorder {
    if (materialType == MaterialElementType.border) {
      return true;
    }
    return false;
  }

  bool get isMaterialIcon {
    if (materialId == 0) {
      return false;
    }
    return materialType == MaterialElementType.icon;
  }

  @override
  bool get hasVipSource {
    // MaterialCacheData? cacheData = isMaterialBorder
    //     ? MaterialManager.sharedInstance().materialBoderCacheData
    //     : MaterialManager.sharedInstance().materialCacheData;

    // if ((materialId.isNotEmpty) &&
    //     (cacheData?.vipIdList.any((id) => id.toString() == materialId) ??
    //         false)) {
    //   return true;
    // }
    return hasVipRes;
  }

  @override
  List<Object?> get props => [
        ...super.props,
        materialType,
        materialId,
        colorReverse,
      ];
}

MaterialElementType _parseMaterialType(dynamic field) {
  if (field is num) {
    return (MaterialElementType.byValue(field.toInt()) ??
        MaterialElementType.icon);
  }
  return MaterialElementType.icon;
}
