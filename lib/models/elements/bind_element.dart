import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:niimbot_excel/models/interface.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';

import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/value_element.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

/// 绑定元素
@immutable
abstract class BindElement extends ValueElement {
  /// excel 列绑定信息
  final bool isBinding;

  /// 第一个参数为excel唯一表示hash值，第二个参数为excel中第一个sheet的名字 eg:"dataBind": ["d455be4adf7c208fccd5a3c17016d036d4821e20", "sheet1"] // 数据源标识,sheet名
  /// dataBind有值的话代表绑定过excel
  final List<String>? dataBind;

  /// 商品标签模板绑定字段
  final String? fieldName;

  final bool escapeValue = false;

  final bool compactOldTemplate = false;

  BindElement({
    super.id,
    super.x,
    super.y,
    super.rotate,
    super.colorChannel,
    super.elementColor,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    required super.width,
    required super.height,
    required super.type,
    required super.value,
    this.dataBind,
    this.isBinding = false,
    this.fieldName,
  });

  @override
  bool get isBindingElement => dataBind?.isNotEmpty ?? false;

  int get bindingColumn {
    if (isBindingElement && value != null) {
      if ((value!.isEmpty) || (value!.contains("⊙"))) {
        return -1;
      }
      final cellAddress = NiimbotExcelUtils.decodeCellIndex(value!);
      return cellAddress.c;
    }
    return -1;
  }

  @override
  bool get isBindingCommodity {
    if ((dataBind?.isNotEmpty ?? false) &&
        (dataBind?.firstOrNull?.isEmpty ?? true)) {
      return true;
    }
    return false;
  }

  @override
  BindElement.fromJson(super.json)
      : isBinding = TemplateParseUtils.parseBoolFromJSON(json['isBinding']),
        fieldName = json['fieldName'],
        dataBind = TemplateParseUtils.parseListFromJSON(json['dataBind']),
        super.fromJson();

  @override
  Map<String, dynamic> toJson() {
    return ({
      ...super.toJson(),
      'dataBind': dataBind,
      'fieldName': fieldName,
    }..removeWhere((k, v) => v == null));
  }

  @override
  BindElement copyWith({
    num? x,
    num? y,
    int? rotate,
    num? width,
    num? height,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
    bool? isBinding,
    CopyWrapper<List<String>?>? dataBind,
    CopyWrapper<String?>? fieldName,
    String? value,
    int? colorChannel,
    Color? elementColor,
  });

  @override
  List<Object?> get props => [
        ...super.props,
        isBinding,
        dataBind,
        fieldName,
        escapeValue,
        compactOldTemplate
      ];
}
