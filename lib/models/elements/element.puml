@startuml element
'https://plantuml.com/class-diagram
!include netal.puml

class BaseElement {
    +String id
    +bool isLock
    +bool isOpenMirror
    +String? mirrorId
    +ElementMirrorType mirrorType
    +bool hasVipRes
    +Map<String, dynamic> toJson()
    +BaseElement fromJson(Map<String, dynamic> json)
    +NetalElementBase toNetal()
    +BaseElement copyWith()
    +double getItemDisplayMinWidth(BuildContext context)
    +double getItemDisplayMaxWidth()
    +double getItemDisplayMinHeight()
    +double getItemDisplayMaxHeight()
    +BoxConstraints getBoxConstrains(BuildContext context)
    +bool hasVipSource()
    +bool isMirrorElement()
    +String generateFixedMirrorId()
}
NetalElementBase <|-- BaseElement

class ValueElement {
    +String value
    +Map<String, dynamic> toJson()
    +ValueElement fromJson(Map<String, dynamic> json)
}
ColorElement <|-- ValueElement
NetalValueElement <|-- ValueElement

class BindElement {
    +bool isBinding
    +List<String>? dataBind
    +String fieldName
    +int bindingColumn
    +bool isBindingElement
    +bool isBindingCommodity
    +bool escapeValue
    +BindElement fromJson(Map<String, dynamic> json)
    +Map<String, dynamic> toJson()
    +Map<String, dynamic> antiEscapeValueToJson()
}

ValueElement <|-- BindElement

class ColorElement {
    +int colorChannel
    +Color elementColor
    +int paperColorIndex
    +ColorElement fromJson(Map<String, dynamic> json)
    +Map<String, dynamic> toJson()
}
BaseElement <|-- ColorElement
NetalColorElement <|-- ColorElement

class TextElement {
    +bool colorReverse
    +NetalTextWriteMode write_mode
    +NetalTypesettingMode typesettingMode
    +List<num> typesettingParam
    +NetalTextAlign textAlignHorizontal
    +NetalTextAlign textAlignVertical
    +NetalTextLineMode lineMode
    +num wordSpacing
    +num letterSpacing
    +num lineSpacing
    +String fontFamily
    +String fontCode
    +List<NetalTextFontStyle> fontStyle
    +num fontSize
    +List<int>? fontColor
    +bool? isTitle
    +String? contentTitle
    +NetalTextLineBreakMode lineBreakMode
    +double lastWidth
    +double lastHeight
    +TextElement fromJson(Map<String, dynamic> json)
    +Map<String, dynamic> toJson()
    +NetalTextElement toNetal()
    +TextElement copyWith()
}
BindElement <|-- TextElement
NetalTextElement <|-- TextElement

class DateElement {
    +ElementDateFormat dateFormat
    +ElementTimeFormat? timeFormat
    +int time
    +bool dateIsRefresh
    +String get value
    +DateElement fromJson(Map<String, dynamic> json)
    +Map<String, dynamic> toJson()
    +NetalTextElement toNetal()
    +DateElement copyWith()
}
TextElement <|-- DateElement

class LineElement {
    +List<num> dashWidth
    +NetalLineType lineType
    +LineElement fromJson(Map<String, dynamic> json)
    +Map<String, dynamic> toJson()
    +NetalLineElement toNetal()
    +LineElement copyWith()
}
ColorElement <|-- LineElement
NetalLineElement <|-- LineElement

class GraphElement {
    +num cornerRadius
    +NetalGraphType graphType
    +num lineWidth
    +GraphElement fromJson(Map<String, dynamic> json)
    +Map<String, dynamic> toJson()
    +NetalGraphElement toNetal()
    +GraphElement copyWith()
}
LineElement <|-- GraphElement
NetalGraphElement <|-- GraphElement

class ImageElement {
    +NetalImageRenderType imageProcessingType
    +List<num> imageProcessingValue
    +String imageData
    +String? imageUrl
    +String? materialId
    +bool allowFreeZoom
    +String localUrl
    +ImageElement fromJson(Map<String, dynamic> json)
    +Map<String, dynamic> toJson()
    +NetalImageElement toNetal()
    +ImageElement copyWith()
}
ColorElement <|-- ImageElement
NetalImageElement <|-- ImageElement

class QRCodeElement {
    +NetalQRCodeType codeType
    +NetalQRCodeCorrectLevel correctLevel
    +QRCodeElement fromJson(Map<String, dynamic> json)
    +Map<String, dynamic> toJson()
    +NetalQRCodeElement toNetal()
    +QRCodeElement copyWith()
}
BindElement <|-- QRCodeElement
NetalQRCodeElement <|-- QRCodeElement

class BarCodeElement {
    +NetalBarcodeType codeType
    +num fontSize
    +num textHeight
    +NetalBarcodeTextPosition textPosition
    +bool checkResult
    +BarCodeElement fromJson(Map<String, dynamic> json)
    +Map<String, dynamic> toJson()
    +NetalBarcodeElement toNetal()
    +BarCodeElement copyWith()
}
BindElement <|-- BarCodeElement
NetalBarcodeElement <|-- BarCodeElement

class SerialElement {
    +String? prefix
    +String startNumber
    +String? suffix
    +int incrementValue
    +int fixLength
    +String fixValue
    +int index
    +String get value
    +SerialElement fromJson(Map<String, dynamic> json)
    +Map<String, dynamic> toJson()
    +NetalTextElement toNetal()
    +SerialElement copyWith()
}
TextElement <|-- SerialElement

class TableCellElement {
    +int columnIndex
    +String? combineId
    +int rowIndex
    +TableCellElement fromJson(Map<String, dynamic> json)
    +Map<String, dynamic> toJson()
    +NetalTableCellElement toNetal()
    +TableCellElement copyWith()
}
TableCombineCellElement <|-- TableCellElement
NetalTableCellElement <|-- TableCellElement

class TableCombineCellElement {
    +bool isBinding
    +List<String>? dataBind;
    +String fieldName
    +bool focused
    +TableElement? tableElement
    +bool get isBindingElement
    +int get bindingColumn
    +NetalImageResult? image
    +Map<String, dynamic> toJson()
    +TableCombineCellElement fromJson(Map<String, dynamic> json)
    +NetalTableCombineCellElement toNetal()
    +TableCombineCellElement copyWith()
    +bool hasVipSource()
    +resetImageCache()
}
NetalTableCombineCellElement <|-- TableCombineCellElement

class TableElement {
    +List<TableCellElement> cells
    +List<TableCombineCellElement> combineCells
    +bool allowFreeZoom
    +int get column
    +List<num> columnWidth
    +Color contentColor
    +int contentColorChannel
    +Color lineColor
    +int lineColorChannel
    +NetalLineType lineType
    +num lineWidth
    +int get row
    +List<num> rowHeight
    +num get width
    +num get height
    +TableElement.fromJson(Map<String, dynamic> json)
    +Map<String, dynamic> toJson()
    +NetalTableElement toNetal()
    +TableElement copyWith()
}
BaseElement <|-- TableElement
NetalTableElement <|-- TableElement
TableCellElement o-- TableElement
TableCombineCellElement o-- TableElement
@enduml