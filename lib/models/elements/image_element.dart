import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/models/netal_image_element.dart';

import 'package:niimbot_template/models/elements/color_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

/// 图片元素
@immutable
class ImageElement extends ColorElement implements NetalImageElement {
  @override
  final NetalImageRenderType imageProcessingType;

  @override
  final List<num> imageProcessingValue;

  @override
  final bool colorReverse;

  @override
  final String imageData;

  ///图片保存在服务器的路径（素材已经保存在服务器不用重复上传服务器，本地图片需要上传oss服务器）
  final String? imageUrl;

  /// 是否允许自由拉伸
  final bool allowFreeZoom;

  ///图片本地保存路径
  @override
  final String localImageUrl;

  ImageElement({
    super.id,
    super.x,
    super.y,
    required super.width,
    required super.height,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    // super.paperColorIndex,
    super.hasVipRes,
    super.colorChannel,
    super.elementColor,
    required this.imageData,
    NetalImageRenderType? imageProcessingType,
    List<num>? imageProcessingValue,
    this.allowFreeZoom = true,
    this.localImageUrl = '',
    this.imageUrl = '',
    this.colorReverse = false,
  })  : imageProcessingType =
            imageProcessingType ?? NetalImageRenderType.grayscale,
        imageProcessingValue = imageProcessingValue ?? const [5],
        super(type: NetalElementType.image);

  ImageElement.fromJson(super.json)
      : imageProcessingType =
            _parseImageProcessingType(json['imageProcessingType']),
        imageProcessingValue = TemplateParseUtils.parseListFromJSON(
                json['imageProcessingValue']) ??
            [127],

        ///允许自由拉伸
        allowFreeZoom =
            TemplateParseUtils.parseBoolFromJSON(json['allowFreeZoom']),
        localImageUrl =
            TemplateParseUtils.parseStringFromJSON(json["localImageUrl"]) ?? '',
        imageUrl = json["imageUrl"],
        imageData =
            TemplateParseUtils.parseStringFromJSON(json["imageData"]) ?? '',
        colorReverse = TemplateParseUtils.parseBoolFromJSON(json['colorReverse']),
        super.fromJson();

  @override
  Map<String, dynamic> toJson() {
    return ({
      ...super.toJson(),
      "imageData": imageData,
      'imageProcessingType': imageProcessingType.value,
      "imageProcessingValue": imageProcessingValue,
      'imageUrl': imageUrl,
      'localImageUrl': localImageUrl,
      // 'localUrl': localUrl,
      // 'allowFreeZoom': allowFreeZoom,
      'colorReverse': colorReverse,
    }..removeWhere((k, v) => v == null));
  }

  @override
  NetalImageElement toNetal() {
    return NetalImageElement(
      x: x,
      y: y,
      rotate: rotate,
      elementColor: elementColor,
      colorChannel: colorChannel,
      width: width,
      height: height,
      imageData: imageData,
      localImageUrl: localImageUrl,
      imageProcessingType: imageProcessingType,
      imageProcessingValue: imageProcessingValue,
      colorReverse: colorReverse,
    );
  }

  ImageElement copyWith({
    num? x,
    num? y,
    int? rotate,
    num? width,
    num? height,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    Color? elementColor,
    String? value,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
    int? colorChannel,
    NetalImageRenderType? imageProcessingType,
    List<num>? imageProcessingValue,
    String? imageData,
    String? localImageUrl,
    CopyWrapper<String?>? imageUrl,
    // int? paperColorIndex,
    bool? allowFreeZoom,
    bool? colorReverse,
  }) {
    return ImageElement(
      x: x ?? this.x,
      y: y ?? this.y,
      rotate: rotate ?? this.rotate,
      width: width ?? this.width,
      height: height ?? this.height,
      id: id ?? this.id,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      imageProcessingType: imageProcessingType ?? this.imageProcessingType,
      imageProcessingValue: imageProcessingValue ?? this.imageProcessingValue,
      imageData: imageData ?? this.imageData,
      imageUrl: imageUrl != null ? imageUrl.value : this.imageUrl,
      localImageUrl: localImageUrl ?? this.localImageUrl,
      allowFreeZoom: allowFreeZoom ?? this.allowFreeZoom,
      colorReverse: colorReverse ?? this.colorReverse,
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        imageProcessingType,
        imageProcessingValue,
        imageData,
        imageUrl,
        allowFreeZoom,
        localImageUrl
      ];
}

NetalImageRenderType _parseImageProcessingType(dynamic imageProcessingType) {
  return NetalImageRenderType.byValue(
          TemplateParseUtils.parseNumberFromJSON(imageProcessingType)
                  ?.toInt() ??
              1) ??
      NetalImageRenderType.threshold;
}
