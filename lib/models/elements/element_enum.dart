import 'package:collection/collection.dart';

/// 镜像方式
enum ElementMirrorType {
  /// 画板中心点镜像
  canvasCenter(0),

  /// 画板中心 y 轴镜像
  canvasCenterY(1),

  /// 画板中心 x 轴镜像
  canvasCenterX(2);

  final int value;

  const ElementMirrorType(this.value);

  static ElementMirrorType? byValue(int value) {
    return ElementMirrorType.values.firstWhereOrNull((e) => e.value == value);
  }
}

/// YYYY 四位数年份 2020
/// MM 两位数月份 01-12
/// MMM 月份名称缩写 Jan-Dec
/// MMMM 完整的月份名称 January-December
/// DD 两位数日期 01-31
enum ElementDateFormat {
  ///中文连接
  ///  YYYY年MM月DD日
  YMD_C('YYYY年MM月DD日', 'yyyy年MM月dd日'),

  /// YYYY年MM月
  YM_C('YYYY年MM月', 'yyyy年MM月'),

  /// MM月DD日
  MD_C('MM月DD日', 'MM月dd日'),

  ///中文连接
  ///  YYYY年MM月DD日 EEEE
  YMDE_C('YYYY年MM月DD日 EEEE', 'yyyy年MM月dd日 EEEE'),

  /// 无连接
  /// yyyyMMdd
  YMD('YYYYMMDD', 'yyyyMMdd'),

  ///中划线连接
  /// YYYY-MM-DD
  YMD_LC('YYYY-MM-DD', 'yyyy-MM-dd'),

  /// MM-DD-YYYY
  MDY_LC('MM-DD-YYYY', 'MM-dd-yyyy'),

  /// DD-MM-YYYY
  DMY_LC('DD-MM-YYYY', 'dd-MM-yyyy'),

  /// YYYY-MM
  YM_LC('YYYY-MM', 'yyyy-MM'),

  /// MM-DD
  MD_LC('MM-DD', 'MM-dd'),

  /// 斜线连接
  /// YYYY/MM/DD
  YMD_S('YYYY/MM/DD', 'yyyy/MM/dd'),

  /// MM/DD/YYYY
  MDY_S('MM/DD/YYYY', 'MM/dd/yyyy'),


  /// YYYY/MM/DD
  YM_S('YYYY/MM', 'yyyy/MM'),

  /// /MM/DD
  MD_S('MM/DD', 'MM/dd'),

  /// DD/MM/YYYY
  DMY_S('DD/MM/YYYY', 'dd/MM/yyyy'),

  /// MM-DD-YYYY EEEE
  MDYE_S('MM/DD/YYYY EEEE', 'MM/dd/yyyy EEEE'),

  ///空格连接
  /// D MMM,YYYY
  DMY_Sp('D MMM,YYYY', 'd MMM,yyyy'),

  /// D MMMM,YYYY
  DFMY_Sp('D MMMM,YYYY', 'd MMMM,yyyy'),

  /// MMM D,YYYY
  MDY_Sp('MMM D,YYYY', 'MMM d,yyyy'),

  /// MMMM D,YYYY
  FMDY_Sp('MMMM D,YYYY', 'MMMM d,yyyy'),

  /// DD.MM.YYYY
  YMD_DOT('YYYY.MM.DD', 'yyyy.MM.dd'),

  /// DD.MM.YYYY
  DMY_DOT('DD.MM.YYYY', 'dd.MM.yyyy'),

  /// YYYY      MM      DD
  YDM_Sp('YYYY      MM      DD', 'yyyy      MM      dd');

  final String label;
  final String value;

  const ElementDateFormat(this.label, this.value);

  static ElementDateFormat? byValue(String value) {
    return ElementDateFormat.values.firstWhereOrNull((e) => e.value == value);
  }

  static ElementDateFormat? byLabel(String label) {
    return ElementDateFormat.values.firstWhereOrNull((e) => e.label == label);
  }
}

/// HH 小时 24小时制 00-23
/// mm 分钟  00-59
/// ss 秒 00-59
enum ElementTimeFormat {
  /// HH:mm:ss
  HMS('HH:mm:ss'),

  /// HH:mm
  HM('HH:mm'),

  /// mm:ss
  MS('mm:ss'),

  /// HH
  H('HH'),

  /// HH时mm分ss秒 (简中、繁中、韩语、日语)
  HMS_C('HH时mm分ss秒'),

  /// HH时mm分 (简中、繁中、韩语、日语)
  HM_C('HH时mm分'),

  /// HH时 (简中、繁中、韩语、日语)
  H_C('HH时');

  final String value;

  const ElementTimeFormat(this.value);

  static ElementTimeFormat? byValue(String value) {
    return ElementTimeFormat.values.firstWhereOrNull((e) => e.value == value);
  }
}

/// 12小时制时上午/下午
enum ElementTimeUnit {
  afternoon,
  morning;

  static ElementTimeUnit? byValue(String? value) {
    return ElementTimeUnit.values.firstWhereOrNull((e) => e.name == value);
  }
}

/// 关联时间单位
enum ElementDateAssociatedUnit {
  hour('Associated_Hour'),
  day('Associated_Day'),
  month('Associated_month'),
  year('Associated_Year');

  final String value;

  const ElementDateAssociatedUnit(this.value);

  static ElementDateAssociatedUnit? byValue(String? value) {
    return ElementDateAssociatedUnit.values.firstWhereOrNull(
      (e) => e.value == value,
    );
  }
}

/// 文本排版模式
enum TextTypesettingMode {
  /// 水平
  Horizontal,

  /// 水平90°
  Horizontal_90,

  /// 垂直排版
  Vertical,

  /// 弧形
  Arc,
}

/// 素材元素类型
enum MaterialElementType {
  /// 图标
  icon(1),

  /// 边框
  border(2);

  final int value;

  const MaterialElementType(this.value);

  static MaterialElementType? byValue(int value) {
    return MaterialElementType.values.firstWhereOrNull((e) => e.value == value);
  }
}

enum SerialElementFormat {
  ///大写字母
  capitalLetter,

  ///小写字母
  smallLetter,

  ///数字重复，总长度固定
  digitRepeat,

  ///大写字母重复，总长度固定
  capitalLetterRepeat,

  ///小写字母重复，总长度固定
  smallLetterRepeat;

  static SerialElementFormat? byName(String? name) {
    if (name == null || name.isEmpty) {
      return null;
    }
    return SerialElementFormat.values.firstWhereOrNull((e) => e.name == name);
  }
}

// 段落超长应用枚举
enum ParagraphOverflowEnum {
  // 应用当前
  current,
  // 应用所有
  all;

  static ParagraphOverflowEnum? byName(String? name) {
    if (name == null || name.isEmpty) {
      return null;
    }
    return ParagraphOverflowEnum.values.firstWhereOrNull((e) => e.name == name);
  }
}
