import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/netal_color_element.dart';

import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

/// 颜色元素
@immutable
abstract class ColorElement extends BaseElement implements NetalColorElement {
  @override
  final int colorChannel;

  @override
  final Color elementColor;

  ///设置元素颜色索引
  final int paperColorIndex;

  ColorElement({
    super.id,
    super.x,
    super.y,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    required super.width,
    required super.height,
    required super.type,
    Color? elementColor,
    int? colorChannel,
  })  : elementColor = elementColor ?? const Color(0xFF000000),
        colorChannel = colorChannel ?? 0,
        paperColorIndex = colorChannel ?? 0;

  @override
  Map<String, dynamic> toJson() {
    return ({
      ...super.toJson(),
      "elementColor": [
        elementColor.alpha,
        elementColor.red,
        elementColor.green,
        elementColor.blue
      ],
      'colorChannel': colorChannel,
      'paperColorIndex': paperColorIndex,
    }..removeWhere((k, v) => v == null));
  }

  @override
  ColorElement.fromJson(super.json)
      : elementColor =
            TemplateParseUtils.parseColorFromJSON(json['elementColor']) ??
                const Color(0xFF000000),
        colorChannel =
            TemplateParseUtils.parseNumberFromJSON(json['colorChannel'])
                    ?.toInt() ??
                0,
        paperColorIndex =
            TemplateParseUtils.parseNumberFromJSON(json['paperColorIndex'])
                    ?.toInt() ??
                0,
        super.fromJson();

  @override
  ColorElement copyWith({
    num? x,
    num? y,
    int? rotate,
    num? width,
    num? height,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
    int? colorChannel,
    Color? elementColor,
  });

  @override
  List<Object?> get props =>
      [...super.props, colorChannel, elementColor, paperColorIndex];
}
