import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/models/netal_table_cell_element.dart';

import 'package:niimbot_template/models/elements/table_combine_cell_element.dart';
import 'package:niimbot_template/utils/string_utils.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

/// 表格单元格
@immutable
class TableCellElement extends TableCombineCellElement
    implements NetalTableCellElement {
  @override
  final int columnIndex;

  @override
  final String? combineId;

  @override
  final int rowIndex;

  TableCellElement({
    super.textAlignHorizontal,
    super.textAlignVertical,
    super.wordSpacing,
    super.letterSpacing,
    super.lineSpacing,
    super.fontCode,
    super.fontFamily,
    super.hasVipRes,
    super.fontStyle,
    super.fontSize,
    super.lineBreakMode,
    // super.typesettingMode,
    // super.typesettingParam,
    // super.colorReverse,
    // super.write_mode,
    super.id,
    super.value,
    super.dataBind,
    super.isBinding = false,
    super.fieldName = '',
    super.contentTitle,
    super.isTitle,
    this.combineId,
    this.rowIndex = 0,
    this.columnIndex = 0,
  });

  TableCellElement.fromJson(super.json)
    : combineId = json['combineId'],
      columnIndex =
          TemplateParseUtils.parseNumberFromJSON(
            json['columnIndex'],
          )?.toInt() ??
          0,
      rowIndex =
          TemplateParseUtils.parseNumberFromJSON(json['rowIndex'])?.toInt() ??
          0,
      super.fromJson();

  @override
  Map<String, dynamic> toJson() {
    return ({
      ...super.toJson(),
      'dataBind': dataBind,
      'fieldName': fieldName,
      'combineId': combineId ?? '',
      'columnIndex': columnIndex,
      'rowIndex': rowIndex,
    }..removeWhere((k, v) => v == null));
  }

  @override
  /// TODO 需要迁移至values数据结构
  NetalTableCellElement toNetal() {
    return NetalTableCellElement(
      id: id,
      value: StringUtils.covertNewLineChar(value),
      textAlignVertical: textAlignVertical,
      textAlignHorizontal: textAlignHorizontal,
      wordSpacing: wordSpacing,
      letterSpacing: letterSpacing,
      lineSpacing: lineSpacing,
      // 上层使用fontCode标记字体 ZT001表示默认字体即为Harmony
      fontFamily: fontCode == 'ZT001' ? 'Harmony' : fontCode,
      fontStyle: fontStyle,
      fontSize: fontSize,
      lineBreakMode: lineBreakMode,
      typesettingMode: typesettingMode,
      typesettingParam: typesettingParam,
      colorReverse: colorReverse,
      write_mode: write_mode,
      combineId: combineId,
      rowIndex: rowIndex,
      columnIndex: columnIndex,
    );
  }

  @override
  TableCellElement copyWith({
    NetalTextAlign? textAlignHorizontal,
    NetalTextAlign? textAlignVertical,
    num? wordSpacing,
    num? letterSpacing,
    num? lineSpacing,
    String? fontFamily,
    String? fontCode,
    bool? hasVipRes,
    num? fontSize,
    NetalTextLineBreakMode? lineBreakMode,
    List<NetalTextFontStyle>? fontStyle,
    // List<int>? typesettingParam,
    // bool? colorReverse,
    // NetalTextWriteMode? write_mode,
    String? id,
    String? value,
    CopyWrapper<String?>? combineId,
    int? rowIndex,
    int? columnIndex,
    CopyWrapper<List<String>?>? dataBind,
    bool? isTitle,
    String? contentTitle,
    String? fieldName,
    bool? isBinding,
  }) {
    return TableCellElement(
      textAlignHorizontal: textAlignHorizontal ?? this.textAlignHorizontal,
      textAlignVertical: textAlignVertical ?? this.textAlignVertical,
      wordSpacing: wordSpacing ?? this.wordSpacing,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      lineSpacing: lineSpacing ?? this.lineSpacing,
      fontFamily: fontFamily ?? this.fontFamily,
      fontCode: fontCode ?? this.fontCode,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      fontSize: fontSize ?? this.fontSize,
      lineBreakMode: lineBreakMode ?? this.lineBreakMode,
      fontStyle: fontStyle ?? this.fontStyle,
      // typesettingParam: typesettingParam ?? this.typesettingParam,
      // colorReverse: colorReverse ?? this.colorReverse,
      // write_mode: write_mode ?? this.write_mode,
      id: id ?? this.id,
      value: value ?? this.value,
      combineId: combineId != null ? combineId.value : this.combineId,
      rowIndex: rowIndex ?? this.rowIndex,
      columnIndex: columnIndex ?? this.columnIndex,
      dataBind: dataBind != null ? dataBind.value : this.dataBind,
    );
  }

  @override
  List<Object?> get props => [...super.props, columnIndex, combineId, rowIndex];
}
