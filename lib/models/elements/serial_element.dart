import 'dart:math';

import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';

import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/utils/element_utils.dart';
import 'package:niimbot_template/utils/string_utils.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';

/// 序列号元素
@immutable
class SerialElement extends TextElement {
  final String? prefix;
  final String startNumber;
  final String? suffix;
  final int incrementValue;
  final int fixLength;
  final String fixValue;

  final int index;

  SerialElement({
    super.id,
    super.x,
    super.y,
    super.width = 6,
    super.height = 4,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.typesettingMode,
    super.typesettingParam,
    super.textAlignHorizontal,
    super.textAlignVertical,
    super.lineBreakMode,
    super.wordSpacing,
    super.letterSpacing,
    super.lineSpacing,
    super.fontFamily,
    super.fontCode,
    super.fontStyle,
    super.fontSize,
    super.fontColor,
    super.isTitle,
    super.colorReverse,
    super.colorChannel,
    super.elementColor,
    super.hasVipRes,
    super.write_mode,
    super.boxStyle,
    super.textStyle,
    super.isEditing,
    super.lineMode,
    super.wordsRotate,
    this.prefix,
    String? startNumber,
    this.suffix,
    int? incrementValue,
    int? fixLength,
    String? fixValue,
    int? index,
  }) : startNumber = _initStartNumber(startNumber, fixLength, fixValue),
       fixLength = _initFixLength(fixLength, startNumber),
       fixValue = fixValue ?? '0',
       incrementValue = incrementValue ?? 1,
       index = index ?? 1,
       super(type: NetalElementType.serial, value: '');

  SerialElement.fromJson(super.json)
    : prefix = json['prefix'],
      suffix = json['suffix'],
      incrementValue =
          TemplateParseUtils.parseNumberFromJSON(
            json['incrementValue'],
          )?.toInt() ??
          1,
      fixLength = _buildFixLength(json['fixLength'], json['startNumber']),
      fixValue = json['fixValue'] ?? '0',
      startNumber = _buildStartNumber(json),
      index = json['index'] ?? 1,
      super.fromJson();

  @override
  Map<String, dynamic> toJson() {
    final data = ({
      ...super.toJson(),
      'prefix': prefix,
      'startNumber': startNumber,
      'suffix': suffix,
      'incrementValue': incrementValue,
      'fixLength': fixLength,
      'fixValue': fixValue,
      'value': value,
      'index': index,
    }..removeWhere((k, v) => v == null));
    ElementUtils.completionFontDefault(data);
    return data;
  }

  @override
  SerialElement copyWith({
    num? x,
    num? y,
    int? rotate,
    num? width,
    num? height,
    num? lastWidth,
    num? lastHeight,
    String? id,
    bool? isLock,
    bool? isOpenMirror,
    ElementMirrorType? mirrorType,
    bool? hasVipRes,
    bool? colorReverse,
    NetalTextWriteMode? write_mode,
    NetalTypesettingMode? typesettingMode,
    List<int>? typesettingParam,
    NetalTextAlign? textAlignHorizontal,
    NetalTextAlign? textAlignVertical,
    num? wordSpacing,
    num? letterSpacing,
    num? lineSpacing,
    String? fontFamily,
    String? fontCode,
    List<NetalTextFontStyle>? fontStyle,
    num? fontSize,
    List<int>? fontColor,
    bool? isTitle,
    CopyWrapper<String?>? contentTitle,
    NetalTextLineBreakMode? lineBreakMode,
    String? value,
    CopyWrapper<List<String>?>? dataBind,
    CopyWrapper<String?>? fieldName,
    bool? isBinding,
    int? tagForRelocation,
    // int? paperColorIndex,
    int? colorChannel,
    Color? elementColor,
    NetalTextBoxStyle? boxStyle,
    List<NetalTextStyle>? textStyle,
    CopyWrapper<String?>? prefix,
    CopyWrapper<String?>? suffix,
    String? startNumber,
    int? incrementValue,
    int? fixLength,
    String? fixValue,
    int? index,
    CopyWrapper<String?>? val,
    CopyWrapper<NetalTextLineMode?>? lineMode,
    int? wordsRotate,
  }) {
    return SerialElement(
      x: x ?? this.x,
      y: y ?? this.y,
      rotate: rotate ?? this.rotate,
      width: width ?? this.width,
      height: height ?? this.height,
      id: id ?? this.id,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      colorReverse: colorReverse ?? this.colorReverse,
      fontCode: fontCode ?? this.fontCode,
      fontFamily: fontFamily ?? this.fontFamily,
      fontSize: fontSize ?? this.fontSize,
      fontStyle: fontStyle ?? this.fontStyle,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      lineBreakMode: lineBreakMode ?? this.lineBreakMode,
      lineSpacing: lineSpacing ?? this.lineSpacing,
      textAlignHorizontal: textAlignHorizontal ?? this.textAlignHorizontal,
      textAlignVertical: textAlignVertical ?? this.textAlignVertical,
      typesettingMode: typesettingMode ?? this.typesettingMode,
      typesettingParam: typesettingParam ?? this.typesettingParam,
      wordSpacing: wordSpacing ?? this.wordSpacing,
      write_mode: write_mode ?? this.write_mode,
      prefix: prefix != null ? prefix.value : this.prefix,
      startNumber: startNumber ?? this.startNumber,
      suffix: suffix != null ? suffix.value : this.suffix,
      incrementValue: incrementValue ?? this.incrementValue,
      fixLength: fixLength ?? this.fixLength,
      fixValue: fixValue ?? this.fixValue,
      index: index ?? this.index,
      boxStyle: boxStyle ?? this.boxStyle,
      textStyle: textStyle ?? this.textStyle,
      lineMode: lineMode != null ? lineMode.value : this.lineMode,
      wordsRotate: wordsRotate ?? this.wordsRotate,
    );
  }

  @override
  List<Object?> get props => [
    ...super.props,
    prefix,
    startNumber,
    suffix,
    incrementValue,
    fixLength,
    fixValue,
    index,
  ];

  /// 根据[index]索引构建值
  String buildValue(int index) => ElementUtils.buildSerialValue(
    prefix: prefix,
    suffix: suffix,
    startNumber: startNumber,
    increment: incrementValue,
    index: index,
    fixLength: fixLength,
    fixValue: fixValue,
  );
}

int _maxFixLength(int val) {
  if (val > 8) {
    return 8;
  }
  return val;
}

String _buildStartNumber(dynamic json) {
  dynamic startNumber = json['startNumber'];
  dynamic fixLength = json['fixLength'];
  dynamic fixValue = json['fixValue'];
  String serialNumber = json['serialNumber'] ?? '';
  String prefix = json['prefix'] ?? '';
  String suffix = json['suffix'] ?? '';
  String value = json['value'] ?? '';
  if (serialNumber.isNotEmpty &&
      '$prefix$serialNumber$suffix'.length == value.length) {
    startNumber = serialNumber;
  }

  final _fixLength = _maxFixLength(
    TemplateParseUtils.parseNumberFromJSON(fixLength)?.toInt() ?? 2,
  );
  final _fixValue = fixValue ?? '0';
  return StringUtils.padStart(
    (TemplateParseUtils.parseStringFromJSON(startNumber) ?? '01'),
    _fixLength,
    _fixValue,
  );
}

String _initStartNumber(String? startNumber, int? fixLength, String? fixValue) {
  final _fixLength = _maxFixLength(fixLength ?? 2);
  final _fixValue = fixValue ?? '0';
  return StringUtils.padStart((startNumber ?? '01'), _fixLength, _fixValue);
}

int _buildFixLength(dynamic fixLength, dynamic startNumber) {
  final _startNumber =
      TemplateParseUtils.parseStringFromJSON(startNumber) ?? '01';
  final _fixLength =
      TemplateParseUtils.parseNumberFromJSON(fixLength)?.toInt() ??
      _startNumber.length;
  return _maxFixLength(max(_fixLength, _startNumber.length));
}

int _initFixLength(int? fixLength, String? startNumber) {
  final _startNumber = startNumber ?? '01';
  final _fixLength = fixLength ?? _startNumber.length;
  return _maxFixLength(max(_fixLength, _startNumber.length));
}
