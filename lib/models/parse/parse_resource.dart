import 'package:niimbot_template/models/parse/enum.dart';

/// 解析资源
class ParseResource {
  final ParseResourceType type;

  ParseResource({required this.type});
}

class ParseFontResource extends ParseResource {
  final String fontCode;
  final String fontFamily;
  final bool? hasVipRes;

  ParseFontResource(
      {required this.fontCode, required this.fontFamily, this.hasVipRes})
      : super(type: ParseResourceType.font);
}
