import 'package:niimbot_template/models/parse/enum.dart';
import 'package:niimbot_template/models/parse/parse_resource.dart';

class ParseDataSourceResource extends ParseResource {
  final String hash;

  ParseDataSourceResource({required this.hash})
      : super(type: ParseResourceType.dataSource);
}

class ParsedFontResource extends ParseResource {
  final String hash;

  ParsedFontResource({required this.hash})
      : super(type: ParseResourceType.dataSource);
}
