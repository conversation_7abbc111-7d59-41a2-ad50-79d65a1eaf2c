import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:image/image.dart' as img;
import 'package:intl/intl.dart';
import 'package:isolate/isolate.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_element_base.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/models/netal_text_element.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/serial_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/template/constants.dart';
import 'package:niimbot_template/models/template/file_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_info.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/models/values/element_value.dart';
import 'package:niimbot_template/utils/element_utils.dart';
import 'package:niimbot_template/utils/file_utils.dart';
import 'package:niimbot_template/utils/string_utils.dart';
import 'package:niimbot_template/utils/template_data_source_utils.dart';
import 'package:path/path.dart';

import 'models/elements/element_enum.dart';
import 'models/template/template_enum.dart';
import 'models/template/template_profile.dart';

final Logger _logger = Logger("TemplateGenerate", on: kDebugMode);

class TemplateGenerate {
  static LoadBalancer? _lb;

  static Future<R> _run<R, P>(
    FutureOr<R> Function(P argument) function,
    P argument, {
    Duration? timeout,
    FutureOr<R> Function()? onTimeout,
    int load = 100,
  }) async {
    _lb ??= await LoadBalancer.create(4, IsolateRunner.spawn);
    return await _lb!.run(
      function,
      argument,
      load: load,
      timeout: timeout,
      onTimeout: onTimeout,
    );
  }

  /// 生成预览数据
  /// [page] 第几页
  /// [List<Color?>?] 耗材颜色
  /// [backgroundImage] 背景图 Base64编码
  /// [localBackgroundImageUrl] 背景图片 本地路径
  /// [position] 0：左黑右红，1-上黑下红，2-右黑左红，3-下黑上红
  static Future<NetalImageResult> generatePreviewImageAsync(
    TemplateData template, {
    int page = 1,
    List<Color?>? colorList,
    String? backgroundImage,
    String? localBackgroundImageUrl,
    Offset? offset,
  }) async {
    int orientation = 360 - template.rotate.toInt();

    if (colorList != null && colorList.length == 2) {
      ///出纸方向 向上-0 向右-90 向下-180 向左-270

      int position = 0;

      switch (orientation) {
        case 0:
        case 360:
          position = 0;
        case 90:
          position = 1;
        case 180:
          position = 2;
        case 270:
          position = 3;
      }

      // 黑红碳带
      return await NetalPlugin().generateBlackRedPreviewAsync(
          size: template.size,
          elements: template.covertElementsToNetal(page),
          color1: colorList[0] ?? Color(0xff000000),
          color2: colorList[1] ?? Color(0xffe60012),
          backgroundImage: backgroundImage,
          localBackgroundImageUrl: localBackgroundImageUrl,
          //使用固定缩放倍率
          ratio: 8 * 3,
          offset: offset,
          orientation: orientation,
          position: position);
    }

    return await NetalPlugin().generatePreviewAsync(
      size: template.size,
      elements: template.covertElementsToNetal(page),
      backgroundImage: backgroundImage,
      localBackgroundImageUrl: localBackgroundImageUrl,
      //使用固定缩放倍率
      ratio: 8 * 3,
      color: colorList?.first,
      offset: offset,
      orientation: orientation,
    );
  }

  /// 生成预览数据
  /// [page] 第几页
  /// [color] 耗材颜色
  /// [backgroundImage] 背景图 Base64编码
  /// [localBackgroundImageUrl] 背景图片 本地路径
  static Future<NetalImageResult> generateTubePreviewImageAsync(
    NetalTextElement element, {
    int page = 1,
    Color? color,
    String? backgroundImage,
    String? localBackgroundImageUrl,
    Offset? offset,
  }) async {
    return await NetalPlugin().generateTextElementAsync(
      size: Size.zero,
      ratio: DisplayUtil.pxRatio,
      color: color,
      element: element,
    );
  }

  /// 生成预览数据
  /// [page] 第几页
  /// [color] 耗材颜色
  /// [backgroundImage] 背景图 Base64编码
  /// [localBackgroundImageUrl] 背景图片 本地路径
  @Deprecated('请使用generatePreviewImageAsync')
  static NetalImageResult generatePreviewImage(
    TemplateData template, {
    int page = 1,
    Color? color,
    String? backgroundImage,
    String? localBackgroundImageUrl,
    Offset? offset,
    num? rotate,
  }) {
    return NetalPlugin().generatePreview(
      size: template.size,
      elements: template.covertElementsToNetal(page),
      // backgroundImage: backgroundImage != null && rotate != null
      //     ? rotateBase64Image(backgroundImage, rotate)
      //     : backgroundImage,
      localBackgroundImageUrl: localBackgroundImageUrl,
      ratio: 8 * 3,
      color: color,
      offset: offset,
      orientation: template.rotate.toInt(),
    );
  }

  /// 生成打印数据
  /// [page] 第几页
  /// [ratio] 打印倍率
  /// [color] 耗材颜色
  static NetalImageResult generatePrintData(
    TemplateData template, {
    int page = 0,
    double ratio = 8,
    Color? color,
    Offset? offset,
  }) {
    /// 其它处理过程
    return NetalPlugin().generatePrint(
      size: template.size,
      elements: template.covertElementsToNetal(page),
      cableDirection: template.cableDirection ?? NetalCableDirection.top,
      cableLength: template.isCable ? template.cableLength.toDouble() : 0,
      ratio: ratio,
      color: color,
      offset: offset,
      orientation: template.rotate.toInt(), // 由于业务层定义的旋转角度是逆时针，所以需要取反
    );
  }

  /// 旋转背景图
  static Future<String> rotateBase64Image(
    String base64String,
    num angle,
  ) async {
    return _run((o) {
      Uint8List imageBytes = base64Decode(base64String);
      img.Image decodedImage = img.decodeImage(imageBytes)!;
      img.Image rotatedImage = img.copyRotate(decodedImage, angle: angle);
      Uint8List encodedBytes = img.encodePng(rotatedImage); // 或者使用encodePng等
      return base64Encode(encodedBytes);
    }, 0);
  }

  static Future<String> _uploadBackgroundImage(
    String backgroundImage,
    List<String> localBackground,
    Future<String?> Function(Uint8List data, {String? fileName}) uploadOss,
  ) async {
    final List<String> list = [];
    if (backgroundImage.isNotEmpty) {
      /* 背景图片不为空 解析背景图片列表 */
      final backgrounds = backgroundImage.split(',');
      for (final background in backgrounds) {
        if (background.isNotEmpty) {
          try {
            final imageBytes = base64Decode(background);
            // 地址为Base64 上传到OSS
            final url = await uploadOss(imageBytes);
            if (url != null) {
              list.add(url);
            }
          } catch (e) {
            /* 地址为URL */
            list.add(background);
          }
        }
      }
    } else {
      /* 背景图片为空 则使用本地图片 */
      for (final local in localBackground) {
        File localImage = File(local);
        if (localImage.existsSync()) {
          /* 本地路径存在时 */
          final bytes = localImage.readAsBytesSync();
          final url = await uploadOss(bytes);
          if (url != null) {
            list.add(url);
          }
        }
      }
    }
    return list.join(',');
  }

  /// 线号机文件打印和预览的size处理
  static Size generateTemplateSize(
      {required Size size,
      required TextElement element,
      required List<num> margin,
      ElementValue? value,
      bool autoWidth = false,
      bool preview = true,
      TemplateData? template}) {
    bool isOverflowTypeCurrent = value != null &&
        value is CompositeValue &&
        value.overflowType == ParagraphOverflowEnum.current;
    final textIsVer = element.isTextVertical();
    Size viewSize =
        size + Offset(0, preview ? (margin[0] + margin[2]).toDouble() : 0);
    if (autoWidth || isOverflowTypeCurrent) {
      var tempSize = size;
      final netalElement = template?.coverElementToNetal(element);
      final netalRes = buildNetalResult((netalElement as NetalTextElement));
      tempSize = Size((netalRes?.width ?? 0).px2mm(false).toDouble(),
          (netalRes?.height ?? 0).px2mm(false).toDouble());
      final viewWidth = !textIsVer ? tempSize.width : tempSize.height;
      viewSize = Size(
        (viewWidth + margin[1] + margin[3]).toDouble(),
        viewSize.height,
      );
    }
    return viewSize;
  }

  /// 线号机文件打印和预览的元素处理
  static TextElement generateTubeElement({
    required Size size,
    required TextElement element,
    required List<num> margin,
    required bool textAlignCenter,
    bool preview = true,
  }) {
    final textIsVer = element.isTextVertical();
    const num height = 100;
    num y = textIsVer
        ? -(height - element.height) / 2 + element.y + (preview ? margin[0] : 0)
        : element.y + (preview ? margin[0] : 0);
    num elementHeight = textIsVer ? height : element.height;
    NetalTextAlign textAlignVertical =
        textIsVer ? NetalTextAlign.center : element.textAlignVertical;
    num x = element.x;
    if (textIsVer) {
      final verHeight =
          size.width < element.height ? element.height : size.width;
      x = textAlignCenter
          ? (verHeight - element.width) / 2
          : (verHeight - element.width) / 2 -
              (verHeight - element.height) / 2 +
              margin[3];
    }
    return element.copyWith(
      x: x,
      y: y,
      height: elementHeight,
      textAlignVertical: textAlignVertical,
    );
  }

  // 图像库渲染元素
  static NetalImageResult getNetalResult(NetalTextElement text,
      {num? verticalHeight}) {
    final textIsHor = text.typesettingMode == NetalTypesettingMode.horizontal;
    final elementSize = Size(text.width.toDouble(), text.height.toDouble());
    final paragraphLength = textIsHor ? text.width : text.height;
    final max = 100;

    Size sizeResult = elementSize;
    final size = textIsHor
        ? sizeResult
        : Size(
            sizeResult.height,
            verticalHeight?.toDouble() ?? sizeResult.width,
          );
    return NetalPlugin().generateTextElement(
      element: text,
      ratio: RatioUtils().ratio,
      size: size,
    );
  }

  static NetalImageResult? buildNetalResult(final NetalTextElement element) {
    final textIsHor =
        element.typesettingMode == NetalTypesettingMode.horizontal;
    final lineMode = (textIsHor
        ? NetalTextLineMode.widthFit
        : NetalTextLineMode.verticalFit);
    final max = 100;
    final text = element.copyWith(
      width: textIsHor ? element.width : max,
      height: textIsHor
          ? element.height
          : (max > element.height ? max : element.height),
      textAlignHorizontal: element.textAlignHorizontal,
      textAlignVertical: element.textAlignVertical,
      lineMode: CopyWrapper.value(lineMode),
      value: element.value,
    );
    return getNetalResult(text);
  }

  /// 线号机预览图的数据生成
  static TemplateData generateTubeTemplateData(
    TemplateData template, {
    int page = 1,
    bool preview = true,
  }) {
    if (template.isTubeFile) {
      final element = template.elements[page - 1] as TextElement;
      final margin = template.margin;
      final value = template.values?.firstWhereOrNull(
        (final e) => e.elementId == element.id,
      );
      final size = generateTemplateSize(
          size: template.size,
          element: element,
          margin: margin,
          value: value,
          autoWidth: template.tubeFileSetting?.autoWidth == true,
          preview: preview,
          template: template);

      final newElement = generateTubeElement(
        size: size,
        element: element,
        margin: margin,
        textAlignCenter: template.tubeFileSetting?.align == 'center',
        preview: preview,
      );
      return template.copyWith(
        width: size.width,
        height: size.height,
        elements: [newElement],
        dataSources: [],
      );
    } else {
      return template;
    }
  }

  static Future<String> _syncImageList(
    String val,
    Future<String?> Function(Uint8List data, {String? fileName}) uploadOss,
  ) async {
    File localImage = File(val);
    if (localImage.existsSync()) {
      /* 本地路径存在时 */
      final bytes = localImage.readAsBytesSync();
      final uploadOssUrl = await uploadOss(bytes);
      return (uploadOssUrl?.isNotEmpty ?? false) ? uploadOssUrl! : val;
    }
    return val;
  }

  /// 生成打印JSON
  /// [page] 第几页
  static Future<Map<String, dynamic>> generatePrintJSON(
    TemplateData template, {
    int page = 1,
    bool create = true,
    bool rebuildTime = true,
    bool rebuildThumbnail = true,
    bool? rebuildValue,
    List<Color?>? colorList,
    required Future<String?> Function(Uint8List data, {String? fileName})
        uploadOss,
    required Future<String?> Function(Uint8List data) saveToLocal,
    required Future<TemplateDataSource> Function(TemplateDataSource dataSources)
        dataSourcesFormatter,
  }) async {
    var templateVersion = TemplateConstants.SAVE_TEMPLATE_VERSION;
    final List<Map<String, dynamic>> elements = [];
    final List<TemplateDataSource> dataSourcesFormat = [];

    /// 数据源转化
    for (var dataSource in (template.dataSources ?? [])) {
      dataSourcesFormat.add(await dataSourcesFormatter(dataSource));
    }

    /// 元素转化
    for (var element in template.elements) {
      BaseElement elementFormat = element.copyWith();
      if (element is ImageElement) {
        String? imageUrl = element.imageUrl;
        if (!(imageUrl?.isNotEmpty ?? false) ||
            !(StringUtils.isNetUrl(imageUrl ?? ''))) {
          Uint8List? bytes;
          /* 图片远程路径为空时 或者不是网络路径 通过本地路径 为其生成远程路径 */
          if (element.localImageUrl.isNotEmpty) {
            File localImage = File(element.localImageUrl);
            if (localImage.existsSync()) {
              /* 本地路径存在时 */
              bytes = localImage.readAsBytesSync();
            }
          }
          if (bytes == null) {
            /* 通过本地路径解析后 */
            /* 图片远程路径为空时 通过imageData为其生成远程路径 */
            if (element.imageData.isNotEmpty) {
              /* */
              try {
                bytes = base64Decode(element.imageData);
              } catch (e) {
                /* 解析失败 */
              }
            }
          }
          if (bytes != null) {
            final uploadOssUrl = await uploadOss(bytes);
            if (uploadOssUrl?.isNotEmpty ?? false) {
              /* 生成远程路径 成功时清除imageData */
              elementFormat = element.copyWith(
                imageUrl: CopyWrapper.value(uploadOssUrl),
                imageData: '',
              );
            } else {
              final localPath = element.localImageUrl.isNotEmpty
                  ? element.localImageUrl
                  : await saveToLocal(bytes);
              elementFormat = element.copyWith(
                localImageUrl: localPath,
                imageData: '',
              );
            }
          }
        }
      }
      if (element is DateElement) {
        templateVersion = '1.7.0.1';
      }
      BaseElement elem = rebuildValue == true
          ? rebuildElementValue(
              elementFormat,
              dataSources: dataSourcesFormat,
              modify: template.dataSourceModifies,
              bindInfo: template.dataSourceBindInfo,
              page: page,
            )
          : elementFormat;
      if (rebuildValue == true && elem is BindElement) {
        elem = elem.copyWith(
          dataBind: const CopyWrapper.value(null),
          isBinding: false,
        );
      }
      if (elem.isOpenMirror) {
        /* 镜像元素 */
        final id = ElementUtils.generateId();
        final mirrorElement = ElementUtils.buildMirrorElement(
          element: elem,
          templateSize: template.size,
        ).copyWith(id: id, isOpenMirror: false);
        final mirror = mirrorElement.toJson();
        mirror['mirrorId'] = elem.id;
        elements.add(mirror);
        final curr = elem.toJson();
        curr['mirrorId'] = id;
        elements.add(curr);
      } else {
        elements.add(elem.toJson());
      }
    }
    if (template.dataSources != null && template.dataSources!.isNotEmpty) {
      final TemplateDataSource tempDataSourceFirst =
          template.dataSources!.first;

      ///移动端1.7.0.3的需单独处理
      if (tempDataSourceFirst.type == TemplateDataSourceType.excel &&
          (tempDataSourceFirst.name ?? '').endsWith('.xls')) {
        templateVersion = '1.7.0.3';
      }
    }
    final List<FileDataSource> fileDataSources = [];
    if (template.fileDataSources != null &&
        template.fileDataSources!.isNotEmpty) {
      templateVersion = '1.7.0.4';
      // Uint8List? bytes;
      for (var item in template.fileDataSources!) {
        if (item.imageDir != null && StringUtils.isNetUrl(item.imageDir!)) {
          fileDataSources.add(item);
        } else {
          List<String> pageImages = await Future.wait(item.pageImages
              .map((final e) async => await _syncImageList(e, uploadOss)));
          final data = FileUtils.extractUrls(pageImages);
          if (data != null && StringUtils.isNetUrl(data.$1)) {
            fileDataSources.add(item.copyWith(
                pageImages: data.$2, imageDir: CopyWrapper.value(data.$1)));
          } else {
            fileDataSources.add(item);
          }
        }
      }
    }
    final dateTime = DateTime.now();
    TemplateProfile profile = template.profile;
    final id =
        !create ? template.id : dateTime.millisecondsSinceEpoch.toString();

    /// 预览图转化，需外部判断图片是否存在
    String? localBackImageUrlRotate;
    if (template.localBackImageUrl.isNotEmpty) {
      final ext = extension(template.localBackImageUrl);
      final suffix = ext.isEmpty ? '' : '.$ext';
      localBackImageUrlRotate = template.canvasRotate == 0
          ? template.localBackImageUrl
          : '${template.localBackImageUrl.replaceAll(suffix, '')}-${template.canvasRotate}$suffix';
    }
    String? thumbnail = template.thumbnail;
    if (rebuildThumbnail) {
      TemplateData tempData = !template.isTubeFile
          ? template.copyWith()
          : TemplateGenerate.generateTubeTemplateData(template, page: page);
      final tmpImage = await TemplateGenerate.generatePreviewImageAsync(
        tempData,
        colorList: colorList,
        page: !template.isTubeFile ? page : 1,
        localBackgroundImageUrl: localBackImageUrlRotate,
      );
      final thumbnailUrl = await uploadOss(tmpImage.pixels);
      if (thumbnailUrl?.isNotEmpty ?? false) {
        thumbnail = thumbnailUrl;
      } else {
        final localPath = await saveToLocal(tmpImage.pixels);
        thumbnail = localPath;
      }
    }
    final backgroundImage = await _uploadBackgroundImage(
      template.backgroundImage,
      template.localBackground,
      uploadOss,
    );

    /// 创建时间、更新时间处理
    if (rebuildTime) {
      String dateTimeNow = DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
      profile = profile.copyWith(
        extra: profile.extra.copyWith(
          createTime: profile.extra.createTime ?? dateTimeNow,
          updateTime: dateTimeNow,
        ),
      );
    }

    /// 值转化
    if (template.values != null && template.isTubeFile) {
      template = template.copyWith(
        values: template.values!
            .map(
              (e) => CompositeValue(
                elementId: '',
                valueObjects: [e],
                delimiter: '',
                repeatCount: e is CompositeValue?
                    ? (e as CompositeValue).repeatCount
                    : 1,
              ),
            )
            .toList(),
      );
    }
    template = template.copyWith(
      dataSources: dataSourcesFormat,
      fileDataSources: fileDataSources,
      thumbnail: thumbnail,
      profile: profile,
      id: id,
    );

    final data = template.toJson();
    data['elements'] = elements;
    data['previewImage'] = template.thumbnail;
    data['backgroundImage'] = backgroundImage;
    data['templateVersion'] = templateVersion;
    if (template.dataSources?.isNotEmpty ?? false) {
      data['totalPage'] = template.dataSourceBindInfo?.total;
      data['currentPage'] = template.dataSourceBindInfo?.page;
    }
    data['values'] = (template.values ?? []).map((e) => e.toJson()).toList();
    data['templateAttributes'] =
        (template.templateAttributes ?? []).map((e) => e.value).toList();
    data['tubeFileSetting'] = template.tubeFileSetting?.toJson();
    return data;
  }

  /// 生成图像库元素
  /// [page] 第几页
  @Deprecated('请使用`TemplateData.covertElementsToNetal`')
  static List<NetalElementBase> generateNetalElements(
    TemplateData template, {
    int? page,
  }) {
    return template.covertElementsToNetal(page);
  }

  /// 生成元素
  /// [page] 第几页
  /// [index] 第几份
  static List<BaseElement> generateElements(
    TemplateData template, {
    int? page,
  }) {
    return template.elements
        .map(
          (e) => rebuildElementValue(
            e,
            dataSources: template.dataSources,
            modify: template.dataSourceModifies,
            bindInfo: template.dataSourceBindInfo,
            page: page ?? template.dataSourceBindInfo?.page ?? 1,
          ),
        )
        .toList();
  }

  /// 重新生成元素数据
  static T rebuildElementValue<T extends BaseElement>(
    T element, {
    int? page,
    List<TemplateDataSource>? dataSources,
    TemplateDataSourceModifies? modify,
    TemplateDataSourceInfo? bindInfo,
    DataSourceTextValue? dValue,
  }) {
    int startTime = DateTime.now().millisecondsSinceEpoch;
    if (element is SerialElement) {
      return element.copyWith(index: page ?? bindInfo?.page ?? 1) as T;
    }
    if (element is TableElement) {
      final cells = element.cells.map((e) {
        final value = TemplateDataSourceUtils.getElementBindValue(
          e.id,
          e.value,
          e.dataBind,
          dataSources,
          modify,
          page ?? bindInfo?.page ?? 1,
        );
        return e.copyWith(value: value);
      }).toList();
      final combineCells = element.combineCells.map((e) {
        final value = TemplateDataSourceUtils.getElementBindValue(
          e.id,
          e.value,
          e.dataBind,
          dataSources,
          modify,
          page ?? bindInfo?.page ?? 1,
        );
        return e.copyWith(value: value);
      }).toList();
      return element.copyWith(cells: cells, combineCells: combineCells) as T;
    }
    if (element is BindElement) {
      if (element.value != null) {
        final value = TemplateDataSourceUtils.getElementBindValue(
          element.id,
          element.value!,
          element.dataBind,
          dataSources,
          modify,
          page ?? bindInfo?.page ?? 1,
        );
        return element.copyWith(value: value) as T;
      }
      if (dValue != null) {
        final value = TemplateDataSourceUtils.getElementBindValue(
          element.id,
          dValue.value,
          dValue.dataBind,
          dataSources,
          modify,
          page ?? bindInfo?.page ?? 1,
        );
        return element.copyWith(value: value) as T;
      }
    }

    int endTime = DateTime.now().millisecondsSinceEpoch;
    if (kDebugMode) {
      _logger.log('rebuildElementValue => time => ${endTime - startTime}ms');
    }
    return element;
  }
}
