{"id": 185041, "name": "重命名-1", "names": [], "commodityTemplate": false, "cableDirection": 0, "cableLength": 0, "isCable": false, "paperType": 3, "consumableType": 54, "consumableTypeTextId": "app100001326", "previewImage": "https://oss-print-fat.jc-test.cn/user_resources/ce68da49bb644821825206911794ea85/100002834/templates/4ad89850-f404-11ef-b39b-cb0b6d36f93a.png", "rotate": 90, "multipleBackIndex": 0, "paccuracyName": 203, "thumbnail": "https://oss-print-fat.jc-test.cn/user_resources/ce68da49bb644821825206911794ea85/100002834/templates/4ad89850-f404-11ef-b39b-cb0b6d36f93a.png", "machineIdList": ["10027"], "platformCode": "CP001Mobile", "printedProcessList": [], "originTemplateId": 0, "elements": [{"rotate": 0, "textAlignVertical": 1, "typesettingMode": 1, "wordSpacing": 0, "type": "text", "lineSpacing": 0, "elementColor": [255, 0, 0, 0], "isLock": 0, "write_mode": 0, "fontFamily": "", "id": "2f62a7d61f0f401688b65386a75d71b2", "paperColorIndex": 0, "value": "的ins女女OK", "lineBreakMode": 0, "height": 1.754651, "textAlignHorizonral": 1, "fontCode": "ZT001", "mirrorType": 0, "isOpenMirror": 0, "letterSpacing": 0, "fontStyle": ["bold", "underline"], "typesettingParam": [0, 180], "colorChannel": 0, "colorReverse": 0, "boxStyle": "auto-width", "hasVipRes": false, "x": 10.53774, "width": 8.92452, "y": 0.5726745, "fontSize": 1.5, "textStyle": ["adaptive"]}, {"rotate": 0, "textAlignVertical": 1, "typesettingMode": 1, "wordSpacing": 0, "type": "text", "lineSpacing": 0, "elementColor": [255, 0, 0, 0], "isLock": 0, "write_mode": 0, "fontFamily": "", "id": "031c6fd94aa0467bac2686e4c911b5bd", "paperColorIndex": 0, "value": "秃噜秃噜", "lineBreakMode": 0, "height": 1.754651, "textAlignHorizonral": 1, "fontCode": "ZT001", "mirrorType": 0, "isOpenMirror": 0, "letterSpacing": 0, "fontStyle": ["bold", "underline"], "typesettingParam": [0, 180], "colorChannel": 0, "colorReverse": 0, "boxStyle": "auto-width", "hasVipRes": false, "x": 11.823476, "width": 6.353048, "y": 0.5726745, "fontSize": 1.5, "textStyle": ["adaptive"]}, {"rotate": 0, "textAlignVertical": 1, "typesettingMode": 1, "wordSpacing": 0, "type": "text", "lineSpacing": 0, "elementColor": [255, 0, 0, 0], "isLock": 0, "write_mode": 0, "fontFamily": "", "id": "2cef099da7454b2da16bff7c9a0d5c8d", "paperColorIndex": 0, "value": "我兔子", "lineBreakMode": 0, "height": 1.754651, "textAlignHorizonral": 1, "fontCode": "ZT001", "mirrorType": 0, "isOpenMirror": 0, "letterSpacing": 0, "fontStyle": ["bold", "underline"], "typesettingParam": [0, 180], "colorChannel": 0, "colorReverse": 0, "boxStyle": "auto-width", "hasVipRes": false, "x": 12.564665, "width": 4.87067, "y": 0.5726745, "fontSize": 1.5, "textStyle": ["adaptive"]}, {"rotate": 0, "textAlignVertical": 1, "typesettingMode": 1, "wordSpacing": 0, "type": "text", "lineSpacing": 0, "elementColor": [255, 0, 0, 0], "isLock": 0, "write_mode": 0, "fontFamily": "", "id": "26be539ae53a49f88486684d044a8f66", "paperColorIndex": 0, "value": "", "lineBreakMode": 0, "height": 1.754651, "textAlignHorizonral": 1, "fontCode": "ZT001", "mirrorType": 0, "isOpenMirror": 0, "letterSpacing": 0, "fontStyle": ["bold", "underline"], "typesettingParam": [0, 180], "colorChannel": 0, "colorReverse": 0, "boxStyle": "auto-width", "hasVipRes": false, "x": 13.60838, "width": 2.78324, "y": 0.5726745, "fontSize": 1.5, "textStyle": ["adaptive"]}], "height": 2.9, "margin": [0.4, 2, 0.7, 2], "printed": 0, "totalPage": 1, "usedFonts": {"fontConfig": "fontConfig.json", "ZT063": "ZT063.ttf", "ZT001": "ZT001.ttf", "fontDefault": "ZT001.ttf", "Thai": "Thai.ttf", "Fallback": "Fallback"}, "width": 30, "hasVipRes": false, "dataSourceModifies": {}, "tubeFileSetting": {"autoWidth": false, "width": 30, "line": 1, "specId": "166117282", "specName": "0.5平方瓷白"}, "values": [{"elementId": "bb41d36f1f144c58b3d7c582db6f6b51", "delimiter": "", "id": "bb41d36f1f144c58b3d7c582db6f6b51", "type": "composite", "valueObjects": [{"id": "bb41d36f1f144c58b3d7c582db6f6b51_1", "elementId": "2f62a7d61f0f401688b65386a75d71b2", "type": "composite", "value": "", "valueObjects": [{"id": "bb41d36f1f144c58b3d7c582db6f6b51_1_1", "elementId": "2f62a7d61f0f401688b65386a75d71b2", "type": "text", "value": "的ins女女OK"}], "order": 1, "delimiter": "", "repeatCount": null}], "value": "", "order": 1, "repeatCount": 1}, {"elementId": "ba30928c9b034da784b784ba4d63c777", "delimiter": "", "id": "ba30928c9b034da784b784ba4d63c777", "type": "composite", "valueObjects": [{"id": "ba30928c9b034da784b784ba4d63c777_1", "elementId": "2cef099da7454b2da16bff7c9a0d5c8d", "type": "composite", "value": "", "valueObjects": [{"id": "ba30928c9b034da784b784ba4d63c777_1_1", "elementId": "2cef099da7454b2da16bff7c9a0d5c8d", "type": "text", "value": "我兔子"}], "order": 1, "delimiter": "", "repeatCount": null}], "value": "", "order": 1, "repeatCount": 1}, {"elementId": "2047cbf64ca644e8bcb726791847fcef", "delimiter": "", "id": "2047cbf64ca644e8bcb726791847fcef", "type": "composite", "valueObjects": [{"id": "2047cbf64ca644e8bcb726791847fcef_1", "elementId": "031c6fd94aa0467bac2686e4c911b5bd", "type": "composite", "value": "", "valueObjects": [{"id": "2047cbf64ca644e8bcb726791847fcef_1_1", "elementId": "031c6fd94aa0467bac2686e4c911b5bd", "type": "text", "value": "秃噜秃噜"}], "order": 1, "delimiter": "", "repeatCount": null}], "value": "", "order": 1, "repeatCount": 1}, {"elementId": "2ec4dde1f765480fa6bf52401bbd18c0", "delimiter": "", "id": "2ec4dde1f765480fa6bf52401bbd18c0", "type": "composite", "valueObjects": [{"id": "2ec4dde1f765480fa6bf52401bbd18c0_1", "elementId": "26be539ae53a49f88486684d044a8f66", "type": "composite", "value": "", "valueObjects": [{"id": "2ec4dde1f765480fa6bf52401bbd18c0_1_1", "elementId": "26be539ae53a49f88486684d044a8f66", "type": "text", "value": ""}], "order": 1, "delimiter": "", "repeatCount": null}], "value": "", "order": 1, "repeatCount": 1}], "profile": {"machineId": "10027", "machineName": "C1", "extrain": {"userId": "100002834", "folderId": null, "labelId": null, "sourceId": null, "goodsCode": null, "isDelete": false, "createTime": "2025-02-20 16:21:29", "updateTime": "2025-02-26 13:41:13", "barcodeCategoryMap": {}, "adaptPlatformCode": "", "adaptPlatformName": "", "templateType": 0, "templateClass": 1, "isMobileTemplete": false, "isNewPath": false, "resourceVersion": null}}, "paperColor": ["0.0.0", "230.0.18"], "isEdited": "0", "templateVersion": "1.7.0.1", "dataSources": [], "supportedEditors": ["GENERAL"], "templateAttributes": [8]}