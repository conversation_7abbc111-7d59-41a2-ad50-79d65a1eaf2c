{"id": 149206, "name": "分", "names": [], "commodityTemplate": false, "cableDirection": 0, "cableLength": 0, "isCable": false, "paperType": 1, "consumableType": 1, "previewImage": "https://oss-print-fat.jc-test.cn/user_resources/452c6009b37841758e3697741b778a5f/100000436/files/55660712fda38fa35cfb0f8bf2841d187168f1b3.png", "rotate": 0, "multipleBackIndex": 0, "paccuracyName": 203, "thumbnail": "https://oss-print-fat.jc-test.cn/user_resources/452c6009b37841758e3697741b778a5f/100000436/files/55660712fda38fa35cfb0f8bf2841d187168f1b3.png", "machineIdList": ["55"], "version": "2.0.6", "platformCode": "CP001PC", "fromOldVersion": 2, "printedProcessList": [], "originTemplateId": 0, "currentPage": 1, "elements": [{"rotate": 0, "itemType": "widget", "printMultiple": 8, "textAlignVertical": 0, "typesettingMode": 0, "wordSpacing": 0, "type": "text", "lineSpacing": 0, "elementColor": [255, 0, 0, 0], "isLock": 0, "uid": "w7395a6b6-a7b9-4fc4-aa95-4f540bb92b1d", "write_mode": 0, "fontFamily": "鸿蒙", "zoomable": "w, e", "id": "w7395a6b6-a7b9-4fc4-aa95-4f540bb92b1d", "locked": false, "value": "A1", "dataBind": ["3219463db5b25b9584458baa27c25a5f", "表格1"], "lineBreakMode": 0, "zIndex": 1, "height": 4.25, "textAlignHorizonral": 0, "fontCode": "ZT001", "lineMode": 2, "letterSpacing": 0, "fontStyle": [], "typesettingParam": [0, 180], "colorReverse": 0, "colorChannel": 0, "isTitle": false, "x": 0, "name": "text", "width": 50, "y": 0, "fontSize": 3.2, "centralPoint": 0, "rotatePoint": 5, "displayScale": 1, "fontColor": [0, 0, 0]}, {"rotate": 0, "itemType": "widget", "printMultiple": 8, "textAlignVertical": 0, "typesettingMode": 0, "wordSpacing": 0, "type": "text", "lineSpacing": 0, "elementColor": [255, 0, 0, 0], "isLock": 0, "uid": "w5480f22a-7a2a-4ebf-a75a-450a8a954da4", "write_mode": 0, "fontFamily": "鸿蒙", "zoomable": "w, e", "id": "w5480f22a-7a2a-4ebf-a75a-450a8a954da4", "locked": false, "value": "B1", "dataBind": ["3219463db5b25b9584458baa27c25a5f", "表格1"], "lineBreakMode": 0, "zIndex": 1, "height": 4.25, "textAlignHorizonral": 0, "fontCode": "ZT001", "lineMode": 2, "letterSpacing": 0, "fontStyle": [], "typesettingParam": [0, 180], "colorReverse": 0, "colorChannel": 0, "isTitle": false, "x": 0, "name": "text", "width": 50, "y": 4, "fontSize": 3.2, "centralPoint": 0, "rotatePoint": 5, "displayScale": 1, "fontColor": [0, 0, 0]}, {"rotate": 0, "itemType": "widget", "printMultiple": 8, "textAlignVertical": 0, "typesettingMode": 0, "wordSpacing": 0, "type": "text", "lineSpacing": 0, "elementColor": [255, 0, 0, 0], "isLock": 0, "uid": "wc4100745-dc20-4ada-acad-0987404f4b5f", "write_mode": 0, "fontFamily": "鸿蒙", "zoomable": "w, e", "id": "wc4100745-dc20-4ada-acad-0987404f4b5f", "locked": false, "value": "C1", "dataBind": ["3219463db5b25b9584458baa27c25a5f", "表格1"], "lineBreakMode": 0, "zIndex": 1, "height": 4.25, "textAlignHorizonral": 0, "fontCode": "ZT001", "lineMode": 2, "letterSpacing": 0, "fontStyle": [], "typesettingParam": [0, 180], "colorReverse": 0, "colorChannel": 0, "isTitle": false, "x": 0, "name": "text", "width": 50, "y": 8, "fontSize": 3.2, "centralPoint": 0, "rotatePoint": 5, "displayScale": 1, "fontColor": [0, 0, 0]}], "height": 30, "margin": [0, 0, 0, 0], "printed": 0, "thickness": 0, "totalPage": 3, "usedFonts": {}, "width": 50, "hasVipRes": false, "dataSourceModifies": {"w5480f22a-7a2a-4ebf-a75a-450a8a954da4": {"0": {"useTitle": true, "delimiter": "："}}, "wc4100745-dc20-4ada-acad-0987404f4b5f": {"0": {"useTitle": true, "delimiter": "："}}, "w7395a6b6-a7b9-4fc4-aa95-4f540bb92b1d": {"0": {"useTitle": true, "delimiter": "："}}}, "dataSourceBindInfo": {"total": 3, "page": 1}, "profile": {"machineId": "55", "machineName": "B1", "extrain": {"userId": "100000436", "folderId": null, "labelId": null, "sourceId": null, "goodsCode": null, "isDelete": false, "createTime": "2024-05-07 14:15:58", "updateTime": "2024-05-07 14:16:28", "barcodeCategoryMap": {}, "adaptPlatformCode": "", "adaptPlatformName": "", "templateType": 0, "templateClass": 1, "isMobileTemplete": false, "isNewPath": false, "resourceVersion": null}}, "paperColor": [], "isEdited": "0", "templateVersion": "*******", "dataSources": [{"headers": {"表格1": 1}, "name": "测试销售价格(1).xlsx", "range": [{"s": 2, "e": 4}], "type": "excel", "uri": "https://oss-print-fat.jc-test.cn/user_resources/452c6009b37841758e3697741b778a5f/100000436/files/3219463db5b25b9584458baa27c25a5f.xlsx", "hash": "3219463db5b25b9584458baa27c25a5f"}]}