import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_template/utils/element_utils.dart';

void main() {
  group('简化后的12小时制处理测试', () {
    group('addHourUnitToTimeFormat 处理已格式化字符串', () {
      test('H 格式 - 纯数字字符串', () {
        final result = ElementUtils.addHourUnitToTimeFormat('2', 'H', langCode: 'zh');
        expect(result, '2时');
        print('H 格式处理: "2" -> "$result"');
      });

      test('HH 格式 - 纯数字字符串', () {
        final result = ElementUtils.addHourUnitToTimeFormat('14', 'HH', langCode: 'zh');
        expect(result, '14时');
        print('HH 格式处理: "14" -> "$result"');
      });

      test('HH:mm 格式 - 包含冒号的字符串', () {
        final result = ElementUtils.addHourUnitToTimeFormat('2:30', 'HH:mm', langCode: 'zh');
        expect(result, '2:30');
        print('HH:mm 格式处理: "2:30" -> "$result"');
      });

      test('HH:mm:ss 格式 - 完整时间字符串', () {
        final result = ElementUtils.addHourUnitToTimeFormat('2:30:45', 'HH:mm:ss', langCode: 'zh');
        expect(result, '2:30:45');
        print('HH:mm:ss 格式处理: "2:30:45" -> "$result"');
      });
    });

    group('12小时制完整流程模拟', () {
      test('模拟12小时制处理流程', () {
        print('\n=== 12小时制处理流程模拟 ===');
        
        // 模拟不同格式的12小时制处理
        final testCases = [
          {
            'timeFormat': 'H',
            'originalTime': '2:30 PM',
            'afterAmPmReplace': '下午2',
            'afterReverse': '下午2',
            'expected': '下午2时',
            'desc': 'H 格式 - 应该添加时间单位'
          },
          {
            'timeFormat': 'HH',
            'originalTime': '02:30 PM',
            'afterAmPmReplace': '下午02',
            'afterReverse': '下午02',
            'expected': '下午02时',
            'desc': 'HH 格式 - 应该添加时间单位'
          },
          {
            'timeFormat': 'HH:mm',
            'originalTime': '02:30 PM',
            'afterAmPmReplace': '下午02:30',
            'afterReverse': '下午02:30',
            'expected': '下午02:30',
            'desc': 'HH:mm 格式 - 不应该添加时间单位'
          },
          {
            'timeFormat': 'HH:mm:ss',
            'originalTime': '02:30:45 PM',
            'afterAmPmReplace': '下午02:30:45',
            'afterReverse': '下午02:30:45',
            'expected': '下午02:30:45',
            'desc': 'HH:mm:ss 格式 - 不应该添加时间单位'
          },
        ];

        for (final testCase in testCases) {
          final timeFormat = testCase['timeFormat'] as String;
          final afterReverse = testCase['afterReverse'] as String;
          final expected = testCase['expected'] as String;
          final desc = testCase['desc'] as String;
          
          print('\n$desc:');
          print('  原始时间: ${testCase['originalTime']}');
          print('  AM/PM替换后: ${testCase['afterAmPmReplace']}');
          print('  顺序调整后: $afterReverse');
          
          // 使用简化后的逻辑
          final result = ElementUtils.addHourUnitToTimeFormat(afterReverse, timeFormat, langCode: 'zh');
          
          print('  最终结果: $result');
          print('  期望结果: $expected');
          print('  是否正确: ${result == expected}');
          
          expect(result, expected, reason: '简化逻辑应该正确处理: $desc');
        }
      });
    });

    group('不同语言环境测试', () {
      test('简体中文环境', () {
        final result = ElementUtils.addHourUnitToTimeFormat('下午2', 'H', langCode: 'zh');
        expect(result, '下午2时');
        print('简体中文: "下午2" -> "$result"');
      });

      test('繁体中文环境', () {
        final result = ElementUtils.addHourUnitToTimeFormat('下午2', 'H', langCode: 'zh_Hant');
        expect(result, '下午2時');
        print('繁体中文: "下午2" -> "$result"');
      });

      test('韩语环境', () {
        final result = ElementUtils.addHourUnitToTimeFormat('오후2', 'H', langCode: 'ko');
        expect(result, '오후2시');
        print('韩语: "오후2" -> "$result"');
      });

      test('日语环境', () {
        final result = ElementUtils.addHourUnitToTimeFormat('午後2', 'H', langCode: 'ja');
        expect(result, '午後2時');
        print('日语: "午後2" -> "$result"');
      });

      test('英语环境 - 不添加单位', () {
        final result = ElementUtils.addHourUnitToTimeFormat('2 PM', 'H', langCode: 'en');
        expect(result, '2 PM');
        print('英语: "2 PM" -> "$result"');
      });
    });

    group('简化前后对比', () {
      test('逻辑复杂度对比', () {
        print('\n=== 简化前后对比 ===');
        
        print('❌ 简化前的复杂逻辑:');
        print('  1. 检查 timeFormat == "HH" || timeFormat == "H"');
        print('  2. 使用正则表达式提取小时数字');
        print('  3. 调用 addHourUnitToTimeFormat 处理提取的数字');
        print('  4. 使用正则表达式替换原字符串中的小时部分');
        print('  风险: 正则匹配可能出错，字符串替换可能替换错位置');
        
        print('\n✅ 简化后的清晰逻辑:');
        print('  1. 直接调用 addHourUnitToTimeFormat(timeFormatVal, timeFormat)');
        print('  2. addHourUnitToTimeFormat 内部自动判断是否需要添加单位');
        print('  优势: 逻辑简单，无正则风险，职责清晰');
        
        print('\n核心改进:');
        print('  - 代码行数: 17行 -> 3行');
        print('  - 复杂度: 高 -> 低');
        print('  - 风险: 有正则匹配风险 -> 无风险');
        print('  - 可读性: 复杂 -> 清晰');
      });
    });

    group('边界情况测试', () {
      test('空字符串处理', () {
        final result = ElementUtils.addHourUnitToTimeFormat('', 'H', langCode: 'zh');
        expect(result, '时'); // 空字符串 + 时间单位
        print('空字符串: "" -> "$result"');
      });

      test('特殊字符处理', () {
        final result = ElementUtils.addHourUnitToTimeFormat('上午12', 'H', langCode: 'zh');
        expect(result, '上午12时');
        print('包含中文: "上午12" -> "$result"');
      });

      test('非H/HH格式的安全处理', () {
        final result = ElementUtils.addHourUnitToTimeFormat('复杂字符串', 'mm:ss', langCode: 'zh');
        expect(result, '复杂字符串'); // 不是H/HH格式，直接返回
        print('非H/HH格式: "复杂字符串" -> "$result"');
      });
    });
  });
}
