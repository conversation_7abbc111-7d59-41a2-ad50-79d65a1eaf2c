import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_template/models/parse/parse_exception.dart';
import 'package:path/path.dart';
import 'package:stack_trace/stack_trace.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';

import 'package:niimbot_template/template_parse.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/elements/value_element.dart';
import 'package:niimbot_template/models/template/constants.dart';

import 'fake_path_provider_platform.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  setUp(() async {
    PathProviderPlatform.instance = FakePathProviderPlatform();
    // await NetalPlugin().init();
  });
  test('模板JSON解析', () async {
    final full_18_Str =
        await File(join(getRootDir(), 'assets/full_18.json')).readAsString();
    final full_18 = await TemplateParse.parse(full_18_Str);
    expect(full_18.elements.length, 1);

    final JsonTest3_Str =
        await File(join(getRootDir(), 'assets/JsonTest3.json')).readAsString();
    final JsonTest3 = await TemplateParse.parse(JsonTest3_Str);
    expect(JsonTest3.elements.length, 1);

    final print_json_40_30_Str =
        await File(
          join(getRootDir(), 'assets/print_json_40_30.json'),
        ).readAsString();
    final print_json_40_30 = await TemplateParse.parse(print_json_40_30_Str);
    expect(print_json_40_30.elements.length, 16);

    final print_json_40_30_empty_Str =
        await File(
          join(getRootDir(), 'assets/print_json_40_30_empty.json'),
        ).readAsString();
    final print_json_40_30_empty = await TemplateParse.parse(
      print_json_40_30_empty_Str,
    );
    expect(print_json_40_30_empty.elements.length, 0);

    final print_json_45_80_Str =
        await File(
          join(getRootDir(), 'assets/print_json_45_80.json'),
        ).readAsString();
    final print_json_45_80 = await TemplateParse.parse(print_json_45_80_Str);
    expect(print_json_45_80.elements.length, 0);

    final printer_json_40_30_Str =
        await File(
          join(getRootDir(), 'assets/printer_json_40_30.json'),
        ).readAsString();
    final printer_json_40_30 = await TemplateParse.parse(
      printer_json_40_30_Str,
    );
    expect(printer_json_40_30.elements.length, 0);

    final test_json_Str =
        await File(join(getRootDir(), 'assets/test_json.json')).readAsString();
    final test_json = await TemplateParse.parse(test_json_Str);
    expect(test_json.elements.length, 1);

    final yyjqmb_Str =
        await File(join(getRootDir(), 'assets/医药价签模板.json')).readAsString();
    final yyjqmb = await TemplateParse.parse(yyjqmb_Str);
    expect(yyjqmb.elements.length, 5);

    final spmb_Str =
        await File(join(getRootDir(), 'assets/商品模板.json')).readAsString();
    final spmb = await TemplateParse.parse(spmb_Str);
    expect(spmb.elements.length, 6);
  });

  test('模板版本解析', () async {
    final res = TemplateParse.parseTemplateVersion('');
    expect(res, TemplateConstants.SAVE_TEMPLATE_VERSION);

    final res2 = TemplateParse.parseTemplateVersion('1.6.3');
    expect(res2, TemplateConstants.SAVE_TEMPLATE_VERSION);

    final res3 = TemplateParse.parseTemplateVersion('1.6.3.3');
    expect(res3, '1.6.3.3');

    try {
      TemplateParse.parseTemplateVersion('1.7.3.3');
    } on TemplateParseException catch (error) {
      expect(error.code, TemplateParseExceptionCode.versionNotSupport);
    }

    try {
      TemplateParse.parseTemplateVersion('1.7.0.3');
    } on TemplateParseException catch (error) {
      expect(error.code, TemplateParseExceptionCode.versionNotSupport);
    }

    final res4 = TemplateParse.parseTemplateVersion('1.7.3.0');
    expect(res4, '1.7.3.0');
  });

  test('模板数据源信息解析', () async {
    final print_json_40_30_Str =
        await File(
          join(getRootDir(), 'assets/print_json_40_30.json'),
        ).readAsString();
    final print_json_40_30 = await TemplateParse.parse(print_json_40_30_Str);
    expect(print_json_40_30.dataSourceBindInfo?.page, 1);
    expect(print_json_40_30.dataSourceBindInfo?.total, 1999);
  });

  test('元素数值并修正解析', () async {
    final lineSpacing1_Str =
        await File(
          join(getRootDir(), 'assets/fix_value/lineSpacing1.json'),
        ).readAsString();
    final lineSpacing1 = await TemplateParse.parse(lineSpacing1_Str);
    expect((lineSpacing1.elements.first as TextElement).lineSpacing, 1.2);

    final lineSpacing2_Str =
        await File(
          join(getRootDir(), 'assets/fix_value/lineSpacing2.json'),
        ).readAsString();
    final lineSpacing2 = await TemplateParse.parse(lineSpacing2_Str);
    expect((lineSpacing2.elements.first as TextElement).lineSpacing, 1);

    final fontSize_Str =
        await File(
          join(getRootDir(), 'assets/fix_value/fontSize.json'),
        ).readAsString();
    final fontSize = await TemplateParse.parse(fontSize_Str);
    expect((fontSize.elements.first as TextElement).fontSize, 3.7);
  });

  test('元素绑定值解析', () async {
    final value_parse_Str =
        await File(
          join(getRootDir(), 'assets/data_source/value_parse.json'),
        ).readAsString();
    final value_parse = await TemplateParse.parse(value_parse_Str);
    expect((value_parse.elements.first as ValueElement).value, 'B0');
  });

  test('元素数据源解析', () async {
    final value_parse_Str =
        await File(
          join(getRootDir(), 'assets/data_source/value_parse.json'),
        ).readAsString();
    final value_parse = await TemplateParse.parse(value_parse_Str);
    expect(value_parse.dataSources?.length, 1);
  });

  test('元素数据源修改标记解析', () async {
    final value_parse_Str =
        await File(
          join(getRootDir(), 'assets/data_source/value_parse.json'),
        ).readAsString();
    final value_parse = await TemplateParse.parse(value_parse_Str);
    expect(value_parse.dataSourceModifies?.length, 4);
  });

  test('元素网络资源_图片资源', () async {
    final image_resource_Str =
        await File(
          join(getRootDir(), 'assets/net_resource/image_resource.json'),
        ).readAsString();
    final image_resource = await TemplateParse.parse(image_resource_Str);
    expect((image_resource.elements.first as ImageElement).imageData, "");
  });

  test('数据源解析_网络资源解析', () async {
    final net_data_source_Str =
        await File(
          join(getRootDir(), 'assets/data_source/net_data_source.json'),
        ).readAsString();
    final net_data_source = await TemplateParse.parse(net_data_source_Str);
    expect((net_data_source.elements.first as ImageElement).imageData, "");
  });

  test('labelNames解析与生成', () async {
    final label_names_Str =
        await File(
          join(getRootDir(), 'assets/label_names.json'),
        ).readAsString();
    final label_names = await TemplateParse.parse(label_names_Str);
    expect(label_names.labelNames.length, 1);
    expect(label_names.toJson()['labelNames'] is List, true);
  });
}

String getRootDir() {
  final frame = Frame.caller(1);
  return dirname(frame.uri.path.substring(1, frame.uri.path.length));
}
