import 'dart:io';

import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:netal_plugin/models/netal_text_element.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/parse/parse_exception.dart';
import 'package:niimbot_template/template_parse.dart';
import 'package:path/path.dart';
import 'package:stack_trace/stack_trace.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:collection/collection.dart';

import 'fake_path_provider_platform.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  setUp(() async {
    PathProviderPlatform.instance = FakePathProviderPlatform();
    // await NetalPlugin().init();
  });
  test('解析Values模式模板', () async {
    final c1_default_Str =
        await File(
          join(getRootDir(), 'assets/values/c1_default_template_file.json'),
        ).readAsString();
    final c1_default = await TemplateParse.parse(c1_default_Str);
    expect(c1_default.values?.length, 1);
    final text = c1_default.elements.whereType<TextElement>().first;
    final netal = c1_default.coverElementToNetal<NetalTextElement>(text);
    expect(netal.value, '');
  });

  test('解析Values模式模板-老模板', () async {
    final template_Str =
        await File(join(getRootDir(), 'assets/values/老模板.jcps')).readAsString();
    final c1_default = await TemplateParse.parse(template_Str);
    final text = c1_default.elements.whereType<TextElement>().first;
    final netal = c1_default.coverElementToNetal<NetalTextElement>(text);
    expect(netal.value, '哦婆婆婆婆01');
  });

  test('解析Values模式模板-双行不支持', () async {
    final template_Str =
        await File(
          join(getRootDir(), 'assets/values/未命名文件4.jcps'),
        ).readAsString();
    try {
      await TemplateParse.parse(template_Str);
    } on TemplateParseException catch (e) {
      expect(e.code, TemplateParseExceptionCode.valuesNotSupport);
    }
  });
}
