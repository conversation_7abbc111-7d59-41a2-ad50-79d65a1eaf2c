import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_template/utils/element_utils.dart';

void main() {
  group('英文24小时制格式测试', () {
    final testDateTime = DateTime(2025, 7, 15, 14, 30, 45); // 2025年7月15日 14:30:45

    group('英文环境24小时制 - 不应该添加时间单位', () {
      test('仅时间 HH 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'HH',
          militaryTime: true,
        );
        
        print('英文 HH 格式结果: $result');
        // 英文环境下应该是纯数字，不包含时间单位
        expect(result, '14');
        expect(result.contains('时'), false);
        expect(result.contains('時'), false);
        expect(result.contains('시'), false);
      });

      test('仅时间 H 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'H',
          militaryTime: true,
        );
        
        print('英文 H 格式结果: $result');
        expect(result, '14');
        expect(result.contains('时'), false);
      });

      test('HH:mm 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'HH:mm',
          militaryTime: true,
        );
        
        print('英文 HH:mm 格式结果: $result');
        expect(result, '14:30');
        expect(result.contains('时'), false);
      });

      test('HH:mm:ss 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'HH:mm:ss',
          militaryTime: true,
        );
        
        print('英文 HH:mm:ss 格式结果: $result');
        expect(result, '14:30:45');
        expect(result.contains('时'), false);
      });
    });

    group('英文环境 + 日期格式', () {
      test('标准日期 + HH 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy-MM-dd',
          timeFormat: 'HH',
          militaryTime: true,
        );
        
        print('标准日期 + HH: $result');
        expect(result, '2025-07-15 14');
        expect(result.contains('时'), false);
      });

      test('特殊英语日期 + HH 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMMM yyyy',
          timeFormat: 'HH',
          militaryTime: true,
        );
        
        print('特殊英语日期 + HH: $result');
        expect(result, 'July2025 14');
        expect(result.contains('时'), false);
      });

      test('MMM.yyyy + H 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMM.yyyy',
          timeFormat: 'H',
          militaryTime: true,
        );
        
        print('MMM.yyyy + H: $result');
        expect(result, 'Jul.2025 14');
        expect(result.contains('时'), false);
      });

      test('标准日期 + HH:mm 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy-MM-dd',
          timeFormat: 'HH:mm',
          militaryTime: true,
        );
        
        print('标准日期 + HH:mm: $result');
        expect(result, '2025-07-15 14:30');
        expect(result.contains('时'), false);
      });
    });

    group('英文环境12小时制 - 也不应该添加时间单位', () {
      test('12小时制 HH 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'HH',
          militaryTime: false,
        );
        
        print('英文12小时制 HH: $result');
        // 英文12小时制应该包含AM/PM但不包含时间单位
        expect(result.contains('时'), false);
        expect(result.contains('時'), false);
        expect(result.contains('시'), false);
      });

      test('12小时制 + 日期格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMMM yyyy',
          timeFormat: 'HH',
          militaryTime: false,
        );
        
        print('英文12小时制 + 日期: $result');
        expect(result.contains('July2025'), true);
        expect(result.contains('时'), false);
      });
    });

    group('边界时间测试', () {
      test('午夜 00:00', () {
        final midnightTime = DateTime(2025, 7, 15, 0, 0).millisecondsSinceEpoch;
        final result = ElementUtils.buildDateValue(
          time: midnightTime,
          timeFormat: 'HH',
          militaryTime: true,
        );
        
        print('午夜英文24小时制: $result');
        expect(result, '00');
        expect(result.contains('时'), false);
      });

      test('正午 12:00', () {
        final noonTime = DateTime(2025, 7, 15, 12, 0).millisecondsSinceEpoch;
        final result = ElementUtils.buildDateValue(
          time: noonTime,
          timeFormat: 'HH',
          militaryTime: true,
        );
        
        print('正午英文24小时制: $result');
        expect(result, '12');
        expect(result.contains('时'), false);
      });

      test('晚上 23:59', () {
        final nightTime = DateTime(2025, 7, 15, 23, 59).millisecondsSinceEpoch;
        final result = ElementUtils.buildDateValue(
          time: nightTime,
          timeFormat: 'HH:mm',
          militaryTime: true,
        );
        
        print('晚上英文24小时制: $result');
        expect(result, '23:59');
        expect(result.contains('时'), false);
      });
    });

    group('前缀测试', () {
      test('带前缀的英文24小时制', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMMM yyyy',
          timeFormat: 'HH',
          militaryTime: true,
          prefix: 'Time',
        );
        
        print('带前缀的英文24小时制: $result');
        expect(result.contains('Time'), true);
        expect(result.contains('July2025'), true);
        expect(result.contains('14'), true);
        expect(result.contains('时'), false);
      });
    });
  });

  group('对比测试 - 英文 vs CJK', () {
    final testTime = DateTime(2025, 7, 15, 14, 30).millisecondsSinceEpoch;

    test('英文环境 vs CJK环境对比', () {
      // 注意：这个测试需要模拟不同的语言环境
      // 在实际测试中，需要设置正确的locale
      
      // 模拟英文环境结果（当前默认）
      final englishResult = ElementUtils.buildDateValue(
        time: testTime,
        timeFormat: 'HH',
        militaryTime: true,
      );
      
      print('英文环境结果: $englishResult');
      print('期望CJK环境结果: 14时 (简中), 14時 (繁中), 14시 (韩), 14時 (日)');
      
      // 英文环境不应该包含时间单位
      expect(englishResult.contains('时'), false);
      expect(englishResult.contains('時'), false);
      expect(englishResult.contains('시'), false);
      
      // 英文环境应该是纯数字
      expect(englishResult, '14');
    });
  });
}
