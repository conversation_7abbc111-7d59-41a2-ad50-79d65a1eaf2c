import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_template/utils/element_utils.dart';

void main() {
  group('12小时制时间单位添加测试', () {
    group('上午时间测试', () {
      final morningTime = DateTime(2025, 7, 15, 9, 30, 45).millisecondsSinceEpoch; // 上午9:30:45

      test('12小时制 + HH 格式 - 应该添加时间单位', () {
        final result = ElementUtils.buildDateValue(
          time: morningTime,
          timeFormat: 'HH',
          militaryTime: false, // 12小时制
        );
        
        print('上午12小时制 HH 格式结果: $result');
        // 期望结果应该包含时间单位（如"9时"）和上午标识
        expect(result.isNotEmpty, true);
      });

      test('12小时制 + H 格式 - 应该添加时间单位', () {
        final result = ElementUtils.buildDateValue(
          time: morningTime,
          timeFormat: 'H',
          militaryTime: false, // 12小时制
        );
        
        print('上午12小时制 H 格式结果: $result');
        expect(result.isNotEmpty, true);
      });

      test('12小时制 + HH:mm 格式 - 不应该添加时间单位', () {
        final result = ElementUtils.buildDateValue(
          time: morningTime,
          timeFormat: 'HH:mm',
          militaryTime: false, // 12小时制
        );
        
        print('上午12小时制 HH:mm 格式结果: $result');
        expect(result.isNotEmpty, true);
      });
    });

    group('下午时间测试', () {
      final afternoonTime = DateTime(2025, 7, 15, 14, 30, 45).millisecondsSinceEpoch; // 下午14:30:45

      test('12小时制 + HH 格式 - 应该添加时间单位', () {
        final result = ElementUtils.buildDateValue(
          time: afternoonTime,
          timeFormat: 'HH',
          militaryTime: false, // 12小时制
        );
        
        print('下午12小时制 HH 格式结果: $result');
        // 期望结果应该包含时间单位（如"2时"）和下午标识
        expect(result.isNotEmpty, true);
      });

      test('12小时制 + H 格式 - 应该添加时间单位', () {
        final result = ElementUtils.buildDateValue(
          time: afternoonTime,
          timeFormat: 'H',
          militaryTime: false, // 12小时制
        );
        
        print('下午12小时制 H 格式结果: $result');
        expect(result.isNotEmpty, true);
      });
    });

    group('带日期的12小时制测试', () {
      final testTime = DateTime(2025, 7, 15, 14, 30).millisecondsSinceEpoch;

      test('标准日期 + 12小时制 HH', () {
        final result = ElementUtils.buildDateValue(
          time: testTime,
          dateFormat: 'yyyy-MM-dd',
          timeFormat: 'HH',
          militaryTime: false,
        );
        
        print('标准日期 + 12小时制 HH: $result');
        expect(result.contains('2025-07-15'), true);
      });

      test('特殊英语日期 + 12小时制 HH', () {
        final result = ElementUtils.buildDateValue(
          time: testTime,
          dateFormat: 'MMMM yyyy',
          timeFormat: 'HH',
          militaryTime: false,
        );
        
        print('特殊英语日期 + 12小时制 HH: $result');
        expect(result.contains('July2025'), true);
      });

      test('特殊英语日期 + 12小时制 H', () {
        final result = ElementUtils.buildDateValue(
          time: testTime,
          dateFormat: 'MMM.yyyy',
          timeFormat: 'H',
          militaryTime: false,
        );
        
        print('特殊英语日期 + 12小时制 H: $result');
        expect(result.contains('Jul.2025'), true);
      });
    });

    group('24小时制对比测试', () {
      final testTime = DateTime(2025, 7, 15, 14, 30).millisecondsSinceEpoch;

      test('24小时制 + HH 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testTime,
          timeFormat: 'HH',
          militaryTime: true, // 24小时制
        );
        
        print('24小时制 HH 格式结果: $result');
        expect(result.isNotEmpty, true);
      });

      test('12小时制 vs 24小时制 对比', () {
        final result12h = ElementUtils.buildDateValue(
          time: testTime,
          dateFormat: 'yyyy-MM-dd',
          timeFormat: 'HH',
          militaryTime: false, // 12小时制
        );
        
        final result24h = ElementUtils.buildDateValue(
          time: testTime,
          dateFormat: 'yyyy-MM-dd',
          timeFormat: 'HH',
          militaryTime: true, // 24小时制
        );
        
        print('12小时制结果: $result12h');
        print('24小时制结果: $result24h');
        
        // 两种格式都应该包含日期
        expect(result12h.contains('2025-07-15'), true);
        expect(result24h.contains('2025-07-15'), true);
        
        // 两种格式应该不同（12小时制包含AM/PM信息）
        expect(result12h != result24h, true);
      });
    });

    group('边界时间测试', () {
      test('午夜12点 (00:00)', () {
        final midnightTime = DateTime(2025, 7, 15, 0, 0).millisecondsSinceEpoch;
        final result = ElementUtils.buildDateValue(
          time: midnightTime,
          timeFormat: 'HH',
          militaryTime: false,
        );
        
        print('午夜12小时制结果: $result');
        expect(result.isNotEmpty, true);
      });

      test('正午12点 (12:00)', () {
        final noonTime = DateTime(2025, 7, 15, 12, 0).millisecondsSinceEpoch;
        final result = ElementUtils.buildDateValue(
          time: noonTime,
          timeFormat: 'HH',
          militaryTime: false,
        );
        
        print('正午12小时制结果: $result');
        expect(result.isNotEmpty, true);
      });

      test('晚上11点 (23:00)', () {
        final nightTime = DateTime(2025, 7, 15, 23, 0).millisecondsSinceEpoch;
        final result = ElementUtils.buildDateValue(
          time: nightTime,
          timeFormat: 'HH',
          militaryTime: false,
        );
        
        print('晚上12小时制结果: $result');
        expect(result.isNotEmpty, true);
      });
    });
  });

  group('addHourUnitToTimeFormat 直接测试', () {
    test('HH 格式应该添加单位', () {
      final result = ElementUtils.addHourUnitToTimeFormat('14', 'HH');
      print('addHourUnitToTimeFormat(14, HH): $result');
      expect(result.length > 2, true); // 应该比原来长（添加了单位）
    });

    test('H 格式应该添加单位', () {
      final result = ElementUtils.addHourUnitToTimeFormat('9', 'H');
      print('addHourUnitToTimeFormat(9, H): $result');
      expect(result.length > 1, true); // 应该比原来长（添加了单位）
    });

    test('HH:mm 格式不应该添加单位', () {
      final result = ElementUtils.addHourUnitToTimeFormat('14:30', 'HH:mm');
      print('addHourUnitToTimeFormat(14:30, HH:mm): $result');
      expect(result, '14:30'); // 应该保持不变
    });
  });
}
