import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_template/utils/element_utils.dart';

void main() {
  group('时间格式单位添加规则测试', () {
    final testDateTime = DateTime(2025, 7, 15, 14, 30, 45); // 2025年7月15日 14:30:45

    group('addHourUnitToTimeFormat 直接测试', () {
      test('H 格式 - 应该添加单位', () {
        final result = ElementUtils.addHourUnitToTimeFormat('14', 'H', langCode: 'zh');
        expect(result, '14时');
        print('H 格式: 14 -> $result');
      });

      test('HH 格式 - 应该添加单位', () {
        final result = ElementUtils.addHourUnitToTimeFormat('14', 'HH', langCode: 'zh');
        expect(result, '14时');
        print('HH 格式: 14 -> $result');
      });

      test('HH:mm 格式 - 不应该添加单位', () {
        final result = ElementUtils.addHourUnitToTimeFormat('14:30', 'HH:mm', langCode: 'zh');
        expect(result, '14:30');
        print('HH:mm 格式: 14:30 -> $result');
      });

      test('HH:mm:ss 格式 - 不应该添加单位', () {
        final result = ElementUtils.addHourUnitToTimeFormat('14:30:45', 'HH:mm:ss', langCode: 'zh');
        expect(result, '14:30:45');
        print('HH:mm:ss 格式: 14:30:45 -> $result');
      });

      test('mm:ss 格式 - 不应该添加单位', () {
        final result = ElementUtils.addHourUnitToTimeFormat('30:45', 'mm:ss', langCode: 'zh');
        expect(result, '30:45');
        print('mm:ss 格式: 30:45 -> $result');
      });
    });

    group('24小时制完整测试', () {
      test('24小时制 + H 格式 - 应该添加单位', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'H',
          militaryTime: true,
        );
        
        print('24小时制 H 格式: $result');
        // 在CJK语言环境下应该包含时间单位
        // 在英文环境下应该是纯数字
      });

      test('24小时制 + HH 格式 - 应该添加单位', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'HH',
          militaryTime: true,
        );
        
        print('24小时制 HH 格式: $result');
      });

      test('24小时制 + HH:mm 格式 - 不应该添加单位', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'HH:mm',
          militaryTime: true,
        );
        
        print('24小时制 HH:mm 格式: $result');
        expect(result, '14:30');
        expect(result.contains('时'), false);
      });

      test('24小时制 + HH:mm:ss 格式 - 不应该添加单位', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'HH:mm:ss',
          militaryTime: true,
        );
        
        print('24小时制 HH:mm:ss 格式: $result');
        expect(result, '14:30:45');
        expect(result.contains('时'), false);
      });

      test('24小时制 + mm:ss 格式 - 不应该添加单位', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'mm:ss',
          militaryTime: true,
        );
        
        print('24小时制 mm:ss 格式: $result');
        expect(result, '30:45');
        expect(result.contains('时'), false);
      });
    });

    group('12小时制完整测试', () {
      test('12小时制 + H 格式 - 应该添加单位', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'H',
          militaryTime: false,
        );
        
        print('12小时制 H 格式: $result');
        // 应该包含AM/PM信息，CJK语言环境下还应该包含时间单位
      });

      test('12小时制 + HH 格式 - 应该添加单位', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'HH',
          militaryTime: false,
        );
        
        print('12小时制 HH 格式: $result');
      });

      test('12小时制 + HH:mm 格式 - 不应该添加单位', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'HH:mm',
          militaryTime: false,
        );
        
        print('12小时制 HH:mm 格式: $result');
        // 应该包含AM/PM信息，但不应该包含时间单位
        expect(result.contains('时'), false);
      });

      test('12小时制 + HH:mm:ss 格式 - 不应该添加单位', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'HH:mm:ss',
          militaryTime: false,
        );
        
        print('12小时制 HH:mm:ss 格式: $result');
        expect(result.contains('时'), false);
      });
    });

    group('带日期的时间格式测试', () {
      test('标准日期 + H 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy-MM-dd',
          timeFormat: 'H',
          militaryTime: true,
        );
        
        print('标准日期 + H: $result');
        expect(result.contains('2025-07-15'), true);
      });

      test('标准日期 + HH:mm 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy-MM-dd',
          timeFormat: 'HH:mm',
          militaryTime: true,
        );
        
        print('标准日期 + HH:mm: $result');
        expect(result, '2025-07-15 14:30');
        expect(result.contains('时'), false);
      });

      test('特殊英语日期 + H 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMMM yyyy',
          timeFormat: 'H',
          militaryTime: true,
        );
        
        print('特殊英语日期 + H: $result');
        expect(result.contains('July2025'), true);
      });

      test('特殊英语日期 + HH:mm 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMM.yyyy',
          timeFormat: 'HH:mm',
          militaryTime: true,
        );
        
        print('特殊英语日期 + HH:mm: $result');
        expect(result, 'Jul.2025 14:30');
        expect(result.contains('时'), false);
      });
    });

    group('格式规则总结验证', () {
      test('时间单位添加规则总结', () {
        print('\n=== 时间单位添加规则总结 ===');
        
        final testCases = [
          {'format': 'H', 'shouldAdd': true, 'desc': '纯小时格式'},
          {'format': 'HH', 'shouldAdd': true, 'desc': '两位小时格式'},
          {'format': 'HH:mm', 'shouldAdd': false, 'desc': '小时:分钟格式'},
          {'format': 'HH:mm:ss', 'shouldAdd': false, 'desc': '小时:分钟:秒格式'},
          {'format': 'mm:ss', 'shouldAdd': false, 'desc': '分钟:秒格式'},
        ];

        for (final testCase in testCases) {
          final format = testCase['format'] as String;
          final shouldAdd = testCase['shouldAdd'] as bool;
          final desc = testCase['desc'] as String;
          
          print('${shouldAdd ? '✅' : '❌'} $format ($desc): ${shouldAdd ? '添加时间单位' : '不添加时间单位'}');
        }

        print('\n关键规则: 只有纯 H 或 HH 格式才添加时间单位');
        print('原因: ElementTimeFormat.H 专门指小时刻度显示');
      });
    });
  });
}
