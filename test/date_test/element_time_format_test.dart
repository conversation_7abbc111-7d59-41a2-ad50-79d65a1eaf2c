import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_template/utils/element_utils.dart';

void main() {
  group('ElementUtils.addHourUnitToTimeFormat 时间单位测试', () {
    test('ElementTimeFormat.H - 简体中文添加"时"单位', () {
      final result = ElementUtils.addHourUnitToTimeFormat('10', 'H');
      expect(result, '10时');
    });

    test('ElementTimeFormat.H - 繁体中文添加"時"单位', () {
      final result = ElementUtils.addHourUnitToTimeFormat('14', 'H');
      expect(result, '14時');
    });

    test('ElementTimeFormat.H - 韩语添加"시"单位', () {
      final result = ElementUtils.addHourUnitToTimeFormat('22', 'H');
      expect(result, '22시');
    });

    test('ElementTimeFormat.H - 日语添加"時"单位', () {
      final result = ElementUtils.addHourUnitToTimeFormat('08', 'H');
      expect(result, '08時');
    });

    test('ElementTimeFormat.H - 英语不添加时间单位', () {
      final result = ElementUtils.addHourUnitToTimeFormat('15', 'H');
      expect(result, '15');
    });
  });
}
