import 'package:flutter_test/flutter_test.dart';

void main() {
  group('小时提取安全性测试', () {
    group('原始方案的风险测试', () {
      test('原始正则 - 可能的错误匹配', () {
        final testCases = [
          {
            'input': '2:30',
            'expected_hour': '2',
            'desc': '正常情况'
          },
          {
            'input': '12:30',
            'expected_hour': '12',
            'desc': '两位数小时'
          },
          {
            'input': '10:25',
            'expected_hour': '10',
            'desc': '可能匹配到分钟25'
          },
          {
            'input': '1:59',
            'expected_hour': '1',
            'desc': '可能匹配到分钟59'
          },
        ];

        print('=== 原始方案风险分析 ===');
        for (final testCase in testCases) {
          final input = testCase['input'] as String;
          final expectedHour = testCase['expected_hour'] as String;
          final desc = testCase['desc'] as String;
          
          // 模拟原始的不安全正则
          final unsafeMatch = RegExp(r'(\d{1,2})').firstMatch(input);
          final unsafeResult = unsafeMatch?.group(1);
          
          print('$desc: "$input"');
          print('  期望匹配小时: $expectedHour');
          print('  原始正则匹配: $unsafeResult');
          print('  是否正确: ${unsafeResult == expectedHour}');
          print('');
        }
      });
    });

    group('改进方案的安全性测试', () {
      test('改进正则 - 精确匹配开头小时', () {
        final testCases = [
          {
            'input': '2:30',
            'expected_hour': '2',
            'desc': '正常情况'
          },
          {
            'input': '12:30',
            'expected_hour': '12',
            'desc': '两位数小时'
          },
          {
            'input': '10:25',
            'expected_hour': '10',
            'desc': '不会匹配到分钟25'
          },
          {
            'input': '1:59',
            'expected_hour': '1',
            'desc': '不会匹配到分钟59'
          },
          {
            'input': '9',
            'expected_hour': '9',
            'desc': '仅小时数字'
          },
          {
            'input': '23',
            'expected_hour': '23',
            'desc': '仅两位小时数字'
          },
        ];

        print('=== 改进方案安全性验证 ===');
        for (final testCase in testCases) {
          final input = testCase['input'] as String;
          final expectedHour = testCase['expected_hour'] as String;
          final desc = testCase['desc'] as String;
          
          // 使用改进的安全正则
          final safeMatch = RegExp(r'^(\d{1,2})(?=:|$)').firstMatch(input);
          final safeResult = safeMatch?.group(1);
          
          print('$desc: "$input"');
          print('  期望匹配小时: $expectedHour');
          print('  改进正则匹配: $safeResult');
          print('  是否正确: ${safeResult == expectedHour}');
          print('');
          
          expect(safeResult, expectedHour, reason: '改进正则应该正确匹配小时: $desc');
        }
      });
    });

    group('字符串替换安全性测试', () {
      test('原始替换 vs 改进替换', () {
        final testCases = [
          {
            'input': '2:30',
            'hour': '2',
            'unit': '时',
            'expected': '2时:30',
            'desc': '正常替换'
          },
          {
            'input': '12:12',
            'hour': '12',
            'unit': '时',
            'expected': '12时:12',
            'desc': '重复数字 - 只替换开头'
          },
          {
            'input': '10:10',
            'hour': '10',
            'unit': '时',
            'expected': '10时:10',
            'desc': '完全重复数字'
          },
          {
            'input': '1:11',
            'hour': '1',
            'unit': '时',
            'expected': '1时:11',
            'desc': '部分重复数字'
          },
        ];

        print('=== 字符串替换安全性验证 ===');
        for (final testCase in testCases) {
          final input = testCase['input'] as String;
          final hour = testCase['hour'] as String;
          final unit = testCase['unit'] as String;
          final expected = testCase['expected'] as String;
          final desc = testCase['desc'] as String;
          
          // 原始不安全替换
          final unsafeResult = input.replaceFirst(hour, hour + unit);
          
          // 改进的安全替换
          final safeResult = input.replaceFirst(RegExp(r'^\d{1,2}'), hour + unit);
          
          print('$desc: "$input"');
          print('  原始替换: $unsafeResult');
          print('  改进替换: $safeResult');
          print('  期望结果: $expected');
          print('  改进是否正确: ${safeResult == expected}');
          print('');
          
          expect(safeResult, expected, reason: '改进替换应该正确: $desc');
        }
      });
    });

    group('边界情况测试', () {
      test('特殊格式处理', () {
        final edgeCases = [
          {
            'input': '',
            'desc': '空字符串'
          },
          {
            'input': 'abc',
            'desc': '无数字字符串'
          },
          {
            'input': ':30',
            'desc': '缺少小时'
          },
          {
            'input': '100:30',
            'desc': '三位数字（超出预期）'
          },
        ];

        print('=== 边界情况测试 ===');
        for (final testCase in edgeCases) {
          final input = testCase['input'] as String;
          final desc = testCase['desc'] as String;
          
          final match = RegExp(r'^(\d{1,2})(?=:|$)').firstMatch(input);
          final result = match?.group(1);
          
          print('$desc: "$input"');
          print('  匹配结果: $result');
          print('  是否安全: ${result == null ? '是（无匹配）' : '是（有匹配）'}');
          print('');
        }
      });
    });
  });

  group('实际应用场景模拟', () {
    test('12小时制时间格式模拟', () {
      final scenarios = [
        {
          'original': '2:30 PM',
          'afterAmPmReverse': '下午2:30',
          'expected': '下午2时:30',
          'desc': '下午时间'
        },
        {
          'original': '10:15 AM',
          'afterAmPmReverse': '上午10:15',
          'expected': '上午10时:15',
          'desc': '上午时间'
        },
        {
          'original': '12:00 PM',
          'afterAmPmReverse': '下午12:00',
          'expected': '下午12时:00',
          'desc': '正午时间'
        },
      ];

      print('=== 实际应用场景模拟 ===');
      for (final scenario in scenarios) {
        final afterReverse = scenario['afterAmPmReverse'] as String;
        final expected = scenario['expected'] as String;
        final desc = scenario['desc'] as String;
        
        // 模拟改进后的处理逻辑
        final hourMatch = RegExp(r'^(\d{1,2})(?=:|$)').firstMatch(afterReverse);
        String result = afterReverse;
        
        if (hourMatch != null) {
          final hourValue = hourMatch.group(1)!;
          final hourWithUnit = hourValue + '时'; // 模拟 addHourUnitToTimeFormat
          result = afterReverse.replaceFirst(RegExp(r'^\d{1,2}'), hourWithUnit);
        }
        
        print('$desc:');
        print('  AM/PM调整后: $afterReverse');
        print('  添加时间单位: $result');
        print('  期望结果: $expected');
        print('  是否正确: ${result == expected}');
        print('');
        
        expect(result, expected, reason: '实际场景应该正确处理: $desc');
      }
    });
  });
}
