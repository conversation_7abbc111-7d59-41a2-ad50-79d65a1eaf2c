import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_template/utils/element_utils.dart';

void main() {
  group('英语日期格式处理测试', () {
    final testDateTime = DateTime(2025, 7, 15, 14, 30, 45); // 2025年7月15日 14:30:45

    test('_processSpecialEnglishDateFormat - MMMM yyyy 格式', () {
      final result = ElementUtils.processSpecialEnglishDateFormat('MMMM yyyy', testDateTime);
      expect(result, 'July2025');
    });

    test('_processSpecialEnglishDateFormat - MMM.yyyy 格式', () {
      final result = ElementUtils.processSpecialEnglishDateFormat('MMM.yyyy', testDateTime);
      expect(result, 'Jul.2025');
    });

    test('_processSpecialEnglishDateFormat - 非特殊格式返回null', () {
      final result = ElementUtils.processSpecialEnglishDateFormat('yyyy-MM-dd', testDateTime);
      expect(result, null);
    });

    test('buildDateValue - MMMM yyyy 格式（仅日期）', () {
      final result = ElementUtils.buildDateValue(
        time: testDateTime.millisecondsSinceEpoch,
        dateFormat: 'MMMM yyyy',
        militaryTime: true,
      );
      expect(result, 'July2025');
    });

    test('buildDateValue - MMM.yyyy 格式（仅日期）', () {
      final result = ElementUtils.buildDateValue(
        time: testDateTime.millisecondsSinceEpoch,
        dateFormat: 'MMM.yyyy',
        militaryTime: true,
      );
      expect(result, 'Jul.2025');
    });

    test('buildDateValue - MMMM yyyy + HH 格式（日期+时间）', () {
      final result = ElementUtils.buildDateValue(
        time: testDateTime.millisecondsSinceEpoch,
        dateFormat: 'MMMM yyyy',
        timeFormat: 'HH',
        militaryTime: true,
      );
      expect(result, 'July2025 14');
    });

    test('buildDateValue - MMM.yyyy + HH:mm 格式（日期+时间）', () {
      final result = ElementUtils.buildDateValue(
        time: testDateTime.millisecondsSinceEpoch,
        dateFormat: 'MMM.yyyy',
        timeFormat: 'HH:mm',
        militaryTime: true,
      );
      expect(result, 'Jul.2025 14:30');
    });

    test('buildDateValue - MMMM yyyy + HH:mm:ss 格式（日期+时间）', () {
      final result = ElementUtils.buildDateValue(
        time: testDateTime.millisecondsSinceEpoch,
        dateFormat: 'MMMM yyyy',
        timeFormat: 'HH:mm:ss',
        militaryTime: true,
      );
      expect(result, 'July2025 14:30:45');
    });

    test('buildDateValue - 带前缀的特殊格式', () {
      final result = ElementUtils.buildDateValue(
        time: testDateTime.millisecondsSinceEpoch,
        dateFormat: 'MMMM yyyy',
        timeFormat: 'HH',
        militaryTime: true,
        prefix: 'Date:',
      );
      expect(result.contains('Date:'), true);
      expect(result.contains('July2025'), true);
      expect(result.contains('14'), true);
    });

    test('buildDateValue - 12小时制与特殊日期格式', () {
      final result = ElementUtils.buildDateValue(
        time: testDateTime.millisecondsSinceEpoch,
        dateFormat: 'MMM.yyyy',
        timeFormat: 'HH',
        militaryTime: false, // 12小时制
      );
      expect(result.contains('Jul.2025'), true);
      // 12小时制应该包含AM/PM信息
    });
  });

  group('标准日期格式兼容性测试', () {
    final testDateTime = DateTime(2025, 7, 15, 14, 30, 45);

    test('buildDateValue - 标准格式不受影响', () {
      final result = ElementUtils.buildDateValue(
        time: testDateTime.millisecondsSinceEpoch,
        dateFormat: 'yyyy-MM-dd',
        timeFormat: 'HH:mm',
        militaryTime: true,
      );
      expect(result, '2025-07-15 14:30');
    });

    test('buildDateValue - 中文格式不受影响', () {
      final result = ElementUtils.buildDateValue(
        time: testDateTime.millisecondsSinceEpoch,
        dateFormat: 'yyyy年MM月dd日',
        timeFormat: 'HH',
        militaryTime: true,
      );
      expect(result.contains('2025年07月15日'), true);
      expect(result.contains('14'), true);
    });
  });
}
