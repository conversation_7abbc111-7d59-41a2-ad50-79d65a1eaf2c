import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_template/utils/element_utils.dart';

void main() {
  group('优化后的 buildDateValue 功能测试', () {
    final testDateTime = DateTime(2025, 7, 15, 14, 30, 45); // 2025年7月15日 14:30:45

    group('基础功能验证', () {
      test('仅日期格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy-MM-dd',
          militaryTime: true,
        );
        expect(result, '2025-07-15');
      });

      test('仅时间格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'HH:mm',
          militaryTime: true,
        );
        expect(result, '14:30');
      });

      test('日期 + 时间格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy-MM-dd',
          timeFormat: 'HH:mm:ss',
          militaryTime: true,
        );
        expect(result, '2025-07-15 14:30:45');
      });
    });

    group('特殊英语日期格式', () {
      test('MMMM yyyy 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMMM yyyy',
          militaryTime: true,
        );
        expect(result, 'July2025');
      });

      test('MMM.yyyy 格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMM.yyyy',
          militaryTime: true,
        );
        expect(result, 'Jul.2025');
      });

      test('MMMM yyyy + 时间格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMMM yyyy',
          timeFormat: 'HH:mm',
          militaryTime: true,
        );
        expect(result, 'July2025 14:30');
      });

      test('MMM.yyyy + 时间格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMM.yyyy',
          timeFormat: 'HH',
          militaryTime: true,
        );
        expect(result, 'Jul.2025 14');
      });
    });

    group('12小时制处理', () {
      test('标准格式 + 12小时制', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy-MM-dd',
          timeFormat: 'HH',
          militaryTime: false,
        );
        expect(result.contains('2025-07-15'), true);
        // 12小时制应该包含AM/PM信息
      });

      test('特殊英语格式 + 12小时制', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMMM yyyy',
          timeFormat: 'HH',
          militaryTime: false,
        );
        expect(result.contains('July2025'), true);
        // 12小时制应该包含AM/PM信息
      });
    });

    group('前缀处理', () {
      test('带前缀的日期', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy-MM-dd',
          militaryTime: true,
          prefix: 'Date',
        );
        expect(result.contains('Date'), true);
        expect(result.contains('2025-07-15'), true);
      });

      test('带前缀的特殊英语格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMMM yyyy',
          timeFormat: 'HH:mm',
          militaryTime: true,
          prefix: 'Generated',
        );
        expect(result.contains('Generated'), true);
        expect(result.contains('July2025'), true);
        expect(result.contains('14:30'), true);
      });
    });

    group('辅助方法测试', () {
      test('_getFormattedDate - 标准格式', () {
        final result = ElementUtils.getFormattedDate('yyyy-MM-dd', testDateTime, false);
        expect(result, '2025-07-15');
      });

      test('_getFormattedDate - 特殊英语格式', () {
        final result1 = ElementUtils.getFormattedDate('MMMM yyyy', testDateTime, false);
        expect(result1, 'July2025');

        final result2 = ElementUtils.getFormattedDate('MMM.yyyy', testDateTime, false);
        expect(result2, 'Jul.2025');
      });

      test('_getFormattedDate - null 格式', () {
        final result = ElementUtils.getFormattedDate(null, testDateTime, false);
        expect(result, '');
      });

      test('_addTimeUnitIfNeeded - CJK语言', () {
        final result1 = ElementUtils.addTimeUnitIfNeeded('14', 'HH', 'zh');
        expect(result1, '14时');

        final result2 = ElementUtils.addTimeUnitIfNeeded('14', 'HH', 'zh_Hant');
        expect(result2, '14時');

        final result3 = ElementUtils.addTimeUnitIfNeeded('14', 'HH', 'ko');
        expect(result3, '14시');

        final result4 = ElementUtils.addTimeUnitIfNeeded('14', 'HH', 'ja');
        expect(result4, '14時');
      });

      test('_addTimeUnitIfNeeded - 非CJK语言', () {
        final result = ElementUtils.addTimeUnitIfNeeded('14', 'HH', 'en');
        expect(result, '14');
      });

      test('_combineDateTime - 各种组合', () {
        final result1 = ElementUtils.combineDateTime(null, '：', '2025-07-15', '14:30');
        expect(result1, '2025-07-15 14:30');

        final result2 = ElementUtils.combineDateTime('Date', '：', '2025-07-15', '');
        expect(result2, 'Date：2025-07-15');

        final result3 = ElementUtils.combineDateTime('Time', '：', '', '14:30');
        expect(result3, 'Time：14:30');

        final result4 = ElementUtils.combineDateTime(null, '：', 'July2025', '14时');
        expect(result4, 'July2025 14时');
      });
    });

    group('边界情况', () {
      test('所有参数为null', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          militaryTime: true,
        );
        // 应该返回空字符串或默认值
        expect(result, '');
      });

      test('空字符串前缀', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy-MM-dd',
          militaryTime: true,
          prefix: '',
        );
        expect(result, '2025-07-15');
      });
    });
  });
}
