import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_template/utils/element_utils.dart';

void main() {
  group('ElementUtils.buildDateValue 时间单位测试', () {
    test('简体中文 - 12小时制添加"时"单位', () {
      // 模拟简体中文环境
      // 注意：实际测试中需要设置正确的locale
      final result = ElementUtils.buildDateValue(
        time: DateTime(2025, 1, 1, 10, 30, 45).millisecondsSinceEpoch,
        timeFormat: 'HH',
        militaryTime: false,
      );
      
      // 期望结果包含"时"单位
      expect(result.contains('时'), true);
    });

    test('繁体中文 - 24小时制添加"時"单位', () {
      final result = ElementUtils.buildDateValue(
        time: DateTime(2025, 1, 1, 14, 30).millisecondsSinceEpoch,
        timeFormat: 'HH',
        militaryTime: true,
      );
      
      // 期望结果包含"時"单位（繁体）
      expect(result.contains('時'), true);
    });

    test('韩语 - 添加"시"单位', () {
      final result = ElementUtils.buildDateValue(
        time: DateTime(2025, 1, 1, 10).millisecondsSinceEpoch,
        timeFormat: 'HH',
        militaryTime: true,
      );
      
      // 期望结果包含"시"单位
      expect(result.contains('시'), true);
    });

    test('日语 - 添加"時"单位', () {
      final result = ElementUtils.buildDateValue(
        time: DateTime(2025, 1, 1, 22).millisecondsSinceEpoch,
        timeFormat: 'HH',
        militaryTime: true,
      );
      
      // 期望结果包含"時"单位
      expect(result.contains('時'), true);
    });

    test('英语 - 不添加时间单位', () {
      final result = ElementUtils.buildDateValue(
        time: DateTime(2025, 1, 1, 10).millisecondsSinceEpoch,
        timeFormat: 'HH',
        militaryTime: true,
      );
      
      // 期望结果不包含CJK时间单位
      expect(result.contains('时'), false);
      expect(result.contains('時'), false);
      expect(result.contains('시'), false);
    });
  });
}
