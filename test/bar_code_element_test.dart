import 'package:flutter_test/flutter_test.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';

void main() {
  test('测试元素值超长', () {
    final barcode = BarCodeElement(
        codeType: NetalBarcodeType.CODEBAR,
        value:
            '1234567891011121314151617181920212223242526272829303132333435363738394041424344454647484950');
    expect(barcode.value?.length, 57);
  });
}
