import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_template/utils/element_utils.dart';

void main() {
  group('完整的日期时间格式处理测试', () {
    final testDateTime = DateTime(2025, 7, 15, 14, 30, 45); // 2025年7月15日 14:30:45

    group('特殊英语日期格式 (specialDateFormat != null)', () {
      test('MMMM yyyy + HH 格式（24小时制）', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMMM yyyy',
          timeFormat: 'HH',
          militaryTime: true,
        );
        expect(result, 'July2025 14');
      });

      test('MMM.yyyy + HH:mm 格式（24小时制）', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMM.yyyy',
          timeFormat: 'HH:mm',
          militaryTime: true,
        );
        expect(result, 'Jul.2025 14:30');
      });

      test('MMMM yyyy + HH:mm:ss 格式（24小时制）', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMMM yyyy',
          timeFormat: 'HH:mm:ss',
          militaryTime: true,
        );
        expect(result, 'July2025 14:30:45');
      });
    });

    group('标准日期格式 (specialDateFormat == null)', () {
      test('标准格式 + HH 格式（24小时制）', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy-MM-dd',
          timeFormat: 'HH',
          militaryTime: true,
        );
        expect(result, '2025-07-15 14');
      });

      test('标准格式 + HH:mm 格式（24小时制）', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy-MM-dd',
          timeFormat: 'HH:mm',
          militaryTime: true,
        );
        expect(result, '2025-07-15 14:30');
      });

      test('中文格式 + HH 格式（24小时制）- 应该添加时间单位', () {
        // 注意：这个测试需要模拟中文语言环境
        // 在实际测试中，需要设置正确的locale
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy年MM月dd日',
          timeFormat: 'HH',
          militaryTime: true,
        );
        // 期望结果应该包含中文日期和时间单位
        expect(result.contains('2025年07月15日'), true);
        expect(result.contains('14'), true);
      });
    });

    group('CJK语言时间单位处理', () {
      test('addHourUnitToTimeFormat - 简体中文', () {
        final result = ElementUtils.addHourUnitToTimeFormat('14', 'HH');
        expect(result, '14时');
      });

      test('addHourUnitToTimeFormat - 繁体中文', () {
        final result = ElementUtils.addHourUnitToTimeFormat('14', 'HH');
        expect(result, '14時');
      });

      test('addHourUnitToTimeFormat - 韩语', () {
        final result = ElementUtils.addHourUnitToTimeFormat('14', 'HH');
        expect(result, '14시');
      });

      test('addHourUnitToTimeFormat - 日语', () {
        final result = ElementUtils.addHourUnitToTimeFormat('14', 'HH');
        expect(result, '14時');
      });

      test('addHourUnitToTimeFormat - 英语（不添加单位）', () {
        final result = ElementUtils.addHourUnitToTimeFormat('14', 'HH');
        expect(result, '14');
      });

      test('addHourUnitToTimeFormat - HH:mm 格式', () {
        final result = ElementUtils.addHourUnitToTimeFormat('14:30', 'HH:mm');
        expect(result, '14时30分');
      });

      test('addHourUnitToTimeFormat - HH:mm:ss 格式', () {
        final result = ElementUtils.addHourUnitToTimeFormat('14:30:45', 'HH:mm:ss');
        expect(result, '14时30分45秒');
      });
    });

    group('12小时制处理', () {
      test('特殊英语格式 + 12小时制', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMMM yyyy',
          timeFormat: 'HH',
          militaryTime: false, // 12小时制
        );
        expect(result.contains('July2025'), true);
        // 12小时制应该包含AM/PM信息
      });

      test('标准格式 + 12小时制', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy-MM-dd',
          timeFormat: 'HH',
          militaryTime: false, // 12小时制
        );
        expect(result.contains('2025-07-15'), true);
        // 12小时制应该包含AM/PM信息
      });
    });

    group('边界情况测试', () {
      test('仅特殊日期格式（无时间）', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMMM yyyy',
          militaryTime: true,
        );
        expect(result, 'July2025');
      });

      test('仅标准日期格式（无时间）', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'yyyy-MM-dd',
          militaryTime: true,
        );
        expect(result, '2025-07-15');
      });

      test('仅时间格式（无日期）', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          timeFormat: 'HH:mm',
          militaryTime: true,
        );
        expect(result, '14:30');
      });

      test('带前缀的完整格式', () {
        final result = ElementUtils.buildDateValue(
          time: testDateTime.millisecondsSinceEpoch,
          dateFormat: 'MMMM yyyy',
          timeFormat: 'HH:mm',
          militaryTime: true,
          prefix: 'DateTime',
        );
        expect(result.contains('DateTime'), true);
        expect(result.contains('July2025'), true);
        expect(result.contains('14:30'), true);
      });
    });
  });
}
