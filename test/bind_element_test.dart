import 'package:flutter_test/flutter_test.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:niimbot_template/models/elements/text_element.dart';

void main() {
  test('测试copy null value', () {
    final element = TextElement(
        width: 0, height: 0, value: '10', dataBind: const ['1', '2']);
    final copyElement =
        element.copyWith(dataBind: const CopyWrapper.value(null));
    expect(copyElement.dataBind, null);
  });
}
