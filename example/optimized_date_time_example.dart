import 'package:niimbot_template/utils/element_utils.dart';

/// 优化后的日期时间格式处理示例
/// 展示简化后的 buildDateValue 逻辑
void main() {
  print('=== 优化后的日期时间格式处理示例 ===\n');

  // 测试时间：2025年7月15日 14:30:45
  final testTime = DateTime(2025, 7, 15, 14, 30, 45).millisecondsSinceEpoch;

  print('测试时间: 2025年7月15日 14:30:45\n');

  // 1. 基础功能测试
  print('=== 基础功能测试 ===');
  
  // 仅日期
  final dateOnly = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'yyyy-MM-dd',
    militaryTime: true,
  );
  print('仅日期: $dateOnly');

  // 仅时间
  final timeOnly = ElementUtils.buildDateValue(
    time: testTime,
    timeFormat: 'HH:mm',
    militaryTime: true,
  );
  print('仅时间: $timeOnly');

  // 日期 + 时间
  final dateTime = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'yyyy-MM-dd',
    timeFormat: 'HH:mm:ss',
    militaryTime: true,
  );
  print('日期 + 时间: $dateTime');

  // 2. 特殊英语日期格式测试
  print('\n=== 特殊英语日期格式测试 ===');
  
  // MMMM yyyy 格式
  final july2025 = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMMM yyyy',
    militaryTime: true,
  );
  print('MMMM yyyy: $july2025');

  // MMM.yyyy 格式
  final julDot2025 = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMM.yyyy',
    militaryTime: true,
  );
  print('MMM.yyyy: $julDot2025');

  // 特殊格式 + 时间
  final july2025WithTime = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMMM yyyy',
    timeFormat: 'HH:mm',
    militaryTime: true,
  );
  print('MMMM yyyy + 时间: $july2025WithTime');

  // 3. ElementTimeFormat.H 模式测试
  print('\n=== ElementTimeFormat.H 模式测试 ===');
  
  // 24小时制 + HH 格式（会添加时间单位）
  final hour24 = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'yyyy-MM-dd',
    timeFormat: 'HH',
    militaryTime: true,
  );
  print('24小时制 + HH: $hour24');

  // 特殊英语格式 + HH 格式
  final englishHour = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMMM yyyy',
    timeFormat: 'HH',
    militaryTime: true,
  );
  print('特殊英语格式 + HH: $englishHour');

  // 4. 12小时制测试
  print('\n=== 12小时制测试 ===');
  
  // 上午时间
  final morningTime = DateTime(2025, 7, 15, 9, 30).millisecondsSinceEpoch;
  final morning12h = ElementUtils.buildDateValue(
    time: morningTime,
    dateFormat: 'yyyy-MM-dd',
    timeFormat: 'HH',
    militaryTime: false,
  );
  print('上午12小时制: $morning12h');

  // 下午时间
  final afternoon12h = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMMM yyyy',
    timeFormat: 'HH',
    militaryTime: false,
  );
  print('下午12小时制: $afternoon12h');

  // 5. 前缀测试
  print('\n=== 前缀测试 ===');
  
  final withPrefix = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMM.yyyy',
    timeFormat: 'HH:mm',
    militaryTime: true,
    prefix: 'Generated',
  );
  print('带前缀: $withPrefix');

  // 6. addHourUnitToTimeFormat 直接测试
  print('\n=== addHourUnitToTimeFormat 直接测试 ===');
  
  // HH 格式（会添加单位）
  final hourWithUnit = ElementUtils.addHourUnitToTimeFormat('14', 'HH');
  print('HH 格式: 14 -> $hourWithUnit');

  // H 格式（会添加单位）
  final hourWithUnitH = ElementUtils.addHourUnitToTimeFormat('9', 'H');
  print('H 格式: 9 -> $hourWithUnitH');

  // HH:mm 格式（不会添加单位）
  final hourMinute = ElementUtils.addHourUnitToTimeFormat('14:30', 'HH:mm');
  print('HH:mm 格式: 14:30 -> $hourMinute');

  // mm:ss 格式（不会添加单位）
  final minuteSecond = ElementUtils.addHourUnitToTimeFormat('30:45', 'mm:ss');
  print('mm:ss 格式: 30:45 -> $minuteSecond');

  // 7. 不同月份测试
  print('\n=== 不同月份测试 ===');
  
  final months = [1, 3, 6, 9, 12];
  for (final month in months) {
    final monthTime = DateTime(2025, month, 1).millisecondsSinceEpoch;
    
    final fullMonth = ElementUtils.buildDateValue(
      time: monthTime,
      dateFormat: 'MMMM yyyy',
      militaryTime: true,
    );
    
    final abbrMonth = ElementUtils.buildDateValue(
      time: monthTime,
      dateFormat: 'MMM.yyyy',
      militaryTime: true,
    );
    
    print('${month}月: $fullMonth | $abbrMonth');
  }

  // 8. 边界情况测试
  print('\n=== 边界情况测试 ===');
  
  // 所有参数为默认值
  final minimal = ElementUtils.buildDateValue(
    time: testTime,
    militaryTime: true,
  );
  print('最小参数: "$minimal"');

  // 空前缀
  final emptyPrefix = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'yyyy-MM-dd',
    militaryTime: true,
    prefix: '',
  );
  print('空前缀: $emptyPrefix');

  print('\n=== 优化验证完成 ===');
  print('✅ 代码逻辑简化，功能保持完整');
  print('✅ addHourUnitToTimeFormat 调用简化（无需lang参数）');
  print('✅ 特殊英语日期格式正常工作');
  print('✅ CJK语言时间单位自动添加');
}
