// 语言特定的时间格式演示
// 展示英文和CJK语言在24小时制下的不同处理

/// 语言特定的时间格式演示
void main() {
  print('=== 语言特定的时间格式演示 ===\n');

  print('=== 问题描述 ===');
  print('❌ 修复前: 英文24小时制也会添加时间单位');
  print('   例如: "14" 变成 "14时"');
  print('✅ 修复后: 只有CJK语言才添加时间单位');
  print('   英文: "14" 保持 "14"');
  print('   中文: "14" 变成 "14时"');

  print('\n=== 修复的关键代码 ===');
  print('修复前:');
  print('  String timeFormatVal = DateFormat(timeFormat, isZh ? \'zh_CN\' : null).format(dateTime);');
  print('  return addHourUnitToTimeFormat(timeFormatVal, timeFormat); // ❌ 对所有语言都添加');
  
  print('\n修复后:');
  print('  String timeFormatVal = DateFormat(timeFormat, isZh ? \'zh_CN\' : null).format(dateTime);');
  print('  // 只为CJK语言添加时间单位');
  print('  if ([\'zh\', \'zh_Hant\', \'ko\', \'ja\'].contains(lang)) {');
  print('    timeFormatVal = addHourUnitToTimeFormat(timeFormatVal, timeFormat);');
  print('  }');
  print('  return timeFormatVal;');

  print('\n=== 测试用例演示 ===');
  
  final testCases = [
    {
      'lang': 'en',
      'name': '英文',
      'shouldAddUnit': false,
      'expectedUnit': '',
    },
    {
      'lang': 'zh',
      'name': '简体中文',
      'shouldAddUnit': true,
      'expectedUnit': '时',
    },
    {
      'lang': 'zh_Hant',
      'name': '繁体中文',
      'shouldAddUnit': true,
      'expectedUnit': '時',
    },
    {
      'lang': 'ko',
      'name': '韩语',
      'shouldAddUnit': true,
      'expectedUnit': '시',
    },
    {
      'lang': 'ja',
      'name': '日语',
      'shouldAddUnit': true,
      'expectedUnit': '時',
    },
  ];

  print('\n24小时制 + HH 格式处理结果:');
  for (final testCase in testCases) {
    final lang = testCase['lang'] as String;
    final name = testCase['name'] as String;
    final shouldAdd = testCase['shouldAddUnit'] as bool;
    final unit = testCase['expectedUnit'] as String;
    
    print('\n$name ($lang):');
    print('  输入: timeFormat="HH", timeFormatVal="14"');
    
    if (shouldAdd) {
      print('  处理: 检测到CJK语言，添加时间单位');
      print('  输出: "14$unit"');
    } else {
      print('  处理: 非CJK语言，不添加时间单位');
      print('  输出: "14"');
    }
  }

  print('\n=== 实际应用场景 ===');
  
  final scenarios = [
    {
      'desc': '仅时间格式',
      'input': 'timeFormat: "HH", militaryTime: true',
      'english': '14',
      'chinese': '14时',
    },
    {
      'desc': '日期+时间格式',
      'input': 'dateFormat: "yyyy-MM-dd", timeFormat: "HH", militaryTime: true',
      'english': '2025-07-15 14',
      'chinese': '2025-07-15 14时',
    },
    {
      'desc': '特殊英语日期+时间',
      'input': 'dateFormat: "MMMM yyyy", timeFormat: "HH", militaryTime: true',
      'english': 'July2025 14',
      'chinese': 'July2025 14时',
    },
    {
      'desc': 'HH:mm格式（不添加单位）',
      'input': 'timeFormat: "HH:mm", militaryTime: true',
      'english': '14:30',
      'chinese': '14:30',
    },
  ];

  for (final scenario in scenarios) {
    print('\n${scenario['desc']}:');
    print('  参数: ${scenario['input']}');
    print('  英文环境: ${scenario['english']}');
    print('  中文环境: ${scenario['chinese']}');
  }

  print('\n=== 语言检查逻辑 ===');
  print('CJK语言检查: [\'zh\', \'zh_Hant\', \'ko\', \'ja\'].contains(lang)');
  print('- zh: 简体中文');
  print('- zh_Hant: 繁体中文');
  print('- ko: 韩语');
  print('- ja: 日语');
  print('- 其他语言(如en): 不添加时间单位');

  print('\n=== addHourUnitToTimeFormat 行为 ===');
  print('只有当 timeFormat == "H" || timeFormat == "HH" 时才添加单位');
  print('其他格式(HH:mm, mm:ss, HH:mm:ss)不添加单位');

  print('\n=== 修复验证 ===');
  print('✅ 英文24小时制: 不添加时间单位');
  print('✅ CJK语言24小时制: 添加对应的时间单位');
  print('✅ 12小时制: 按语言环境处理');
  print('✅ 非H/HH格式: 所有语言都不添加单位');

  print('\n=== 演示完成 ===');
}
