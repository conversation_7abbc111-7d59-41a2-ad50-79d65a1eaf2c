import 'package:niimbot_template/utils/element_utils.dart';

/// 演示英语日期格式 MMMM yyyy 和 MMM.yyyy 的处理
void main() {
  print('=== 英语日期格式处理示例 ===\n');

  // 测试时间：2025年7月15日 14:30:45
  final testTime = DateTime(2025, 7, 15, 14, 30, 45).millisecondsSinceEpoch;

  print('测试时间: 2025年7月15日 14:30:45\n');

  // 1. MMMM yyyy 格式（July2025）
  print('=== MMMM yyyy 格式测试 ===');
  
  // 仅日期
  final result1 = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMMM yyyy',
    militaryTime: true,
  );
  print('仅日期: $result1');

  // 日期 + 小时
  final result2 = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMMM yyyy',
    timeFormat: 'HH',
    militaryTime: true,
  );
  print('日期 + 小时: $result2');

  // 日期 + 小时:分钟
  final result3 = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMMM yyyy',
    timeFormat: 'HH:mm',
    militaryTime: true,
  );
  print('日期 + 小时:分钟: $result3');

  // 日期 + 小时:分钟:秒
  final result4 = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMMM yyyy',
    timeFormat: 'HH:mm:ss',
    militaryTime: true,
  );
  print('日期 + 小时:分钟:秒: $result4');

  print('\n=== MMM.yyyy 格式测试 ===');

  // 2. MMM.yyyy 格式（Jul.2025）
  
  // 仅日期
  final result5 = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMM.yyyy',
    militaryTime: true,
  );
  print('仅日期: $result5');

  // 日期 + 小时
  final result6 = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMM.yyyy',
    timeFormat: 'HH',
    militaryTime: true,
  );
  print('日期 + 小时: $result6');

  // 日期 + 小时:分钟
  final result7 = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMM.yyyy',
    timeFormat: 'HH:mm',
    militaryTime: true,
  );
  print('日期 + 小时:分钟: $result7');

  print('\n=== 12小时制测试 ===');

  // 3. 12小时制测试
  final result8 = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMMM yyyy',
    timeFormat: 'HH',
    militaryTime: false, // 12小时制
  );
  print('MMMM yyyy + 12小时制: $result8');

  final result9 = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMM.yyyy',
    timeFormat: 'HH:mm',
    militaryTime: false, // 12小时制
  );
  print('MMM.yyyy + 12小时制: $result9');

  print('\n=== 带前缀测试 ===');

  // 4. 带前缀测试
  final result10 = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'MMMM yyyy',
    timeFormat: 'HH:mm',
    militaryTime: true,
    prefix: 'Date',
  );
  print('带前缀: $result10');

  print('\n=== 特殊格式处理方法测试 ===');

  // 5. 直接测试特殊格式处理方法
  final specialResult1 = ElementUtils.processSpecialEnglishDateFormat(
    'MMMM yyyy', 
    DateTime(2025, 7, 15)
  );
  print('processSpecialEnglishDateFormat(MMMM yyyy): $specialResult1');

  final specialResult2 = ElementUtils.processSpecialEnglishDateFormat(
    'MMM.yyyy', 
    DateTime(2025, 7, 15)
  );
  print('processSpecialEnglishDateFormat(MMM.yyyy): $specialResult2');

  final specialResult3 = ElementUtils.processSpecialEnglishDateFormat(
    'yyyy-MM-dd', 
    DateTime(2025, 7, 15)
  );
  print('processSpecialEnglishDateFormat(标准格式): $specialResult3');

  print('\n=== 兼容性测试 ===');

  // 6. 确保标准格式不受影响
  final standardResult = ElementUtils.buildDateValue(
    time: testTime,
    dateFormat: 'yyyy-MM-dd',
    timeFormat: 'HH:mm:ss',
    militaryTime: true,
  );
  print('标准格式不受影响: $standardResult');

  print('\n=== 不同月份测试 ===');

  // 7. 测试不同月份
  final months = [1, 3, 6, 9, 12];
  for (final month in months) {
    final monthTime = DateTime(2025, month, 1).millisecondsSinceEpoch;
    final fullMonth = ElementUtils.buildDateValue(
      time: monthTime,
      dateFormat: 'MMMM yyyy',
      militaryTime: true,
    );
    final abbrMonth = ElementUtils.buildDateValue(
      time: monthTime,
      dateFormat: 'MMM.yyyy',
      militaryTime: true,
    );
    print('${month}月: $fullMonth | $abbrMonth');
  }
}
