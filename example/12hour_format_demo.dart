// 12小时制时间单位添加演示
// 展示修复后的12小时制逻辑

/// 12小时制时间单位添加演示
void main() {
  print('=== 12小时制时间单位添加演示 ===\n');

  // 模拟12小时制处理逻辑
  final testCases = [
    {
      'time': '9:30 AM',
      'hour': '9',
      'format': 'HH',
      'desc': '上午9点'
    },
    {
      'time': '2:30 PM', 
      'hour': '2',
      'format': 'HH',
      'desc': '下午2点'
    },
    {
      'time': '11:45 PM',
      'hour': '11', 
      'format': 'H',
      'desc': '晚上11点'
    },
    {
      'time': '12:00 AM',
      'hour': '12',
      'format': 'HH', 
      'desc': '午夜12点'
    },
  ];

  print('=== 修复前的问题 ===');
  print('❌ 12小时制时，addHourUnitToTimeFormat 接收到的是完整的时间字符串（如"下午2:30"）');
  print('❌ 但 addHourUnitToTimeFormat 只检查 timeFormat 是否为 "H" 或 "HH"');
  print('❌ 导致无法正确提取小时数字并添加单位');

  print('\n=== 修复后的逻辑 ===');
  print('✅ 在CJK语言环境下，专门处理12小时制的时间单位添加');
  print('✅ 使用正则表达式提取小时数字部分');
  print('✅ 只对 H 或 HH 格式添加时间单位');
  print('✅ 保持AM/PM标识的正确位置');

  print('\n=== 处理流程演示 ===');
  
  for (final testCase in testCases) {
    print('\n${testCase['desc']}:');
    print('  1. 原始时间: ${testCase['time']}');
    print('  2. 格式: ${testCase['format']}');
    
    // 模拟CJK语言环境下的处理
    if (testCase['format'] == 'HH' || testCase['format'] == 'H') {
      final hourValue = testCase['hour'] as String;
      final hourWithUnit = addHourUnitToTimeFormat(hourValue, testCase['format'] as String);
      final processedTime = (testCase['time'] as String).replaceFirst(hourValue, hourWithUnit);
      
      print('  3. 提取小时: $hourValue');
      print('  4. 添加单位: $hourWithUnit');
      print('  5. 最终结果: $processedTime');
    } else {
      print('  3. 非H/HH格式，不添加单位');
      print('  4. 最终结果: ${testCase['time']}');
    }
  }

  print('\n=== 关键修复点 ===');
  print('1. 正则表达式提取: RegExp(r\'(\\d{1,2})\').firstMatch(timeFormatVal)');
  print('2. 条件检查: if (timeFormat == \'HH\' || timeFormat == \'H\')');
  print('3. 字符串替换: timeFormatVal.replaceFirst(hourValue, hourWithUnit)');
  print('4. 只在CJK语言环境下执行: if (beforeAmPm)');

  print('\n=== 测试用例 ===');
  print('输入: timeFormat="HH", 12小时制, CJK语言');
  print('期望: 时间值包含对应语言的时间单位');
  print('简中: 2时下午 或 下午2时');
  print('繁中: 2時下午 或 下午2時'); 
  print('韩语: 2시오후 或 오후2시');
  print('日语: 2時午後 或 午後2時');

  print('\n=== 演示完成 ===');
}

/// 模拟时间单位添加逻辑
String addHourUnitToTimeFormat(String timeFormatVal, String timeFormat) {
  bool isAddUnit = timeFormat == 'H' || timeFormat == 'HH';
  if (!isAddUnit) return timeFormatVal;
  
  // 模拟本地化的时间单位
  final hourUnit = '时'; // 实际会根据语言环境变化
  return timeFormatVal + hourUnit;
}
