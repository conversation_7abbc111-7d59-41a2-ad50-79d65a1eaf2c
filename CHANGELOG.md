## 2025/8/4 [0.4.8+3]
- [feature] 更新:关联时间计算优化

## 2025/8/1 [0.4.8+1&0.4.8+2]
- [feature] 修复:接入新图像库、新增横排90°显示

## 2025/7/29 [0.4.7+12]
- [feature] 修复:优化保存多图

## 2025/7/28 [0.4.7+10&0.4.7+11]
- [feature] 修复:同步云端模板到本地预览图无需生成

## 2025/7/24 [0.4.7+9]

- [feature] 更新:多图相关的问题修复

## 2025/7/23 [0.4.7+8]

- [feature] 更新:多图相关的问题修复

## 2025/7/15 [0.4.7+6&0.4.7+7]

- [feature] 更新:多图保存处理
- [feature] 更新:多图尺寸不一渲染端上处理

## 2025/7/15 [0.4.7+4&0.4.7+5]

- [feature] 修复:c1自动长度预览/打印问题修复
- [feature] 更新:画板图片类元素裁剪优化

## 2025/7/8 [0.4.7+2&0.4.7+3]

- [feature] 更新: 兼容红黑碳带打印预览

## 2025/7/7 [0.4.6]

- [feature] 更新:新增时间格式

## 2025/6/27 [0.4.5]

- [feature] 更新:去除fileType字段

## 2025/6/18 [0.4.4-beta4&0.4.4-beta5&0.4.4-beta6&0.4.4-beta7]

- [feature] 更新:pdf图片添加fileType字段

## 2025/6/16 [0.4.4-beta3]

- [feature] 更新:更新剪裁相关model,原图存储在modal中y

## 2025/6/12 [0.4.4-beta2]

- [feature] 更新:添加剪裁相关modal

## 2025/6/6 [0.4.4-beta1]

- [feature] 更新:添加pdf相关字段

## 2025/5/28 [0.4.3-beta7]&&[0.4.3-beta8]

- [feature] 修复:C1段落打印问题修复

## 2025/5/20 [0.4.3-beta6]

- [feature] 修复:段落对齐方式等已知问题

## 2025/5/20 [0.4.3-beta5]

- [feature] 修复:excel解析错误问题处理

## 2025/5/20 [0.4.3-beta4]

- [feature] 修复:竖向段落预览打印相关方法

## 2025/5/19 [0.4.3-beta3]

- [feature] 修复:兼容移动端模板号为1.7.0.3相关数据

## 2025/5/15 [0.4.2-beta9]&& [0.4.3-beta1]&& [0.4.3-beta2]

- [feature] 修复:兼容NetalTextStyle解析报错问题
- [feature] 修复:段落预览打印相关方法

## 2025/5/14 [0.4.2-beta8]

- [feature] 修复:段落预览打印相关方法

## 2025/5/9 [0.4.2-beta5] && [0.4.2-beta7]

- [feature] 修复:values及elements取值问题

## 2025/5/9 [0.4.2-beta5] && [0.4.2-beta6]

- [feature] 修复:C1生成缩略图生成问题

## 2025/5/7 [0.4.2-beta4]

- [feature] 调整:C1多序号排列组合

## 2025/5/6 [0.4.2-beta3]

- [feature] 修复:C1生成缩略图生成问题

## 2025/4/29 [0.4.2-beta2]

- [feature] 修复:C1生成缩略图相关问题修复

## 2025/4/28 [0.4.2-beta1]

- [feature] 修复:C1生成缩略图是添加上下边距,margin处理

## 2025/4/27 [0.4.1-beta9]

- [feature] 修复:C1和移动端数据兼容性问题

## 2025/4/21 [0.4.1-beta7 && 0.4.1-beta8]

- [feature] 修复:C1应用当前缩略图上传

## 2025/4/14 [0.4.1-beta6]

- [feature] 修复:toJson方法完善

## 2025/4/8 [0.4.1-beta5]

- [feature] 兼容:数据源解析调整

## 2025/4/8 [0.4.1-beta1 && 0.4.1-beta2 && 0.4.1-beta3 && 0.4.1-beta4]

- [feature] 兼容:tubeFileSetting添加字段

## 2025/3/27 [0.4.0]

## 2025/5/13 [0.3.4 && 0.3.5]

- [chore] 修复:兼容NetalTextStyle解析报错问题

## 2025/3/21 [0.3.3]

- [feature] 兼容:`lineMode`字段

## 2025/3/19 [0.3.1]&[0.3.2]

- [chore] 调整:支持`lineMode`字段
- [chore] 调整:使用`RatioUtils`处理倍率转换

## 2025/3/18 [0.3.0]

- [chore] 更新:依赖

## 2025/3/14 [0.2.10]

- [chore] 更新:依赖

## 2025/2/28 [0.2.8]&[0.2.9]

- [fix] 优化:实时时间timeFormat为传情况修复
- [fix] 修复:生成预览图图片路径异常

## 2025/2/27 [0.2.7]

- [fix] 优化:实时时间星期格式排序

## 2025/2/24 [0.2.6]

- [fix] 优化:实时时间新增星期相关格式

## 2025/2/20 [0.2.4]&[0.2.5]

- [fix] 优化:模板保存版本号
- [feature] 适配:移动端兼容`rotate`字段为`int`类型

## 2025/2/17 [0.2.3]

- [fix] 修复:保存时版本号判断

## 2025/2/12 [0.2.2]

- [fix] 修复:表格旋转角度未传递给图像库
- [fix] 修复:`TemplateData.toJson`方法移除`isEdited`字段无需向服务端传递该字段

## 2025/2/8 [0.2.1]

- [feature] 新增:模板编辑状态解析

## 2025/2/5 [0.2.0]

- [chore] 更新:Dart最低依赖
- [feature] 优化:部分类型声明

## 2024/12/6 [0.1.50]

- [feature] 修复:`TemplateDataSource`toJson问题

## 2024/12/5 [0.1.49]

- [feature] 修复:多语言修改

## 2024/11/21 [0.1.46]&[0.1.47]

- [feature] 优化:`TemplateDataSource`类型声明
- [feature] 修复:`TemplateDataSource`解析问题

## 2024/11/5 [0.1.45]

- [fix] 修复:多语言适配

## 2024/11/4 [0.1.44]

- [fix] 修复:补全profile相关copyWith方法

## 2024/9/29 [0.1.43]

- [fix] 修复:元素解析时,id重复未重新生成元素Id

## 2024/9/29 [0.1.41]

- [fix] 修复:rebuildValue时清理元素绑定相关数据

## 2024/9/13 [0.1.40]

- [fix] 修复:字段类型兼容处理

## 2024/9/11 [0.1.39]

- [feature] 优化:镜像元素解析

## 2024/9/10 [0.1.38]

- [feature] 优化:打印列名判断列名是否为空

## 2024/9/6 [0.1.36]&[0.1.37]

- [fix] 修复:时间格式相关bug
- [feature] 优化:模板生成

## 2024/9/5 [0.1.34]&[0.1.35]

- [feature] 优化:图片元素解析与生成逻辑

## 2024/9/4 [0.1.31]&[0.1.32]&[0.1.33]

- [fix] 完善:时间格式相关bug
- [feature] 优化:模板解析逻辑

## 2024/9/2 [0.1.30]

- [feature] 完善:本地模板解析逻辑

## 2024/9/2[0.1.28]&[0.1.29]

- [fix] 修复:时间元素解析逻辑

## 2024/8/28[0.1.27]

- [fix] 修复:老PC端数据源解析问题

## 2024/8/26[0.1.25]&[0.1.26]

- [feature] 修复:数据源解析问题，保存报错
- [feature] 修复:老数据源结构解析问题

## 2024/8/26[0.1.23]&[0.1.24]

- [feature] 完善：时间元素
- [fix] 修复:数据源解析问题

## 2024/8/21[0.1.22]

- [feature] 适配：新老数据兼容

## 2024/8/20[0.1.18]&[0.1.19]&[0.1.20]&[0.1.21]

- [feature] 适配：新老数据兼容

## 2024/8/16[0.1.16]&[0.1.17]

- [feature] 优化：元素大小渲染
- [feature] 移除：元素大小渲染实现

## 2024/8/14[0.1.15]

- [feature] 优化：元素大小渲染

## 2024/8/12[0.1.14]

- [feature] 适配：文本模式
- [fix] 修复:文本模式相关bug

## 2024/7/26[0.1.13]

- [feature] 适配：文本模式

## 2024/7/26[0.1.12]

- [feature] 修复：数据源表头作为数据

## 2024/7/25[0.1.11]

- [feature] 修复：边框及背景图bug

## 2024/7/19[0.1.10]

- [feature] 修复：异步图像生成

## 2024/7/18[0.1.8]&[0.1.9]

- [feature] 修复：移动端保存模板vip标识处理问题
- [feature] 修复：处理解析处理慢

## 2024/7/15[0.1.7]

- [feature] 修复：生成打印JSON时间更新逻辑变更

## 2024/7/12[0.1.6]

- [feature] 修复：日期格式校准

## 2024/7/11[0.1.5]

- [feature] 修复：字距支持-5到5。日期格式校准

## 2024/7/10[0.1.4]

- [feature] 修复:lineWidth值校准\日期格式校准

## 2024/7/9[0.1.3]

- [feature] 修复:日期hasVipRes逻辑bug修复
-

## 2024/7/8[0.1.2]

- [feature] 修复:解析库hasVipRes逻辑

## 2024/7/5[0.1.0]&[0.1.1]

- [chore] 更新:依赖
- [feature] 修复:表格线宽0.2时相关bug

## 2024/7/5[0.0.71]

- [feature] 修复:VIP相关资源解析问题

## 2024/6/27[0.0.70]

- [feature] 调整:生成JSON是不忽略本地图片

## 2024/6/26[0.0.69]

- [feature] 调整:生成预览图使用PNG编码

## 2024/6/25[0.0.68]

- [feature] 兼容:解析库本地图片校验是否本地存在

## 2024/6/24[0.0.66]]&[0.0.67]

- [feature] 兼容:解析库不处理range为空情况
- [feature] 兼容:序列号

## 2024/6/24[0.0.64]&[0.0.65]

- [feature] 兼容:时间处理
- [feature] 兼容:背景图旋转问题
- [feature] 兼容:处理profile.extrain.createTime || updateTime字段缺少的问题

## 2024/6/20[0.0.62]&[0.0.63]

- [feature] 兼容:图片及图标解析转化问题
- [feature] 兼容:单元格垂直居中数值

## 2024/6/19[0.0.60]&[0.0.61]

- [feature] 优化:图片解析本地路径
- [feature] 优化:图片解析本地路径\仅取本地

## 2024/6/18[0.0.59]

- [feature] 优化:序列号兼容移动端

## 2024/6/17[0.0.56]&[0.0.57]&[0.0.58]

- [feature] 优化:数据源解析range为空时默认选中全部
- [feature] 移除:zIndex实现,无效字段;
- [feature] 优化:图片解析
- [fix] 修复:序列号填充长度

## 2024/6/14[0.0.55]

### [feature] 优化:解析问题修复\图片解析存储到本地

## 2024/6/13[0.0.54]

### [feature] 优化:模板兼容解析与保存

## 2024/6/13[0.0.51]&[0.0.52]&[0.0.53]

### [feature] 优化:模板兼容解析与保存

## 2024/6/12[0.0.47]&[0.0.48]&[0.0.49]&[0.0.50]

### [feature] 优化:模板兼容解析与保存

## 2024/6/11[0.0.45]&[0.0.46]

### [feature] 优化:模板兼容解析与保存

## 2024/6/6[0.0.43]&[0.0.44]

### [feature] 调整:字体fontCode与fontFamily的使用;

## 2024/5/30[0.0.42]

### [feature] 优化:日期解析兼容修复

## 2024/5/29[0.0.41]

### [feature] 优化:模板解析兼容,兼容移动端老PC端

## 2024/5/29[0.0.40]

### [feature] 优化:模板解析兼容,兼容移动端老PC端

## 2024/5/28[0.0.38]&[0.0.39]

### [feature] 优化:模板解析兼容,兼容移动端老PC端

## 2024/5/27[0.0.37]

### [feature] 优化:模板解析兼容,兼容移动端老PC端

## 2024/5/24[0.0.35]&[0.0.36]

### [feature] 优化:模板解析兼容

## 2024/5/23[0.0.34]

### [feature] 优化:模板解析兼容

## 2024/5/23[0.0.33]

### [feature] 优化:模板解析兼容

## 2024/5/21[0.0.30]&[0.0.31]&[0.0.32]

### [feature] 优化:模板解析兼容

## 2024/5/17[0.0.26]

## 2024/5/21[0.0.27]&[0.0.28]&[0.0.29]

### [feature] 优化:模板解析兼容

## 2024/5/17[0.0.26]

### [feature] 新增:出纸方向;结构裁剪;

## 2024/5/15[0.0.25]

### [feature] 新增:本地图片解析相关逻辑;

## 2024/5/14[0.0.24]

### [feature] 更新:图像库依赖;新增:打印偏移;

## 2024/5/9[0.0.21]&[0.0.22]&[0.0.23]

### [feature] 优化:模板解析兼容

## 2024/5/9[0.0.20]

### [feature] 优化:模板解析兼容

## 2024/5/8[0.0.18]&[0.0.19]

### [feature] 优化:模板解析兼容

## 2024/5/7 [0.0.13]&[0.0.14]&[0.0.15]&[0.0.16]&[0.0.17]

### [feature] 优化:模板解析兼容

## 2024/5/6[0.0.10]&[0.0.11]&[0.0.12]

### [feature] 优化:模板解析兼容

## 2024/4/28[0.0.9]

### [feature] 优化:模板解析兼容

## 2024/4/26[0.0.8]

### [feature] 优化:模板解析兼容

## 2024/4/24[0.0.7]

### [feature] 优化:模板解析兼容

## 2024/4/23[0.0.5]&[0.0.6]

### [feature] 优化:模板解析兼容

## 2024/4/22[0.0.4]

### [feature] 优化:模板解析兼容

## 2024/4/22[0.0.3]

### [feature] 新增:从JSON Map解析

## 2024/4/18[0.0.2]

### [chore] 跟新:依赖

## 2024/4/17[0.0.1]

### [init] 新增:模板库