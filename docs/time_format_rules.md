# 时间格式单位添加规则

## 核心规则

**只有纯 `H` 或 `HH` 格式才会添加时间单位，其他格式不添加。**

## 格式说明

### ✅ 会添加时间单位的格式

| 格式 | 说明 | 示例输入 | 英文输出 | 中文输出 |
|------|------|----------|----------|----------|
| `H` | 单位小时（1-24） | `14` | `14` | `14时` |
| `HH` | 两位小时（01-24） | `14` | `14` | `14时` |

### ❌ 不会添加时间单位的格式

| 格式 | 说明 | 示例输入 | 输出 |
|------|------|----------|------|
| `HH:mm` | 小时:分钟 | `14:30` | `14:30` |
| `HH:mm:ss` | 小时:分钟:秒 | `14:30:45` | `14:30:45` |
| `mm:ss` | 分钟:秒 | `30:45` | `30:45` |
| `H:mm` | 小时:分钟 | `14:30` | `14:30` |

## 实现逻辑

### addHourUnitToTimeFormat 方法

```dart
static String addHourUnitToTimeFormat(String timeFormatVal, String? timeFormat, {final langCode = 'zh'}) {
  // 关键判断：只有纯 H 或 HH 格式才添加单位
  bool isADDUnit = timeFormat == 'H' || timeFormat == 'HH';
  if (!isADDUnit) return timeFormatVal;
  
  final hourUnit = getHourUnit(langCode);
  if (hourUnit.isEmpty) return timeFormatVal;
  
  return timeFormatVal + hourUnit;
}
```

### 24小时制处理

```dart
if (militaryTime) {
  String timeFormatVal = DateFormat(timeFormat, isZh ? 'zh_CN' : null).format(dateTime);
  
  // 只为CJK语言添加时间单位
  if (['zh', 'zh_Hant', 'ko', 'ja'].contains(lang)) {
    timeFormatVal = addHourUnitToTimeFormat(timeFormatVal, timeFormat, langCode: lang);
  }
  
  return timeFormatVal;
}
```

### 12小时制处理

```dart
if (beforeAmPm) {
  timeFormatVal = timeFormatVal.split(' ').reversed.join('');
  
  // 只对 H 或 HH 格式添加时间单位
  if (timeFormat == 'HH' || timeFormat == 'H') {
    // 提取小时数字并添加单位
    final hourMatch = RegExp(r'^(\d{1,2})(?=:|$)').firstMatch(timeFormatVal);
    if (hourMatch != null) {
      final hourValue = hourMatch.group(1)!;
      final hourWithUnit = addHourUnitToTimeFormat(hourValue, timeFormat, langCode: lang);
      timeFormatVal = timeFormatVal.replaceFirst(RegExp(r'^\d{1,2}'), hourWithUnit);
    }
  }
}
```

## 测试用例

### 24小时制测试

| 输入格式 | 时间值 | 英文环境输出 | 中文环境输出 |
|----------|--------|-------------|-------------|
| `H` | 14:30:45 | `14` | `14时` |
| `HH` | 14:30:45 | `14` | `14时` |
| `HH:mm` | 14:30:45 | `14:30` | `14:30` |
| `HH:mm:ss` | 14:30:45 | `14:30:45` | `14:30:45` |

### 12小时制测试

| 输入格式 | 时间值 | 英文环境输出 | 中文环境输出 |
|----------|--------|-------------|-------------|
| `H` | 14:30:45 | `2 PM` | `下午2时` |
| `HH` | 14:30:45 | `02 PM` | `下午02时` |
| `HH:mm` | 14:30:45 | `02:30 PM` | `下午02:30` |
| `HH:mm:ss` | 14:30:45 | `02:30:45 PM` | `下午02:30:45` |

### 带日期的测试

| 日期格式 | 时间格式 | 输出示例 |
|----------|----------|----------|
| `yyyy-MM-dd` | `H` | `2025-07-15 14时` (中文) / `2025-07-15 14` (英文) |
| `yyyy-MM-dd` | `HH:mm` | `2025-07-15 14:30` |
| `MMMM yyyy` | `H` | `July2025 14时` (中文) / `July2025 14` (英文) |
| `MMM.yyyy` | `HH:mm` | `Jul.2025 14:30` |

## 语言环境差异

### CJK语言环境 (zh, zh_Hant, ko, ja)

- **H/HH 格式**: 添加对应的时间单位
  - 简体中文: `时`
  - 繁体中文: `時`
  - 韩语: `시`
  - 日语: `時`

### 其他语言环境 (en, etc.)

- **H/HH 格式**: 不添加时间单位，保持纯数字
- **其他格式**: 与CJK语言环境相同，不添加单位

## 设计原理

### ElementTimeFormat.H 的含义

- `ElementTimeFormat.H` 专门指**小时刻度**显示
- 用于显示纯小时信息，如时钟刻度、小时选择器等
- 在CJK语言中需要添加时间单位以符合本地化习惯

### 为什么 HH:mm 不添加单位

- `HH:mm` 格式已经通过冒号明确表示了时间结构
- 国际通用格式，不需要额外的语言特定单位
- 保持与国际标准的一致性

## 总结

这个设计确保了：

1. ✅ **精确性**: 只有需要的格式才添加单位
2. ✅ **国际化**: 不同语言环境有适当的处理
3. ✅ **一致性**: 24小时制和12小时制遵循相同规则
4. ✅ **兼容性**: 不影响现有的时间格式显示
